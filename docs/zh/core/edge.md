# 边（Edge）

边（Edge）是执行链（Chain）中连接两个节点（Node）的“线条”，用于定义节点之间的执行顺序和数据流动路径。通过边，执行链能够按照预设逻辑从一个节点传递到另一个节点。

**其主要功能**:
- **控制执行顺序**: 定义节点之间的执行流程。
- **传递数据**: 将上游节点的输出作为下游节点的输入。
- **条件控制**: 配置执行条件（Condition），决定是否触发下游节点的执行。


## 边的基本结构

一条边通常包含以下信息：

1. **起点节点（Source）**: 当前边的起始节点 ID。
2. **终点节点（Target）**: 当前边的目标节点 ID。
3. **执行条件（Condition）**: 可选配置，用于决定是否触发目标节点的执行。

```json
{
  "source": "node_a_id",
  "target": "node_b_id",
  "condition": "inputs.status === 'success'"
}
```
