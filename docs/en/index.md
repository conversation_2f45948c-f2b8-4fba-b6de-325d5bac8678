---
# https://vitepress.dev/reference/default-theme-home-page
layout: home

hero:
  name: "Tinyflow"
  text: "An AI workflow orchestration framework"
  tagline: Support integration with VUE, React and native JS projects
  actions:
    - theme: brand
      text: Quick Start
      link: /en/quick-start
    - theme: alt
      text: Documentation
      link: /en/what-is-tinyflow

features:
  - title: Lightweight, yet powerful
    details: Tinyflow does not pursue big and comprehensive, but focuses on solving the core problem - making AI workflows seamlessly integrated with existing businesses. Its code base is very lightweight and has a low learning cost, but its functions are not ambiguous at all. Whether it is simple task scheduling or complex multimodal reasoning, Tinyflow can handle it easily.
  - title: High degree of freedom
    details: Tinyflow is based on Web Component. It will not force you to change your existing technology stack, nor will it dictate your development habits. Whether you are a front-end developer or a back-end engineer, you can find the most suitable entry point in your field. I hope this "non-disturbance" design philosophy can make developers feel comfortable.
  - title: Designed for the future
    details: Tinyflow's architecture is modular, which means it can be expanded as your business needs change. For example, today you only need a simple text generation process, tomorrow you may need to add speech recognition or image processing. Tinyflow's plugin mechanism allows you to add new features at any time without having to start over.
---

