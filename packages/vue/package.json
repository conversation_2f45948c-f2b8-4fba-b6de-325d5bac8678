{"name": "@tinyflow-ai/vue", "version": "1.1.1", "type": "module", "keywords": ["tinyflow", "ai", "ai flow", "agent flow", "agents flow"], "types": "./dist/index.d.ts", "main": "./dist/index.js", "module": "./dist/index.js", "browser": "./dist/index.umd.js", "license": "LGPL-3.0-or-later", "files": ["dist", "LICENSE", "README.md"], "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "check": "svelte-check --tsconfig ./tsconfig.app.json && tsc -p tsconfig.node.json", "lint": "eslint ."}, "dependencies": {"@tinyflow-ai/ui": "workspace:*"}, "peerDependencies": {"vue": "^3.5.13"}, "devDependencies": {"@tinyflow-ai/eslint-config": "workspace:*", "@types/node": "^22.13.4", "@vitejs/plugin-vue": "^5.2.1", "autoprefixer": "^10.4.21", "less": "^4.2.2", "typescript": "^5.6.2", "vite": "^7.0.4", "vite-plugin-dts": "^4.5.3", "vue": "^3.5.13"}, "imports": {"#*": ["./src/*.ts", "./src/*.vue"]}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js"}, "./dist/index.css": {"import": "./dist/index.css", "require": "./dist/index.css"}}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "repository": {"type": "git", "url": "https://github.com/tinyflow-ai/tinyflow"}, "bugs": {"url": "https://github.com/tinyflow-ai/tinyflow/issues"}, "homepage": "https://github.com/tinyflow-ai/tinyflow#readme"}