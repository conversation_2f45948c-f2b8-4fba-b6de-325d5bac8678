(function(Kn,Lt){typeof exports=="object"&&typeof module<"u"?Lt(exports,require("vue")):typeof define=="function"&&define.amd?define(["exports","vue"],Lt):(Kn=typeof globalThis<"u"?globalThis:Kn||self,Lt(Kn.Tinyflow={},Kn.Vue))})(this,function(Kn,Lt){"use strict";var pa=document.createElement("style");pa.textContent=`.svelte-flow{direction:ltr;--xy-edge-stroke-default: #b1b1b7;--xy-edge-stroke-width-default: 1;--xy-edge-stroke-selected-default: #555;--xy-connectionline-stroke-default: #b1b1b7;--xy-connectionline-stroke-width-default: 1;--xy-attribution-background-color-default: rgba(255, 255, 255, .5);--xy-minimap-background-color-default: #fff;--xy-minimap-mask-background-color-default: rgba(240, 240, 240, .6);--xy-minimap-mask-stroke-color-default: transparent;--xy-minimap-mask-stroke-width-default: 1;--xy-minimap-node-background-color-default: #e2e2e2;--xy-minimap-node-stroke-color-default: transparent;--xy-minimap-node-stroke-width-default: 2;--xy-background-color-default: transparent;--xy-background-pattern-dots-color-default: #91919a;--xy-background-pattern-lines-color-default: #eee;--xy-background-pattern-cross-color-default: #e2e2e2;background-color:var(--xy-background-color, var(--xy-background-color-default));--xy-node-color-default: inherit;--xy-node-border-default: 1px solid #1a192b;--xy-node-background-color-default: #fff;--xy-node-group-background-color-default: rgba(240, 240, 240, .25);--xy-node-boxshadow-hover-default: 0 1px 4px 1px rgba(0, 0, 0, .08);--xy-node-boxshadow-selected-default: 0 0 0 .5px #1a192b;--xy-node-border-radius-default: 3px;--xy-handle-background-color-default: #1a192b;--xy-handle-border-color-default: #fff;--xy-selection-background-color-default: rgba(0, 89, 220, .08);--xy-selection-border-default: 1px dotted rgba(0, 89, 220, .8);--xy-controls-button-background-color-default: #fefefe;--xy-controls-button-background-color-hover-default: #f4f4f4;--xy-controls-button-color-default: inherit;--xy-controls-button-color-hover-default: inherit;--xy-controls-button-border-color-default: #eee;--xy-controls-box-shadow-default: 0 0 2px 1px rgba(0, 0, 0, .08);--xy-edge-label-background-color-default: #ffffff;--xy-edge-label-color-default: inherit;--xy-resize-background-color-default: #3367d9}.svelte-flow.dark{--xy-edge-stroke-default: #3e3e3e;--xy-edge-stroke-width-default: 1;--xy-edge-stroke-selected-default: #727272;--xy-connectionline-stroke-default: #b1b1b7;--xy-connectionline-stroke-width-default: 1;--xy-attribution-background-color-default: rgba(150, 150, 150, .25);--xy-minimap-background-color-default: #141414;--xy-minimap-mask-background-color-default: rgba(60, 60, 60, .6);--xy-minimap-mask-stroke-color-default: transparent;--xy-minimap-mask-stroke-width-default: 1;--xy-minimap-node-background-color-default: #2b2b2b;--xy-minimap-node-stroke-color-default: transparent;--xy-minimap-node-stroke-width-default: 2;--xy-background-color-default: #141414;--xy-background-pattern-dots-color-default: #777;--xy-background-pattern-lines-color-default: #777;--xy-background-pattern-cross-color-default: #777;--xy-node-color-default: #f8f8f8;--xy-node-border-default: 1px solid #3c3c3c;--xy-node-background-color-default: #1e1e1e;--xy-node-group-background-color-default: rgba(240, 240, 240, .25);--xy-node-boxshadow-hover-default: 0 1px 4px 1px rgba(255, 255, 255, .08);--xy-node-boxshadow-selected-default: 0 0 0 .5px #999;--xy-handle-background-color-default: #bebebe;--xy-handle-border-color-default: #1e1e1e;--xy-selection-background-color-default: rgba(200, 200, 220, .08);--xy-selection-border-default: 1px dotted rgba(200, 200, 220, .8);--xy-controls-button-background-color-default: #2b2b2b;--xy-controls-button-background-color-hover-default: #3e3e3e;--xy-controls-button-color-default: #f8f8f8;--xy-controls-button-color-hover-default: #fff;--xy-controls-button-border-color-default: #5b5b5b;--xy-controls-box-shadow-default: 0 0 2px 1px rgba(0, 0, 0, .08);--xy-edge-label-background-color-default: #141414;--xy-edge-label-color-default: #f8f8f8}.svelte-flow__background{background-color:var(--xy-background-color-props, var(--xy-background-color, var(--xy-background-color-default)));pointer-events:none;z-index:-1}.svelte-flow__container{position:absolute;width:100%;height:100%;top:0;left:0}.svelte-flow__pane{z-index:1}.svelte-flow__pane.draggable{cursor:grab}.svelte-flow__pane.dragging{cursor:grabbing}.svelte-flow__pane.selection{cursor:pointer}.svelte-flow__viewport{transform-origin:0 0;z-index:2;pointer-events:none}.svelte-flow__renderer{z-index:4}.svelte-flow__selection{z-index:6}.svelte-flow__nodesselection-rect:focus,.svelte-flow__nodesselection-rect:focus-visible{outline:none}.svelte-flow__edge-path{stroke:var(--xy-edge-stroke, var(--xy-edge-stroke-default));stroke-width:var(--xy-edge-stroke-width, var(--xy-edge-stroke-width-default));fill:none}.svelte-flow__connection-path{stroke:var(--xy-connectionline-stroke, var(--xy-connectionline-stroke-default));stroke-width:var(--xy-connectionline-stroke-width, var(--xy-connectionline-stroke-width-default));fill:none}.svelte-flow .svelte-flow__edges{position:absolute}.svelte-flow .svelte-flow__edges svg{overflow:visible;position:absolute;pointer-events:none}.svelte-flow__edge{pointer-events:visibleStroke}.svelte-flow__edge.selectable{cursor:pointer}.svelte-flow__edge.animated path{stroke-dasharray:5;animation:dashdraw .5s linear infinite}.svelte-flow__edge.animated path.svelte-flow__edge-interaction{stroke-dasharray:none;animation:none}.svelte-flow__edge.inactive{pointer-events:none}.svelte-flow__edge.selected,.svelte-flow__edge:focus,.svelte-flow__edge:focus-visible{outline:none}.svelte-flow__edge.selected .svelte-flow__edge-path,.svelte-flow__edge.selectable:focus .svelte-flow__edge-path,.svelte-flow__edge.selectable:focus-visible .svelte-flow__edge-path{stroke:var(--xy-edge-stroke-selected, var(--xy-edge-stroke-selected-default))}.svelte-flow__edge-textwrapper{pointer-events:all}.svelte-flow__edge .svelte-flow__edge-text{pointer-events:none;-webkit-user-select:none;-moz-user-select:none;user-select:none}.svelte-flow__arrowhead polyline{stroke:var(--xy-edge-stroke, var(--xy-edge-stroke-default))}.svelte-flow__arrowhead polyline.arrowclosed{fill:var(--xy-edge-stroke, var(--xy-edge-stroke-default))}.svelte-flow__connection{pointer-events:none}.svelte-flow__connection .animated{stroke-dasharray:5;animation:dashdraw .5s linear infinite}svg.svelte-flow__connectionline{z-index:1001;overflow:visible;position:absolute}.svelte-flow__nodes{pointer-events:none;transform-origin:0 0}.svelte-flow__node{position:absolute;-webkit-user-select:none;-moz-user-select:none;user-select:none;pointer-events:all;transform-origin:0 0;box-sizing:border-box;cursor:default}.svelte-flow__node.selectable{cursor:pointer}.svelte-flow__node.draggable{cursor:grab;pointer-events:all}.svelte-flow__node.draggable.dragging{cursor:grabbing}.svelte-flow__nodesselection{z-index:3;transform-origin:left top;pointer-events:none}.svelte-flow__nodesselection-rect{position:absolute;pointer-events:all;cursor:grab}.svelte-flow__handle{position:absolute;pointer-events:none;min-width:5px;min-height:5px;width:6px;height:6px;background-color:var(--xy-handle-background-color, var(--xy-handle-background-color-default));border:1px solid var(--xy-handle-border-color, var(--xy-handle-border-color-default));border-radius:100%}.svelte-flow__handle.connectingfrom{pointer-events:all}.svelte-flow__handle.connectionindicator{pointer-events:all;cursor:crosshair}.svelte-flow__handle-bottom{top:auto;left:50%;bottom:0;transform:translate(-50%,50%)}.svelte-flow__handle-top{top:0;left:50%;transform:translate(-50%,-50%)}.svelte-flow__handle-left{top:50%;left:0;transform:translate(-50%,-50%)}.svelte-flow__handle-right{top:50%;right:0;transform:translate(50%,-50%)}.svelte-flow__edgeupdater{cursor:move;pointer-events:all}.svelte-flow__pane.selection .svelte-flow__panel{pointer-events:none}.svelte-flow__panel{position:absolute;z-index:5;margin:15px}.svelte-flow__panel.top{top:0}.svelte-flow__panel.bottom{bottom:0}.svelte-flow__panel.top.center,.svelte-flow__panel.bottom.center{left:50%;transform:translate(-15px) translate(-50%)}.svelte-flow__panel.left{left:0}.svelte-flow__panel.right{right:0}.svelte-flow__panel.left.center,.svelte-flow__panel.right.center{top:50%;transform:translateY(-15px) translateY(-50%)}.svelte-flow__attribution{font-size:10px;background:var(--xy-attribution-background-color, var(--xy-attribution-background-color-default));padding:2px 3px;margin:0}.svelte-flow__attribution a{text-decoration:none;color:#999}@keyframes dashdraw{0%{stroke-dashoffset:10}}.svelte-flow__edgelabel-renderer{position:absolute;width:100%;height:100%;pointer-events:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;left:0;top:0}.svelte-flow__viewport-portal{position:absolute;width:100%;height:100%;left:0;top:0;-webkit-user-select:none;-moz-user-select:none;user-select:none}.svelte-flow__minimap{background:var( --xy-minimap-background-color-props, var(--xy-minimap-background-color, var(--xy-minimap-background-color-default)) )}.svelte-flow__minimap-svg{display:block}.svelte-flow__minimap-mask{fill:var( --xy-minimap-mask-background-color-props, var(--xy-minimap-mask-background-color, var(--xy-minimap-mask-background-color-default)) );stroke:var( --xy-minimap-mask-stroke-color-props, var(--xy-minimap-mask-stroke-color, var(--xy-minimap-mask-stroke-color-default)) );stroke-width:var( --xy-minimap-mask-stroke-width-props, var(--xy-minimap-mask-stroke-width, var(--xy-minimap-mask-stroke-width-default)) )}.svelte-flow__minimap-node{fill:var( --xy-minimap-node-background-color-props, var(--xy-minimap-node-background-color, var(--xy-minimap-node-background-color-default)) );stroke:var( --xy-minimap-node-stroke-color-props, var(--xy-minimap-node-stroke-color, var(--xy-minimap-node-stroke-color-default)) );stroke-width:var( --xy-minimap-node-stroke-width-props, var(--xy-minimap-node-stroke-width, var(--xy-minimap-node-stroke-width-default)) )}.svelte-flow__background-pattern.dots{fill:var( --xy-background-pattern-color-props, var(--xy-background-pattern-color, var(--xy-background-pattern-dots-color-default)) )}.svelte-flow__background-pattern.lines{stroke:var( --xy-background-pattern-color-props, var(--xy-background-pattern-color, var(--xy-background-pattern-lines-color-default)) )}.svelte-flow__background-pattern.cross{stroke:var( --xy-background-pattern-color-props, var(--xy-background-pattern-color, var(--xy-background-pattern-cross-color-default)) )}.svelte-flow__controls{display:flex;flex-direction:column;box-shadow:var(--xy-controls-box-shadow, var(--xy-controls-box-shadow-default))}.svelte-flow__controls.horizontal{flex-direction:row}.svelte-flow__controls-button{display:flex;justify-content:center;align-items:center;height:26px;width:26px;padding:4px;border:none;background:var(--xy-controls-button-background-color, var(--xy-controls-button-background-color-default));border-bottom:1px solid var( --xy-controls-button-border-color-props, var(--xy-controls-button-border-color, var(--xy-controls-button-border-color-default)) );color:var( --xy-controls-button-color-props, var(--xy-controls-button-color, var(--xy-controls-button-color-default)) );cursor:pointer;-webkit-user-select:none;-moz-user-select:none;user-select:none}.svelte-flow__controls-button svg{width:100%;max-width:12px;max-height:12px;fill:currentColor}.svelte-flow__edge.updating .svelte-flow__edge-path{stroke:#777}.svelte-flow__edge-text{font-size:10px}.svelte-flow__node.selectable:focus,.svelte-flow__node.selectable:focus-visible{outline:none}.svelte-flow__node-input,.svelte-flow__node-default,.svelte-flow__node-output,.svelte-flow__node-group{padding:10px;border-radius:var(--xy-node-border-radius, var(--xy-node-border-radius-default));width:150px;font-size:12px;color:var(--xy-node-color, var(--xy-node-color-default));text-align:center;border:var(--xy-node-border, var(--xy-node-border-default));background-color:var(--xy-node-background-color, var(--xy-node-background-color-default))}.svelte-flow__node-input.selectable:hover,.svelte-flow__node-default.selectable:hover,.svelte-flow__node-output.selectable:hover,.svelte-flow__node-group.selectable:hover{box-shadow:var(--xy-node-boxshadow-hover, var(--xy-node-boxshadow-hover-default))}.svelte-flow__node-input.selectable.selected,.svelte-flow__node-input.selectable:focus,.svelte-flow__node-input.selectable:focus-visible,.svelte-flow__node-default.selectable.selected,.svelte-flow__node-default.selectable:focus,.svelte-flow__node-default.selectable:focus-visible,.svelte-flow__node-output.selectable.selected,.svelte-flow__node-output.selectable:focus,.svelte-flow__node-output.selectable:focus-visible,.svelte-flow__node-group.selectable.selected,.svelte-flow__node-group.selectable:focus,.svelte-flow__node-group.selectable:focus-visible{box-shadow:var(--xy-node-boxshadow-selected, var(--xy-node-boxshadow-selected-default))}.svelte-flow__node-group{background-color:var(--xy-node-group-background-color, var(--xy-node-group-background-color-default))}.svelte-flow__nodesselection-rect,.svelte-flow__selection{background:var(--xy-selection-background-color, var(--xy-selection-background-color-default));border:var(--xy-selection-border, var(--xy-selection-border-default))}.svelte-flow__nodesselection-rect:focus,.svelte-flow__nodesselection-rect:focus-visible,.svelte-flow__selection:focus,.svelte-flow__selection:focus-visible{outline:none}.svelte-flow__controls-button:hover{background:var( --xy-controls-button-background-color-hover-props, var(--xy-controls-button-background-color-hover, var(--xy-controls-button-background-color-hover-default)) );color:var( --xy-controls-button-color-hover-props, var(--xy-controls-button-color-hover, var(--xy-controls-button-color-hover-default)) )}.svelte-flow__controls-button:disabled{pointer-events:none}.svelte-flow__controls-button:disabled svg{fill-opacity:.4}.svelte-flow__controls-button:last-child{border-bottom:none}.svelte-flow__controls.horizontal .svelte-flow__controls-button{border-bottom:none;border-right:1px solid var( --xy-controls-button-border-color-props, var(--xy-controls-button-border-color, var(--xy-controls-button-border-color-default)) )}.svelte-flow__controls.horizontal .svelte-flow__controls-button:last-child{border-right:none}.svelte-flow__resize-control{position:absolute}.svelte-flow__resize-control.left,.svelte-flow__resize-control.right{cursor:ew-resize}.svelte-flow__resize-control.top,.svelte-flow__resize-control.bottom{cursor:ns-resize}.svelte-flow__resize-control.top.left,.svelte-flow__resize-control.bottom.right{cursor:nwse-resize}.svelte-flow__resize-control.bottom.left,.svelte-flow__resize-control.top.right{cursor:nesw-resize}.svelte-flow__resize-control.handle{width:5px;height:5px;border:1px solid #fff;border-radius:1px;background-color:var(--xy-resize-background-color, var(--xy-resize-background-color-default));translate:-50% -50%}.svelte-flow__resize-control.handle.left{left:0;top:50%}.svelte-flow__resize-control.handle.right{left:100%;top:50%}.svelte-flow__resize-control.handle.top{left:50%;top:0}.svelte-flow__resize-control.handle.bottom{left:50%;top:100%}.svelte-flow__resize-control.handle.top.left,.svelte-flow__resize-control.handle.bottom.left{left:0}.svelte-flow__resize-control.handle.top.right,.svelte-flow__resize-control.handle.bottom.right{left:100%}.svelte-flow__resize-control.line{border-color:var(--xy-resize-background-color, var(--xy-resize-background-color-default));border-width:0;border-style:solid}.svelte-flow__resize-control.line.left,.svelte-flow__resize-control.line.right{width:1px;transform:translate(-50%);top:0;height:100%}.svelte-flow__resize-control.line.left{left:0;border-left-width:1px}.svelte-flow__resize-control.line.right{left:100%;border-right-width:1px}.svelte-flow__resize-control.line.top,.svelte-flow__resize-control.line.bottom{height:1px;transform:translateY(-50%);left:0;width:100%}.svelte-flow__resize-control.line.top{top:0;border-top-width:1px}.svelte-flow__resize-control.line.bottom{border-bottom-width:1px;top:100%}.svelte-flow__edge-label{text-align:center;position:absolute;padding:2px;font-size:10px;color:var(--xy-edge-label-color, var(--xy-edge-label-color-default));background:var(--xy-edge-label-background-color, var(--xy-edge-label-background-color-default))}.svelte-flow__container{-webkit-user-select:none;-moz-user-select:none;user-select:none}:root,:root .tf-theme-light{--tf-primary-color: #2563EB;--xy-node-boxshadow-selected: 0 0 0 1px var(--tf-primary-color);--xy-handle-background-color: var(--tf-primary-color)}.tf-btn{display:flex;align-items:center;justify-content:center;gap:2px;background:#fff;border:1px solid #ccc;cursor:pointer;border-radius:5px;padding:5px;margin:0;height:fit-content;width:fit-content}.tf-btn svg{fill:currentColor;width:16px;height:16px}.tf-btn:hover{border:1px solid var(--tf-primary-color)}.tf-btn.tf-btn-primary{background:var(--tf-primary-color);color:#fff}.tf-btn.tf-btn-primary:hover{border:1px solid #ccc;background:#3a6fe3}.tf-input,.tf-textarea{display:flex;border-radius:5px;border:1px solid #ccc;padding:5px 8px;box-sizing:border-box;resize:vertical;outline:none;line-height:normal}.tf-input::placeholder,.tf-textarea::placeholder{color:#ccc;font-size:12px}.tf-input:focus,.tf-textarea:focus{border-color:var(--tf-primary-color);box-shadow:0 0 5px #51cbee33}.tf-input[disabled],.tf-textarea[disabled]{background-color:#f0f0f0;cursor:not-allowed;color:#aaa}.tf-select-input{display:flex;border:1px solid #ccc;padding:3px 10px;border-radius:5px;font-size:14px;justify-content:space-between;align-items:center;cursor:pointer;background:#fff;height:27px}.tf-select-input:focus{border-color:var(--tf-primary-color);box-shadow:0 0 5px #51cbee33}.tf-select-input-value{height:21px;min-width:10px;font-size:12px;display:flex;align-items:center}.tf-select-input-arrow{display:block;width:16px;height:16px;color:#666}.tf-select-input-placeholder{color:#ccc}.tf-select-content{display:flex;flex-direction:column;background:#fff;margin-top:5px;border:1px solid #ccc;border-radius:5px;padding:5px;width:max-content;min-width:100%;z-index:999;box-sizing:border-box}.tf-select-content-item{display:flex;align-items:center;padding:5px 10px;border:none;background:#fff;border-radius:5px;cursor:pointer;line-height:100%;gap:2px}.tf-select-content-item span{width:16px;display:flex}.tf-select-content-item svg{width:16px;height:16px;margin:auto}.tf-select-content-item:hover{background:#f0f0f0}.tf-select-content-children{padding-left:14px}.tf-checkbox{width:14px;height:14px}.tf-tabs{display:flex;align-items:center;justify-content:center;gap:5px;padding:5px;border-radius:5px;border:none;background:#f4f4f5}.tf-tabs .tf-tabs-item{flex-grow:1;padding:5px 10px;cursor:pointer;border-radius:5px;display:flex;align-items:center;justify-content:center;font-size:14px;color:#808088}.tf-tabs .tf-tabs-item.active{background:#fff;color:#333;font-weight:500;box-shadow:0 0 5px #00000026}h3.tf-heading{font-weight:700;font-size:14px;margin-top:2px;margin-bottom:3px;color:#333}.tf-collapse{border:none;border-radius:5px}.tf-collapse-item-title{display:flex;align-items:center;cursor:pointer;font-size:14px}.tf-collapse-item-title-icon{display:flex;width:26px;height:26px;color:#2563eb;background:#cedafb;border-radius:5px;padding:3px;justify-content:center;align-items:center;margin-right:10px}.tf-collapse-item-title-icon svg{width:22px;height:22px;color:#3474ff}.tf-collapse-item-title-arrow{display:block;width:16px;height:16px;margin-left:auto}.tf-collapse-item-description{font-size:12px;margin:10px 0;color:#999}.svelte-flow__nodes .svelte-flow__node{border:3px solid transparent;border-radius:5px;box-sizing:border-box}.svelte-flow__nodes .svelte-flow__node .svelte-flow__handle{width:16px;height:16px;background:transparent;display:flex;justify-content:center;align-items:center;border:none}.svelte-flow__nodes .svelte-flow__node .svelte-flow__handle:after{content:" ";background:#2563eb;width:8px;height:8px;border-radius:100%;transition:width .1s,height .1s}.svelte-flow__nodes .svelte-flow__node .svelte-flow__handle:hover:after{width:16px;height:16px}.svelte-flow__nodes .svelte-flow__node div.loop_handle_wrapper:after{content:"循环体";background:#2563eb;width:100px;height:20px;border-radius:0;display:flex;color:#fff;justify-content:center;align-items:center}.svelte-flow__nodes .svelte-flow__node div.loop_handle_wrapper:hover:after{width:100px;height:20px}.svelte-flow__nodes .svelte-flow__node:after{content:" ";position:absolute;border-radius:5px;top:-2px;left:-2px;border:1px solid #ccc;height:calc(100% + 2px);width:calc(100% + 2px)}.svelte-flow__nodes .svelte-flow__node:hover{border:3px solid #bacaef7d}.svelte-flow__nodes .svelte-flow__node.selectable.selected{border:3px solid #bacaef7d;box-shadow:var(--xy-node-boxshadow-selected)}.svelte-flow__nodes .svelte-flow__node:hover:after{display:none}.svelte-flow__nodes .svelte-flow__node.selectable.selected:after{display:none}.tf-node-wrapper{border-radius:5px;min-width:300px;background:#fff}.tf-node-wrapper-title{height:30px;background:#eff1f5;color:#bcbcbc;font-size:12px;display:flex;align-items:center;padding-left:5px;border-bottom:1px solid #ccc;font-weight:300;letter-spacing:1px}.tf-node-wrapper-body{padding:10px}.svelte-flow__attribution a{display:none}.tf-toolbar{z-index:200;position:absolute;top:10px;left:10px;display:flex;gap:5px;transition:transform .5s ease,opacity .5s ease;transform:translate(calc(-100% + 20px))}.tf-toolbar.show{transform:translate(0)}.tf-toolbar-container{background:#fff;border:1px solid #eee;border-radius:5px;box-shadow:0 0 5px #0000001a;padding:10px;width:fit-content}.tf-toolbar-container-header{display:flex}.tf-toolbar-container-body{display:flex;margin-top:20px}.tf-toolbar-container-body .tf-toolbar-container-base,.tf-toolbar-container-body .tf-toolbar-container-tools{display:flex;flex-direction:column;gap:4px;flex-grow:1}.tf-toolbar-container-body .tf-toolbar-container-base .tf-btn,.tf-toolbar-container-body .tf-toolbar-container-tools .tf-btn{border:none;width:100%;justify-content:flex-start;height:40px;gap:10px;cursor:grabbing;border-radius:5px}.tf-toolbar-container-body .tf-toolbar-container-base .tf-btn svg,.tf-toolbar-container-body .tf-toolbar-container-tools .tf-btn svg{width:20px;height:20px;fill:#2563eb}.tf-toolbar-container-body .tf-toolbar-container-base .tf-btn:hover,.tf-toolbar-container-body .tf-toolbar-container-tools .tf-btn:hover{background:#f1f1f1}.tinyflow-logo:after{content:"Tinyflow.ai";font-size:145px;display:flex;align-items:center;justify-content:center;width:100%;height:100%;font-weight:800;color:#03153b54;text-shadow:1px 3px 6px #cedafb,0 0 0 #000,1px 3px 6px #fff;opacity:.1}
/*$vite$:1*/`,document.head.appendChild(pa);const tf="5";typeof window<"u"&&((window.__svelte??={}).v??=new Set).add(tf);const Di=1,Hi=2,ga=4,nf=8,rf=16,of=1,sf=2,ha=4,af=8,lf=16,va=1,uf=2,ma="[",Mi="[!",Ti="]",Xn={},Je=Symbol(),cf="http://www.w3.org/1999/xhtml",df="http://www.w3.org/2000/svg",ff="@attach",pf=!1;var Ar=Array.isArray,gf=Array.prototype.indexOf,Vi=Array.from,Po=Object.keys,dr=Object.defineProperty,hn=Object.getOwnPropertyDescriptor,ya=Object.getOwnPropertyDescriptors,wa=Object.prototype,hf=Array.prototype,zo=Object.getPrototypeOf,ba=Object.isExtensible;function Ir(e){return typeof e=="function"}const tt=()=>{};function vf(e){return e()}function Lo(e){for(var t=0;t<e.length;t++)e[t]()}function mf(){var e,t,n=new Promise((r,o)=>{e=r,t=o});return{promise:n,resolve:e,reject:t}}function gt(e,t,n=!1){return e===void 0?n?t():t:e}function qr(e,t){if(Array.isArray(e))return e;if(t===void 0||!(Symbol.iterator in e))return Array.from(e);const n=[];for(const r of e)if(n.push(r),n.length===t)break;return n}const $t=2,Oi=4,Do=8,fr=16,vn=32,zn=64,xa=128,Dt=256,Ho=512,ct=1024,Ht=2048,Ln=4096,It=8192,Yn=16384,Ai=32768,pr=65536,_a=1<<17,yf=1<<18,gr=1<<19,ka=1<<20,Ii=1<<21,qi=1<<22,jn=1<<23,Ut=Symbol("$state"),Zi=Symbol("legacy props"),wf=Symbol(""),Ri=new class extends Error{name="StaleReactionError";message="The reaction that called `getAbortSignal()` was re-run or destroyed"},bf=1,Bi=3,hr=8;function xf(){throw new Error("https://svelte.dev/e/await_outside_boundary")}function Ki(e){throw new Error("https://svelte.dev/e/lifecycle_outside_component")}function _f(){throw new Error("https://svelte.dev/e/async_derived_orphan")}function kf(e){throw new Error("https://svelte.dev/e/effect_in_teardown")}function $f(){throw new Error("https://svelte.dev/e/effect_in_unowned_derived")}function Cf(e){throw new Error("https://svelte.dev/e/effect_orphan")}function Sf(){throw new Error("https://svelte.dev/e/effect_update_depth_exceeded")}function Ef(){throw new Error("https://svelte.dev/e/hydration_failed")}function Nf(e){throw new Error("https://svelte.dev/e/props_invalid_value")}function Pf(){throw new Error("https://svelte.dev/e/state_descriptors_fixed")}function zf(){throw new Error("https://svelte.dev/e/state_prototype_fixed")}function Lf(){throw new Error("https://svelte.dev/e/state_unsafe_mutation")}function Zr(e){console.warn("https://svelte.dev/e/hydration_mismatch")}function Df(){console.warn("https://svelte.dev/e/select_multiple_invalid_value")}let be=!1;function Ct(e){be=e}let Ee;function dt(e){if(e===null)throw Zr(),Xn;return Ee=e}function mn(){return dt(Jt(Ee))}function A(e){if(be){if(Jt(Ee)!==null)throw Zr(),Xn;Ee=e}}function he(e=1){if(be){for(var t=e,n=Ee;t--;)n=Jt(n);Ee=n}}function Xi(){for(var e=0,t=Ee;;){if(t.nodeType===hr){var n=t.data;if(n===Ti){if(e===0)return t;e-=1}else(n===ma||n===Mi)&&(e+=1)}var r=Jt(t);t.remove(),t=r}}function $a(e){if(!e||e.nodeType!==hr)throw Zr(),Xn;return e.data}function Ca(e){return e===this.v}function Sa(e,t){return e!=e?t==t:e!==t||e!==null&&typeof e=="object"||typeof e=="function"}function Ea(e){return!Sa(e,this.v)}let vr=!1,Hf=!1;function Mf(){vr=!0}const Tf=[];function Na(e,t=!1,n=!1){return Mo(e,new Map,"",Tf,null,n)}function Mo(e,t,n,r,o=null,i=!1){if(typeof e=="object"&&e!==null){var s=t.get(e);if(s!==void 0)return s;if(e instanceof Map)return new Map(e);if(e instanceof Set)return new Set(e);if(Ar(e)){var a=Array(e.length);t.set(e,a),o!==null&&t.set(o,a);for(var l=0;l<e.length;l+=1){var u=e[l];l in e&&(a[l]=Mo(u,t,n,r,null,i))}return a}if(zo(e)===wa){a={},t.set(e,a),o!==null&&t.set(o,a);for(var d in e)a[d]=Mo(e[d],t,n,r,null,i);return a}if(e instanceof Date)return structuredClone(e);if(typeof e.toJSON=="function"&&!i)return Mo(e.toJSON(),t,n,r,e)}if(e instanceof EventTarget)return e;try{return structuredClone(e)}catch{return e}}let Ze=null;function To(e){Ze=e}function Dn(e){return Pa().get(e)}function mr(e,t){return Pa().set(e,t),t}function ue(e,t=!1,n){Ze={p:Ze,c:null,e:null,s:e,x:null,l:vr&&!t?{s:null,u:null,$:[]}:null}}function ce(e){var t=Ze,n=t.e;if(n!==null){t.e=null;for(var r of n)Ja(r)}return e!==void 0&&(t.x=e),Ze=t.p,e??{}}function Rr(){return!vr||Ze!==null&&Ze.l===null}function Pa(e){return Ze===null&&Ki(),Ze.c??=new Map(Vf(Ze)||void 0)}function Vf(e){let t=e.p;for(;t!==null;){const n=t.c;if(n!==null)return n;t=t.p}return null}const Of=new WeakMap;function Af(e){var t=$e;if(t===null)return ze.f|=jn,e;if((t.f&Ai)===0){if((t.f&xa)===0)throw!t.parent&&e instanceof Error&&za(e),e;t.b.error(e)}else Yi(e,t)}function Yi(e,t){for(;t!==null;){if((t.f&xa)!==0)try{t.b.error(e);return}catch(n){e=n}t=t.parent}throw e instanceof Error&&za(e),e}function za(e){const t=Of.get(e);t&&(dr(e,"message",{value:t.message}),dr(e,"stack",{value:t.stack}))}const If=typeof requestIdleCallback>"u"?e=>setTimeout(e,1):requestIdleCallback;let Br=[],Kr=[];function La(){var e=Br;Br=[],Lo(e)}function Da(){var e=Kr;Kr=[],Lo(e)}function Xr(e){Br.length===0&&queueMicrotask(La),Br.push(e)}function qf(e){Kr.length===0&&If(Da),Kr.push(e)}function Zf(){Br.length>0&&La(),Kr.length>0&&Da()}function Rf(e){let t=0,n=Un(0),r;return()=>{Qf()&&(c(n),Fr(()=>(t===0&&(r=nt(()=>e(()=>Wr(n)))),t+=1,()=>{Xr(()=>{t-=1,t===0&&(r?.(),r=void 0,Wr(n))})})))}}function Bf(){for(var e=$e.b;e!==null&&!e.has_pending_snippet();)e=e.parent;return e===null&&xf(),e}function Yr(e){var t=$t|Ht,n=ze!==null&&(ze.f&$t)!==0?ze:null;return $e===null||n!==null&&(n.f&Dt)!==0?t|=Dt:$e.f|=gr,{ctx:Ze,deps:null,effects:null,equals:Ca,f:t,fn:e,reactions:null,rv:0,v:Je,wv:0,parent:n??$e,ac:null}}function Kf(e,t){let n=$e;n===null&&_f();var r=n.b,o=void 0,i=Un(Je),s=null,a=!ze;return tp(()=>{try{var l=e();s&&Promise.resolve(l).catch(()=>{})}catch(g){l=Promise.reject(g)}var u=()=>l;o=s?.then(u,u)??Promise.resolve(l),s=o;var d=Qe,p=r.pending;a&&(r.update_pending_count(1),p||d.increment());const f=(g,h=void 0)=>{s=null,p||d.activate(),h?h!==Ri&&(i.f|=jn,jr(i,h)):((i.f&jn)!==0&&(i.f^=jn),jr(i,g)),a&&(r.update_pending_count(-1),p||d.decrement()),Va()};if(o.then(f,g=>f(null,g||"unknown")),d)return()=>{queueMicrotask(()=>d.neuter())}}),new Promise(l=>{function u(d){function p(){d===o?l(i):u(o)}d.then(p,p)}u(o)})}function z(e){const t=Yr(e);return ll(t),t}function ji(e){const t=Yr(e);return t.equals=Ea,t}function Ha(e){var t=e.effects;if(t!==null){e.effects=null;for(var n=0;n<t.length;n+=1)ht(t[n])}}function Xf(e){for(var t=e.parent;t!==null;){if((t.f&$t)===0)return t;t=t.parent}return null}function Wi(e){var t,n=$e;en(Xf(e));try{Ha(e),t=pl(e)}finally{en(n)}return t}function Ma(e){var t=Wi(e);if(e.equals(t)||(e.v=t,e.wv=dl()),!Qn){var n=(Mn||(e.f&Dt)!==0)&&e.deps!==null?Ln:ct;yt(e,n)}}function Ta(e,t,n){const r=Rr()?Yr:ji;if(t.length===0){n(e.map(r));return}var o=Qe,i=$e,s=Yf(),a=Bf();Promise.all(t.map(l=>Kf(l))).then(l=>{o?.activate(),s();try{n([...e.map(r),...l])}catch(u){(i.f&Yn)===0&&Yi(u,i)}o?.deactivate(),Va()}).catch(l=>{a.error(l)})}function Yf(){var e=$e,t=ze,n=Ze,r=Qe;return function(){en(e),Qt(t),To(n),r?.activate()}}function Va(){en(null),Qt(null),To(null)}const Fi=new Set;let Qe=null,Oa=new Set,Vo=[];function Aa(){const e=Vo.shift();Vo.length>0&&queueMicrotask(Aa),e()}let Wn=[],Oo=null,Gi=!1,Ao=!1;class Fn{current=new Map;#t=new Map;#e=new Set;#n=0;#o=null;#c=!1;#i=[];#a=[];#s=[];#r=[];#l=[];#d=[];#f=[];skipped_effects=new Set;process(t){Wn=[];for(const o of t)this.#g(o);if(this.#i.length===0&&this.#n===0){this.#p();var n=this.#s,r=this.#r;this.#s=[],this.#r=[],this.#l=[],Qe=null,qa(n),qa(r),Qe===null?Qe=this:Fi.delete(this),this.#o?.resolve()}else this.#u(this.#s),this.#u(this.#r),this.#u(this.#l);for(const o of this.#i)xr(o);for(const o of this.#a)xr(o);this.#i=[],this.#a=[]}#g(t){t.f^=ct;for(var n=t.first;n!==null;){var r=n.f,o=(r&(vn|zn))!==0,i=o&&(r&ct)!==0,s=i||(r&It)!==0||this.skipped_effects.has(n);if(!s&&n.fn!==null){if(o)n.f^=ct;else if((r&Oi)!==0)this.#r.push(n);else if((r&ct)===0)if((r&qi)!==0){var a=n.b?.pending?this.#a:this.#i;a.push(n)}else qo(n)&&((n.f&fr)!==0&&this.#l.push(n),xr(n));var l=n.first;if(l!==null){n=l;continue}}var u=n.parent;for(n=n.next;n===null&&u!==null;)n=u.next,u=u.parent}}#u(t){for(const n of t)((n.f&Ht)!==0?this.#d:this.#f).push(n),yt(n,ct);t.length=0}capture(t,n){this.#t.has(t)||this.#t.set(t,n),this.current.set(t,t.v)}activate(){Qe=this}deactivate(){Qe=null;for(const t of Oa)if(Oa.delete(t),t(),Qe!==null)break}neuter(){this.#c=!0}flush(){Wn.length>0?Ia():this.#p(),Qe===this&&(this.#n===0&&Fi.delete(this),this.deactivate())}#p(){if(!this.#c)for(const t of this.#e)t();this.#e.clear()}increment(){this.#n+=1}decrement(){if(this.#n-=1,this.#n===0){for(const t of this.#d)yt(t,Ht),yr(t);for(const t of this.#f)yt(t,Ln),yr(t);this.#s=[],this.#r=[],this.flush()}else this.deactivate()}add_callback(t){this.#e.add(t)}settled(){return(this.#o??=mf()).promise}static ensure(){if(Qe===null){const t=Qe=new Fn;Fi.add(Qe),Ao||Fn.enqueue(()=>{Qe===t&&t.flush()})}return Qe}static enqueue(t){Vo.length===0&&queueMicrotask(Aa),Vo.unshift(t)}}function m(e){var t=Ao;Ao=!0;try{for(var n;;){if(Zf(),Wn.length===0&&(Qe?.flush(),Wn.length===0))return Oo=null,n;Ia()}}finally{Ao=t}}function Ia(){var e=br;Gi=!0;try{var t=0;for(sl(!0);Wn.length>0;){var n=Fn.ensure();if(t++>1e3){var r,o;jf()}n.process(Wn),Hn.clear()}}finally{Gi=!1,sl(e),Oo=null}}function jf(){try{Sf()}catch(e){Yi(e,Oo)}}let Gn=null;function qa(e){var t=e.length;if(t!==0){for(var n=0;n<t;){var r=e[n++];if((r.f&(Yn|It))===0&&qo(r)&&(Gn=[],xr(r),r.deps===null&&r.first===null&&r.nodes_start===null&&(r.teardown===null&&r.ac===null?rl(r):r.fn=null),Gn?.length>0)){Hn.clear();for(const o of Gn)xr(o);Gn=[]}}Gn=null}}function yr(e){for(var t=Oo=e;t.parent!==null;){t=t.parent;var n=t.f;if(Gi&&t===$e&&(n&fr)!==0)return;if((n&(zn|vn))!==0){if((n&ct)===0)return;t.f^=ct}}Wn.push(t)}const Hn=new Map;function Un(e,t){var n={f:0,v:e,reactions:null,equals:Ca,rv:0,wv:0};return n}function Se(e,t){const n=Un(e);return ll(n),n}function Za(e,t=!1,n=!0){const r=Un(e);return t||(r.equals=Ea),vr&&n&&Ze!==null&&Ze.l!==null&&(Ze.l.s??=[]).push(r),r}function j(e,t,n=!1){ze!==null&&(!Bt||(ze.f&_a)!==0)&&Rr()&&(ze.f&($t|fr|qi|_a))!==0&&!yn?.includes(e)&&Lf();let r=n?Mt(t):t;return jr(e,r)}function jr(e,t){if(!e.equals(t)){var n=e.v;Qn?Hn.set(e,t):Hn.set(e,n),e.v=t;var r=Fn.ensure();r.capture(e,n),(e.f&$t)!==0&&((e.f&Ht)!==0&&Wi(e),yt(e,(e.f&Dt)===0?ct:Ln)),e.wv=dl(),Ba(e,Ht),Rr()&&$e!==null&&($e.f&ct)!==0&&($e.f&(vn|zn))===0&&(Tt===null?rp([e]):Tt.push(e))}return t}function Ra(e,t=1){var n=c(e),r=t===1?n++:n--;return j(e,n),r}function Wr(e){j(e,e.v+1)}function Ba(e,t){var n=e.reactions;if(n!==null)for(var r=Rr(),o=n.length,i=0;i<o;i++){var s=n[i],a=s.f;if(!(!r&&s===$e)){var l=(a&Ht)===0;l&&yt(s,t),(a&$t)!==0?Ba(s,Ln):l&&((a&fr)!==0&&Gn!==null&&Gn.push(s),yr(s))}}}function Mt(e){if(typeof e!="object"||e===null||Ut in e)return e;const t=zo(e);if(t!==wa&&t!==hf)return e;var n=new Map,r=Ar(e),o=Se(0),i=er,s=a=>{if(er===i)return a();var l=ze,u=er;Qt(null),cl(i);var d=a();return Qt(l),cl(u),d};return r&&n.set("length",Se(e.length)),new Proxy(e,{defineProperty(a,l,u){(!("value"in u)||u.configurable===!1||u.enumerable===!1||u.writable===!1)&&Pf();var d=n.get(l);return d===void 0?d=s(()=>{var p=Se(u.value);return n.set(l,p),p}):j(d,u.value,!0),!0},deleteProperty(a,l){var u=n.get(l);if(u===void 0){if(l in a){const d=s(()=>Se(Je));n.set(l,d),Wr(o)}}else j(u,Je),Wr(o);return!0},get(a,l,u){if(l===Ut)return e;var d=n.get(l),p=l in a;if(d===void 0&&(!p||hn(a,l)?.writable)&&(d=s(()=>{var g=Mt(p?a[l]:Je),h=Se(g);return h}),n.set(l,d)),d!==void 0){var f=c(d);return f===Je?void 0:f}return Reflect.get(a,l,u)},getOwnPropertyDescriptor(a,l){var u=Reflect.getOwnPropertyDescriptor(a,l);if(u&&"value"in u){var d=n.get(l);d&&(u.value=c(d))}else if(u===void 0){var p=n.get(l),f=p?.v;if(p!==void 0&&f!==Je)return{enumerable:!0,configurable:!0,value:f,writable:!0}}return u},has(a,l){if(l===Ut)return!0;var u=n.get(l),d=u!==void 0&&u.v!==Je||Reflect.has(a,l);if(u!==void 0||$e!==null&&(!d||hn(a,l)?.writable)){u===void 0&&(u=s(()=>{var f=d?Mt(a[l]):Je,g=Se(f);return g}),n.set(l,u));var p=c(u);if(p===Je)return!1}return d},set(a,l,u,d){var p=n.get(l),f=l in a;if(r&&l==="length")for(var g=u;g<p.v;g+=1){var h=n.get(g+"");h!==void 0?j(h,Je):g in a&&(h=s(()=>Se(Je)),n.set(g+"",h))}if(p===void 0)(!f||hn(a,l)?.writable)&&(p=s(()=>Se(void 0)),j(p,Mt(u)),n.set(l,p));else{f=p.v!==Je;var v=s(()=>Mt(u));j(p,v)}var w=Reflect.getOwnPropertyDescriptor(a,l);if(w?.set&&w.set.call(d,u),!f){if(r&&typeof l=="string"){var b=n.get("length"),k=Number(l);Number.isInteger(k)&&k>=b.v&&j(b,k+1)}Wr(o)}return!0},ownKeys(a){c(o);var l=Reflect.ownKeys(a).filter(p=>{var f=n.get(p);return f===void 0||f.v!==Je});for(var[u,d]of n)d.v!==Je&&!(u in a)&&l.push(u);return l},setPrototypeOf(){zf()}})}function Ka(e){try{if(e!==null&&typeof e=="object"&&Ut in e)return e[Ut]}catch{}return e}function Wf(e,t){return Object.is(Ka(e),Ka(t))}var mt,Xa,Ya,ja;function Ui(){if(mt===void 0){mt=window,Xa=/Firefox/.test(navigator.userAgent);var e=Element.prototype,t=Node.prototype,n=Text.prototype;Ya=hn(t,"firstChild").get,ja=hn(t,"nextSibling").get,ba(e)&&(e.__click=void 0,e.__className=void 0,e.__attributes=null,e.__style=void 0,e.__e=void 0),ba(n)&&(n.__t=void 0)}}function qt(e=""){return document.createTextNode(e)}function Ge(e){return Ya.call(e)}function Jt(e){return ja.call(e)}function I(e,t){if(!be)return Ge(e);var n=Ge(Ee);if(n===null)n=Ee.appendChild(qt());else if(t&&n.nodeType!==Bi){var r=qt();return n?.before(r),dt(r),r}return dt(n),n}function oe(e,t){if(!be){var n=Ge(e);return n instanceof Comment&&n.data===""?Jt(n):n}return Ee}function V(e,t=1,n=!1){let r=be?Ee:e;for(var o;t--;)o=r,r=Jt(r);if(!be)return r;if(n&&r?.nodeType!==Bi){var i=qt();return r===null?o?.after(i):r.before(i),dt(i),i}return dt(r),r}function Ji(e){e.textContent=""}function Wa(){return!1}function Ff(e,t){if(t){const n=document.body;e.autofocus=!0,Xr(()=>{document.activeElement===n&&e.focus()})}}function Gf(e){be&&Ge(e)!==null&&Ji(e)}let Fa=!1;function Uf(){Fa||(Fa=!0,document.addEventListener("reset",e=>{Promise.resolve().then(()=>{if(!e.defaultPrevented)for(const t of e.target.elements)t.__on_r?.()})},{capture:!0}))}function Qi(e){var t=ze,n=$e;Qt(null),en(null);try{return e()}finally{Qt(t),en(n)}}function Ga(e){$e===null&&ze===null&&Cf(),ze!==null&&(ze.f&Dt)!==0&&$e===null&&$f(),Qn&&kf()}function Jf(e,t){var n=t.last;n===null?t.last=t.first=e:(n.next=e,e.prev=n,t.last=e)}function Zt(e,t,n,r=!0){var o=$e;o!==null&&(o.f&It)!==0&&(e|=It);var i={ctx:Ze,deps:null,nodes_start:null,nodes_end:null,f:e|Ht,first:null,fn:t,last:null,next:null,parent:o,b:o&&o.b,prev:null,teardown:null,transitions:null,wv:0,ac:null};if(n)try{xr(i),i.f|=Ai}catch(l){throw ht(i),l}else t!==null&&yr(i);if(r){var s=i;if(n&&s.deps===null&&s.teardown===null&&s.nodes_start===null&&s.first===s.last&&(s.f&gr)===0&&(s=s.first),s!==null&&(s.parent=o,o!==null&&Jf(s,o),ze!==null&&(ze.f&$t)!==0&&(e&zn)===0)){var a=ze;(a.effects??=[]).push(s)}}return i}function Qf(){return ze!==null&&!Bt}function Ua(e){const t=Zt(Do,null,!1);return yt(t,ct),t.teardown=e,t}function Ke(e){Ga();var t=$e.f,n=!ze&&(t&vn)!==0&&(t&Ai)===0;if(n){var r=Ze;(r.e??=[]).push(e)}else return Ja(e)}function Ja(e){return Zt(Oi|ka,e,!1)}function Qa(e){return Ga(),Zt(Do|ka,e,!0)}function es(e){Fn.ensure();const t=Zt(zn|gr,e,!0);return()=>{ht(t)}}function ep(e){Fn.ensure();const t=Zt(zn|gr,e,!0);return(n={})=>new Promise(r=>{n.outro?Gr(t,()=>{ht(t),r(void 0)}):(ht(t),r(void 0))})}function wr(e){return Zt(Oi,e,!1)}function tp(e){return Zt(qi|gr,e,!0)}function Fr(e,t=0){return Zt(Do|t,e,!0)}function xe(e,t=[],n=[]){Ta(t,n,r=>{Zt(Do,()=>e(...r.map(c)),!0)})}function Jn(e,t=0){var n=Zt(fr|t,e,!0);return n}function Rt(e,t=!0){return Zt(vn|gr,e,!0,t)}function el(e){var t=e.teardown;if(t!==null){const n=Qn,r=ze;al(!0),Qt(null);try{t.call(null)}finally{al(n),Qt(r)}}}function tl(e,t=!1){var n=e.first;for(e.first=e.last=null;n!==null;){const o=n.ac;o!==null&&Qi(()=>{o.abort(Ri)});var r=n.next;(n.f&zn)!==0?n.parent=null:ht(n,t),n=r}}function np(e){for(var t=e.first;t!==null;){var n=t.next;(t.f&vn)===0&&ht(t),t=n}}function ht(e,t=!0){var n=!1;(t||(e.f&yf)!==0)&&e.nodes_start!==null&&e.nodes_end!==null&&(nl(e.nodes_start,e.nodes_end),n=!0),tl(e,t&&!n),Zo(e,0),yt(e,Yn);var r=e.transitions;if(r!==null)for(const i of r)i.stop();el(e);var o=e.parent;o!==null&&o.first!==null&&rl(e),e.next=e.prev=e.teardown=e.ctx=e.deps=e.fn=e.nodes_start=e.nodes_end=e.ac=null}function nl(e,t){for(;e!==null;){var n=e===t?null:Jt(e);e.remove(),e=n}}function rl(e){var t=e.parent,n=e.prev,r=e.next;n!==null&&(n.next=r),r!==null&&(r.prev=n),t!==null&&(t.first===e&&(t.first=r),t.last===e&&(t.last=n))}function Gr(e,t){var n=[];ts(e,n,!0),ol(n,()=>{ht(e),t&&t()})}function ol(e,t){var n=e.length;if(n>0){var r=()=>--n||t();for(var o of e)o.out(r)}else t()}function ts(e,t,n){if((e.f&It)===0){if(e.f^=It,e.transitions!==null)for(const s of e.transitions)(s.is_global||n)&&t.push(s);for(var r=e.first;r!==null;){var o=r.next,i=(r.f&pr)!==0||(r.f&vn)!==0;ts(r,t,i?n:!1),r=o}}}function Io(e){il(e,!0)}function il(e,t){if((e.f&It)!==0){e.f^=It,(e.f&ct)===0&&(yt(e,Ht),yr(e));for(var n=e.first;n!==null;){var r=n.next,o=(n.f&pr)!==0||(n.f&vn)!==0;il(n,o?t:!1),n=r}if(e.transitions!==null)for(const i of e.transitions)(i.is_global||t)&&i.in()}}let br=!1;function sl(e){br=e}let Qn=!1;function al(e){Qn=e}let ze=null,Bt=!1;function Qt(e){ze=e}let $e=null;function en(e){$e=e}let yn=null;function ll(e){ze!==null&&(yn===null?yn=[e]:yn.push(e))}let vt=null,St=0,Tt=null;function rp(e){Tt=e}let ul=1,Ur=0,er=Ur;function cl(e){er=e}let Mn=!1;function dl(){return++ul}function qo(e){var t=e.f;if((t&Ht)!==0)return!0;if((t&Ln)!==0){var n=e.deps,r=(t&Dt)!==0;if(n!==null){var o,i,s=(t&Ho)!==0,a=r&&$e!==null&&!Mn,l=n.length;if((s||a)&&($e===null||($e.f&Yn)===0)){var u=e,d=u.parent;for(o=0;o<l;o++)i=n[o],(s||!i?.reactions?.includes(u))&&(i.reactions??=[]).push(u);s&&(u.f^=Ho),a&&d!==null&&(d.f&Dt)===0&&(u.f^=Dt)}for(o=0;o<l;o++)if(i=n[o],qo(i)&&Ma(i),i.wv>e.wv)return!0}(!r||$e!==null&&!Mn)&&yt(e,ct)}return!1}function fl(e,t,n=!0){var r=e.reactions;if(r!==null&&!yn?.includes(e))for(var o=0;o<r.length;o++){var i=r[o];(i.f&$t)!==0?fl(i,t,!1):t===i&&(n?yt(i,Ht):(i.f&ct)!==0&&yt(i,Ln),yr(i))}}function pl(e){var t=vt,n=St,r=Tt,o=ze,i=Mn,s=yn,a=Ze,l=Bt,u=er,d=e.f;vt=null,St=0,Tt=null,Mn=(d&Dt)!==0&&(Bt||!br||ze===null),ze=(d&(vn|zn))===0?e:null,yn=null,To(e.ctx),Bt=!1,er=++Ur,e.ac!==null&&(Qi(()=>{e.ac.abort(Ri)}),e.ac=null);try{e.f|=Ii;var p=e.fn,f=p(),g=e.deps;if(vt!==null){var h;if(Zo(e,St),g!==null&&St>0)for(g.length=St+vt.length,h=0;h<vt.length;h++)g[St+h]=vt[h];else e.deps=g=vt;if(!Mn||(d&$t)!==0&&e.reactions!==null)for(h=St;h<g.length;h++)(g[h].reactions??=[]).push(e)}else g!==null&&St<g.length&&(Zo(e,St),g.length=St);if(Rr()&&Tt!==null&&!Bt&&g!==null&&(e.f&($t|Ln|Ht))===0)for(h=0;h<Tt.length;h++)fl(Tt[h],e);return o!==null&&o!==e&&(Ur++,Tt!==null&&(r===null?r=Tt:r.push(...Tt))),(e.f&jn)!==0&&(e.f^=jn),f}catch(v){return Af(v)}finally{e.f^=Ii,vt=t,St=n,Tt=r,ze=o,Mn=i,yn=s,To(a),Bt=l,er=u}}function op(e,t){let n=t.reactions;if(n!==null){var r=gf.call(n,e);if(r!==-1){var o=n.length-1;o===0?n=t.reactions=null:(n[r]=n[o],n.pop())}}n===null&&(t.f&$t)!==0&&(vt===null||!vt.includes(t))&&(yt(t,Ln),(t.f&(Dt|Ho))===0&&(t.f^=Ho),Ha(t),Zo(t,0))}function Zo(e,t){var n=e.deps;if(n!==null)for(var r=t;r<n.length;r++)op(e,n[r])}function xr(e){var t=e.f;if((t&Yn)===0){yt(e,ct);var n=$e,r=br;$e=e,br=!0;try{(t&fr)!==0?np(e):tl(e),el(e);var o=pl(e);e.teardown=typeof o=="function"?o:null,e.wv=ul;var i;pf&&Hf&&(e.f&Ht)!==0&&e.deps}finally{br=r,$e=n}}}function c(e){var t=e.f,n=(t&$t)!==0;if(ze!==null&&!Bt){var r=$e!==null&&($e.f&Yn)!==0;if(!r&&!yn?.includes(e)){var o=ze.deps;if((ze.f&Ii)!==0)e.rv<Ur&&(e.rv=Ur,vt===null&&o!==null&&o[St]===e?St++:vt===null?vt=[e]:(!Mn||!vt.includes(e))&&vt.push(e));else{(ze.deps??=[]).push(e);var i=e.reactions;i===null?e.reactions=[ze]:i.includes(ze)||i.push(ze)}}}else if(n&&e.deps===null&&e.effects===null){var s=e,a=s.parent;a!==null&&(a.f&Dt)===0&&(s.f^=Dt)}if(Qn){if(Hn.has(e))return Hn.get(e);if(n){s=e;var l=s.v;return((s.f&ct)===0&&s.reactions!==null||gl(s))&&(l=Wi(s)),Hn.set(s,l),l}}else n&&(s=e,qo(s)&&Ma(s));if((e.f&jn)!==0)throw e.v;return e.v}function gl(e){if(e.v===Je)return!0;if(e.deps===null)return!1;for(const t of e.deps)if(Hn.has(t)||(t.f&$t)!==0&&gl(t))return!0;return!1}function nt(e){var t=Bt;try{return Bt=!0,e()}finally{Bt=t}}const ip=-7169;function yt(e,t){e.f=e.f&ip|t}function sp(e,t){var n={};for(var r in e)t.includes(r)||(n[r]=e[r]);return n}function ns(e){if(!(typeof e!="object"||!e||e instanceof EventTarget)){if(Ut in e)rs(e);else if(!Array.isArray(e))for(let t in e){const n=e[t];typeof n=="object"&&n&&Ut in n&&rs(n)}}}function rs(e,t=new Set){if(typeof e=="object"&&e!==null&&!(e instanceof EventTarget)&&!t.has(e)){t.add(e),e instanceof Date&&e.getTime();for(let r in e)try{rs(e[r],t)}catch{}const n=zo(e);if(n!==Object.prototype&&n!==Array.prototype&&n!==Map.prototype&&n!==Set.prototype&&n!==Date.prototype){const r=ya(n);for(let o in r){const i=r[o].get;if(i)try{i.call(e)}catch{}}}}}const hl=new Set,os=new Set;function is(e,t,n,r={}){function o(i){if(r.capture||Jr.call(t,i),!i.cancelBubble)return Qi(()=>n?.call(this,i))}return e.startsWith("pointer")||e.startsWith("touch")||e==="wheel"?Xr(()=>{t.addEventListener(e,o,r)}):t.addEventListener(e,o,r),o}function ss(e,t,n,r={}){var o=is(t,e,n,r);return()=>{e.removeEventListener(t,o,r)}}function vl(e,t,n,r,o){var i={capture:r,passive:o},s=is(e,t,n,i);(t===document.body||t===window||t===document||t instanceof HTMLMediaElement)&&Ua(()=>{t.removeEventListener(e,s,i)})}function wn(e){for(var t=0;t<e.length;t++)hl.add(e[t]);for(var n of os)n(e)}let ml=null;function Jr(e){var t=this,n=t.ownerDocument,r=e.type,o=e.composedPath?.()||[],i=o[0]||e.target;ml=e;var s=0,a=ml===e&&e.__root;if(a){var l=o.indexOf(a);if(l!==-1&&(t===document||t===window)){e.__root=t;return}var u=o.indexOf(t);if(u===-1)return;l<=u&&(s=l)}if(i=o[s]||e.target,i!==t){dr(e,"currentTarget",{configurable:!0,get(){return i||n}});var d=ze,p=$e;Qt(null),en(null);try{for(var f,g=[];i!==null;){var h=i.assignedSlot||i.parentNode||i.host||null;try{var v=i["__"+r];if(v!=null&&(!i.disabled||e.target===i))if(Ar(v)){var[w,...b]=v;w.apply(i,[e,...b])}else v.call(i,e)}catch(k){f?g.push(k):f=k}if(e.cancelBubble||h===t||h===null)break;i=h}if(f){for(let k of g)queueMicrotask(()=>{throw k});throw f}}finally{e.__root=t,delete e.currentTarget,Qt(d),en(p)}}}function as(e){var t=document.createElement("template");return t.innerHTML=e.replaceAll("<!>","<!---->"),t.content}function wt(e,t){var n=$e;n.nodes_start===null&&(n.nodes_start=e,n.nodes_end=t)}function J(e,t){var n=(t&va)!==0,r=(t&uf)!==0,o,i=!e.startsWith("<!>");return()=>{if(be)return wt(Ee,null),Ee;o===void 0&&(o=as(i?e:"<!>"+e),n||(o=Ge(o)));var s=r||Xa?document.importNode(o,!0):o.cloneNode(!0);if(n){var a=Ge(s),l=s.lastChild;wt(a,l)}else wt(s,s);return s}}function ap(e,t,n="svg"){var r=!e.startsWith("<!>"),o=(t&va)!==0,i=`<${n}>${r?e:"<!>"+e}</${n}>`,s;return()=>{if(be)return wt(Ee,null),Ee;if(!s){var a=as(i),l=Ge(a);if(o)for(s=document.createDocumentFragment();Ge(l);)s.appendChild(Ge(l));else s=Ge(l)}var u=s.cloneNode(!0);if(o){var d=Ge(u),p=u.lastChild;wt(d,p)}else wt(u,u);return u}}function ge(e,t){return ap(e,t,"svg")}function _e(e=""){if(!be){var t=qt(e+"");return wt(t,t),t}var n=Ee;return n.nodeType!==Bi&&(n.before(n=qt()),dt(n)),wt(n,n),n}function Ce(){if(be)return wt(Ee,null),Ee;var e=document.createDocumentFragment(),t=document.createComment(""),n=qt();return e.append(t,n),wt(t,n),e}function H(e,t){if(be){$e.nodes_end=Ee,mn();return}e!==null&&e.before(t)}function lp(e){return e.endsWith("capture")&&e!=="gotpointercapture"&&e!=="lostpointercapture"}const up=["beforeinput","click","change","dblclick","contextmenu","focusin","focusout","input","keydown","keyup","mousedown","mousemove","mouseout","mouseover","mouseup","pointerdown","pointermove","pointerout","pointerover","pointerup","touchend","touchmove","touchstart"];function cp(e){return up.includes(e)}const dp={formnovalidate:"formNoValidate",ismap:"isMap",nomodule:"noModule",playsinline:"playsInline",readonly:"readOnly",defaultvalue:"defaultValue",defaultchecked:"defaultChecked",srcobject:"srcObject",novalidate:"noValidate",allowfullscreen:"allowFullscreen",disablepictureinpicture:"disablePictureInPicture",disableremoteplayback:"disableRemotePlayback"};function fp(e){return e=e.toLowerCase(),dp[e]??e}const pp=["touchstart","touchmove"];function gp(e){return pp.includes(e)}const hp=["textarea","script","style","title"];function vp(e){return hp.includes(e)}function Oe(e,t){var n=t==null?"":typeof t=="object"?t+"":t;n!==(e.__t??=e.nodeValue)&&(e.__t=n,e.nodeValue=n+"")}function yl(e,t){return wl(e,t)}function mp(e,t){Ui(),t.intro=t.intro??!1;const n=t.target,r=be,o=Ee;try{for(var i=Ge(n);i&&(i.nodeType!==hr||i.data!==ma);)i=Jt(i);if(!i)throw Xn;Ct(!0),dt(i),mn();const s=wl(e,{...t,anchor:i});if(Ee===null||Ee.nodeType!==hr||Ee.data!==Ti)throw Zr(),Xn;return Ct(!1),s}catch(s){if(s instanceof Error&&s.message.split(`
`).some(a=>a.startsWith("https://svelte.dev/e/")))throw s;return s!==Xn&&console.warn("Failed to hydrate: ",s),t.recover===!1&&Ef(),Ui(),Ji(n),Ct(!1),yl(e,t)}finally{Ct(r),dt(o)}}const _r=new Map;function wl(e,{target:t,anchor:n,props:r={},events:o,context:i,intro:s=!0}){Ui();var a=new Set,l=p=>{for(var f=0;f<p.length;f++){var g=p[f];if(!a.has(g)){a.add(g);var h=gp(g);t.addEventListener(g,Jr,{passive:h});var v=_r.get(g);v===void 0?(document.addEventListener(g,Jr,{passive:h}),_r.set(g,1)):_r.set(g,v+1)}}};l(Vi(hl)),os.add(l);var u=void 0,d=ep(()=>{var p=n??t.appendChild(qt());return Rt(()=>{if(i){ue({});var f=Ze;f.c=i}o&&(r.$$events=o),be&&wt(p,null),u=e(p,r)||{},be&&($e.nodes_end=Ee),i&&ce()}),()=>{for(var f of a){t.removeEventListener(f,Jr);var g=_r.get(f);--g===0?(document.removeEventListener(f,Jr),_r.delete(f)):_r.set(f,g)}os.delete(l),p!==n&&p.parentNode?.removeChild(p)}});return ls.set(u,d),u}let ls=new WeakMap;function yp(e,t){const n=ls.get(e);return n?(ls.delete(e),n(t)):Promise.resolve()}function Xe(e,t,...n){var r=e,o=tt,i;Jn(()=>{o!==(o=t())&&(i&&(ht(i),i=null),i=Rt(()=>o(r,...n)))},pr),be&&(r=Ee)}function Tn(e){Ze===null&&Ki(),vr&&Ze.l!==null?wp(Ze).m.push(e):Ke(()=>{const t=nt(e);if(typeof t=="function")return t})}function Ro(e){Ze===null&&Ki(),Tn(()=>()=>nt(e))}function wp(e){var t=e.l;return t.u??={a:[],b:[],m:[]}}function ae(e,t,n=!1){be&&mn();var r=e,o=null,i=null,s=Je,a=n?pr:0,l=!1;const u=(f,g=!0)=>{l=!0,p(g,f)};function d(){var f=s?o:i,g=s?i:o;f&&Io(f),g&&Gr(g,()=>{s?i=null:o=null})}const p=(f,g)=>{if(s===(s=f))return;let h=!1;if(be){const b=$a(r)===Mi;!!s===b&&(r=Xi(),dt(r),Ct(!1),h=!0)}var v=Wa(),w=r;s?o??=g&&Rt(()=>g(w)):i??=g&&Rt(()=>g(w)),v||d(),h&&Ct(!0)};Jn(()=>{l=!1,t(u),l||p(null,null)},a),be&&(r=Ee)}function bp(e,t){be&&dt(Ge(e)),Fr(()=>{var n=t();for(var r in n){var o=n[r];o?e.style.setProperty(r,o):e.style.removeProperty(r)}})}function kr(e,t){return t}function xp(e,t,n){for(var r=e.items,o=[],i=t.length,s=0;s<i;s++)ts(t[s].e,o,!0);var a=i>0&&o.length===0&&n!==null;if(a){var l=n.parentNode;Ji(l),l.append(n),r.clear(),tn(e,t[0].prev,t[i-1].next)}ol(o,()=>{for(var u=0;u<i;u++){var d=t[u];a||(r.delete(d.k),tn(e,d.prev,d.next)),ht(d.e,!a)}})}function ft(e,t,n,r,o,i=null){var s=e,a={flags:t,items:new Map,first:null},l=(t&ga)!==0;if(l){var u=e;s=be?dt(Ge(u)):u.appendChild(qt())}be&&mn();var d=null,p=!1,f=new Map,g=ji(()=>{var b=n();return Ar(b)?b:b==null?[]:Vi(b)}),h,v;function w(){_p(v,h,a,f,s,o,t,r,n),i!==null&&(h.length===0?d?Io(d):d=Rt(()=>i(s)):d!==null&&Gr(d,()=>{d=null}))}Jn(()=>{v??=$e,h=c(g);var b=h.length;if(p&&b===0)return;p=b===0;let k=!1;if(be){var x=$a(s)===Mi;x!==(b===0)&&(s=Xi(),dt(s),Ct(!1),k=!0)}if(be){for(var S=null,E,L=0;L<b;L++){if(Ee.nodeType===hr&&Ee.data===Ti){s=Ee,k=!0,Ct(!1);break}var D=h[L],q=r(D,L);E=bl(Ee,a,S,null,D,q,L,o,t,n),a.items.set(q,E),S=E}b>0&&dt(Xi())}be?b===0&&i&&(d=Rt(()=>i(s))):w(),k&&Ct(!0),c(g)}),be&&(s=Ee)}function _p(e,t,n,r,o,i,s,a,l){var u=(s&nf)!==0,d=(s&(Di|Hi))!==0,p=t.length,f=n.items,g=n.first,h=g,v,w=null,b,k=[],x=[],S,E,L,D;if(u)for(D=0;D<p;D+=1)S=t[D],E=a(S,D),L=f.get(E),L!==void 0&&(L.a?.measure(),(b??=new Set).add(L));for(D=0;D<p;D+=1){if(S=t[D],E=a(S,D),L=f.get(E),L===void 0){var q=r.get(E);if(q!==void 0){r.delete(E),f.set(E,q);var B=w?w.next:h;tn(n,w,q),tn(n,q,B),us(q,B,o),w=q}else{var U=h?h.e.nodes_start:o;w=bl(U,n,w,w===null?n.first:w.next,S,E,D,i,s,l)}f.set(E,w),k=[],x=[],h=w.next;continue}if(d&&kp(L,S,D,s),(L.e.f&It)!==0&&(Io(L.e),u&&(L.a?.unfix(),(b??=new Set).delete(L))),L!==h){if(v!==void 0&&v.has(L)){if(k.length<x.length){var O=x[0],$;w=O.prev;var C=k[0],_=k[k.length-1];for($=0;$<k.length;$+=1)us(k[$],O,o);for($=0;$<x.length;$+=1)v.delete(x[$]);tn(n,C.prev,_.next),tn(n,w,C),tn(n,_,O),h=O,w=_,D-=1,k=[],x=[]}else v.delete(L),us(L,h,o),tn(n,L.prev,L.next),tn(n,L,w===null?n.first:w.next),tn(n,w,L),w=L;continue}for(k=[],x=[];h!==null&&h.k!==E;)(h.e.f&It)===0&&(v??=new Set).add(h),x.push(h),h=h.next;if(h===null)continue;L=h}k.push(L),w=L,h=L.next}if(h!==null||v!==void 0){for(var P=v===void 0?[]:Vi(v);h!==null;)(h.e.f&It)===0&&P.push(h),h=h.next;var N=P.length;if(N>0){var T=(s&ga)!==0&&p===0?o:null;if(u){for(D=0;D<N;D+=1)P[D].a?.measure();for(D=0;D<N;D+=1)P[D].a?.fix()}xp(n,P,T)}}u&&Xr(()=>{if(b!==void 0)for(L of b)L.a?.apply()}),e.first=n.first&&n.first.e,e.last=w&&w.e;for(var Z of r.values())ht(Z.e);r.clear()}function kp(e,t,n,r){(r&Di)!==0&&jr(e.v,t),(r&Hi)!==0?jr(e.i,n):e.i=n}function bl(e,t,n,r,o,i,s,a,l,u,d){var p=(l&Di)!==0,f=(l&rf)===0,g=p?f?Za(o,!1,!1):Un(o):o,h=(l&Hi)===0?s:Un(s),v={i:h,v:g,k:i,a:null,e:null,prev:n,next:r};try{if(e===null){var w=document.createDocumentFragment();w.append(e=qt())}return v.e=Rt(()=>a(e,g,h,u),be),v.e.prev=n&&n.e,v.e.next=r&&r.e,n===null?d||(t.first=v):(n.next=v,n.e.next=v.e),r!==null&&(r.prev=v,r.e.prev=v.e),v}finally{}}function us(e,t,n){for(var r=e.next?e.next.e.nodes_start:n,o=t?t.e.nodes_start:n,i=e.e.nodes_start;i!==null&&i!==r;){var s=Jt(i);o.before(i),i=s}}function tn(e,t,n){t===null?e.first=n:(t.next=n,t.e.next=n&&n.e),n!==null&&(n.prev=t,n.e.prev=t&&t.e)}function cs(e,t,n=!1,r=!1,o=!1){var i=e,s="";xe(()=>{var a=$e;if(s===(s=t()??"")){be&&mn();return}if(a.nodes_start!==null&&(nl(a.nodes_start,a.nodes_end),a.nodes_start=a.nodes_end=null),s!==""){if(be){Ee.data;for(var l=mn(),u=l;l!==null&&(l.nodeType!==hr||l.data!=="");)u=l,l=Jt(l);if(l===null)throw Zr(),Xn;wt(Ee,u),i=dt(l);return}var d=s+"";n?d=`<svg>${d}</svg>`:r&&(d=`<math>${d}</math>`);var p=as(d);if((n||r)&&(p=Ge(p)),wt(Ge(p),p.lastChild),n||r)for(;Ge(p);)i.before(Ge(p));else i.before(p)}})}function ds(e,t,n){be&&mn();var r=e,o,i,s=null,a=null;function l(){i&&(Gr(i),i=null),s&&(s.lastChild.remove(),r.before(s),s=null),i=a,a=null}Jn(()=>{if(o!==(o=t())){var u=Wa();if(o){var d=r;u&&(s=document.createDocumentFragment(),s.append(d=qt()),i&&Qe.skipped_effects.add(i)),a=Rt(()=>n(d,o))}u?Qe.add_callback(l):l()}},pr),be&&(r=Ee)}function $p(e,t,n,r,o,i){let s=be;be&&mn();var a,l,u=null;be&&Ee.nodeType===bf&&(u=Ee,mn());var d=be?Ee:e,p;Jn(()=>{const f=t()||null;var g=f==="svg"?df:null;f!==a&&(p&&(f===null?Gr(p,()=>{p=null,l=null}):f===l?Io(p):ht(p)),f&&f!==l&&(p=Rt(()=>{if(u=be?u:g?document.createElementNS(g,f):document.createElement(f),wt(u,u),r){be&&vp(f)&&u.append(document.createComment(""));var h=be?Ge(u):u.appendChild(qt());be&&(h===null?Ct(!1):dt(h)),r(u,h)}$e.nodes_end=u,d.before(u)})),a=f,a&&(l=a))},pr),s&&(Ct(!0),dt(d))}function He(e,t){wr(()=>{var n=e.getRootNode(),r=n.host?n:n.head??n.ownerDocument.head;if(!r.querySelector("#"+t.hash)){const o=document.createElement("style");o.id=t.hash,o.textContent=t.code,r.appendChild(o)}})}function pt(e,t,n){wr(()=>{var r=nt(()=>t(e,n?.())||{});if(n&&r?.update){var o=!1,i={};Fr(()=>{var s=n();ns(s),o&&Sa(i,s)&&(i=s,r.update(s))}),o=!0}if(r?.destroy)return()=>r.destroy()})}function Cp(e,t){var n=void 0,r;Jn(()=>{n!==(n=t())&&(r&&(ht(r),r=null),n&&(r=Rt(()=>{wr(()=>n(e))})))})}function xl(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=xl(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function Sp(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=xl(e))&&(r&&(r+=" "),r+=t);return r}function bn(e){return typeof e=="object"?Sp(e):e??""}const _l=[...` 	
\r\f \v\uFEFF`];function Ep(e,t,n){var r=e==null?"":""+e;if(t&&(r=r?r+" "+t:t),n){for(var o in n)if(n[o])r=r?r+" "+o:o;else if(r.length)for(var i=o.length,s=0;(s=r.indexOf(o,s))>=0;){var a=s+i;(s===0||_l.includes(r[s-1]))&&(a===r.length||_l.includes(r[a]))?r=(s===0?"":r.substring(0,s))+r.substring(a+1):s=a}}return r===""?null:r}function kl(e,t=!1){var n=t?" !important;":";",r="";for(var o in e){var i=e[o];i!=null&&i!==""&&(r+=" "+o+": "+i+n)}return r}function fs(e){return e[0]!=="-"||e[1]!=="-"?e.toLowerCase():e}function Np(e,t){if(t){var n="",r,o;if(Array.isArray(t)?(r=t[0],o=t[1]):r=t,e){e=String(e).replaceAll(/\s*\/\*.*?\*\/\s*/g,"").trim();var i=!1,s=0,a=!1,l=[];r&&l.push(...Object.keys(r).map(fs)),o&&l.push(...Object.keys(o).map(fs));var u=0,d=-1;const v=e.length;for(var p=0;p<v;p++){var f=e[p];if(a?f==="/"&&e[p-1]==="*"&&(a=!1):i?i===f&&(i=!1):f==="/"&&e[p+1]==="*"?a=!0:f==='"'||f==="'"?i=f:f==="("?s++:f===")"&&s--,!a&&i===!1&&s===0){if(f===":"&&d===-1)d=p;else if(f===";"||p===v-1){if(d!==-1){var g=fs(e.substring(u,d).trim());if(!l.includes(g)){f!==";"&&p++;var h=e.substring(u,p).trim();n+=" "+h+";"}}u=p+1,d=-1}}}}return r&&(n+=kl(r)),o&&(n+=kl(o,!0)),n=n.trim(),n===""?null:n}return e==null?null:String(e)}function bt(e,t,n,r,o,i){var s=e.__className;if(be||s!==n||s===void 0){var a=Ep(n,r,i);(!be||a!==e.getAttribute("class"))&&(a==null?e.removeAttribute("class"):t?e.className=a:e.setAttribute("class",a)),e.__className=n}else if(i&&o!==i)for(var l in i){var u=!!i[l];(o==null||u!==!!o[l])&&e.classList.toggle(l,u)}return i}function ps(e,t={},n,r){for(var o in n){var i=n[o];t[o]!==i&&(n[o]==null?e.style.removeProperty(o):e.style.setProperty(o,i,r))}}function rt(e,t,n,r){var o=e.__style;if(be||o!==t){var i=Np(t,r);(!be||i!==e.getAttribute("style"))&&(i==null?e.removeAttribute("style"):e.style.cssText=i),e.__style=t}else r&&(Array.isArray(r)?(ps(e,n?.[0],r[0]),ps(e,n?.[1],r[1],"important")):ps(e,n,r));return r}function gs(e,t,n=!1){if(e.multiple){if(t==null)return;if(!Ar(t))return Df();for(var r of e.options)r.selected=t.includes($l(r));return}for(r of e.options){var o=$l(r);if(Wf(o,t)){r.selected=!0;return}}(!n||t!==void 0)&&(e.selectedIndex=-1)}function Pp(e){var t=new MutationObserver(()=>{gs(e,e.__value)});t.observe(e,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["value"]}),Ua(()=>{t.disconnect()})}function $l(e){return"__value"in e?e.__value:e.value}const Vn=Symbol("class"),nn=Symbol("style"),Cl=Symbol("is custom element"),Sl=Symbol("is html");function rn(e){if(be){var t=!1,n=()=>{if(!t){if(t=!0,e.hasAttribute("value")){var r=e.value;ye(e,"value",null),e.value=r}if(e.hasAttribute("checked")){var o=e.checked;ye(e,"checked",null),e.checked=o}}};e.__on_r=n,qf(n),Uf()}}function Bo(e,t){var n=Ko(e);n.value===(n.value=t??void 0)||e.value===t&&(t!==0||e.nodeName!=="PROGRESS")||(e.value=t??"")}function hs(e,t){var n=Ko(e);n.checked!==(n.checked=t??void 0)&&(e.checked=t)}function zp(e,t){t?e.hasAttribute("selected")||e.setAttribute("selected",""):e.removeAttribute("selected")}function ye(e,t,n,r){var o=Ko(e);be&&(o[t]=e.getAttribute(t),t==="src"||t==="srcset"||t==="href"&&e.nodeName==="LINK")||o[t]!==(o[t]=n)&&(t==="loading"&&(e[wf]=n),n==null?e.removeAttribute(t):typeof n!="string"&&Nl(e).includes(t)?e[t]=n:e.setAttribute(t,n))}function Lp(e,t,n,r,o=!1){var i=Ko(e),s=i[Cl],a=!i[Sl];let l=be&&s;l&&Ct(!1);var u=t||{},d=e.tagName==="OPTION";for(var p in t)p in n||(n[p]=null);n.class?n.class=bn(n.class):(r||n[Vn])&&(n.class=null),n[nn]&&(n.style??=null);var f=Nl(e);for(const x in n){let S=n[x];if(d&&x==="value"&&S==null){e.value=e.__value="",u[x]=S;continue}if(x==="class"){var g=e.namespaceURI==="http://www.w3.org/1999/xhtml";bt(e,g,S,r,t?.[Vn],n[Vn]),u[x]=S,u[Vn]=n[Vn];continue}if(x==="style"){rt(e,S,t?.[nn],n[nn]),u[x]=S,u[nn]=n[nn];continue}var h=u[x];if(!(S===h&&!(S===void 0&&e.hasAttribute(x)))){u[x]=S;var v=x[0]+x[1];if(v!=="$$")if(v==="on"){const E={},L="$$"+x;let D=x.slice(2);var w=cp(D);if(lp(D)&&(D=D.slice(0,-7),E.capture=!0),!w&&h){if(S!=null)continue;e.removeEventListener(D,u[L],E),u[L]=null}if(S!=null)if(w)e[`__${D}`]=S,wn([D]);else{let q=function(B){u[x].call(this,B)};u[L]=is(D,e,q,E)}else w&&(e[`__${D}`]=void 0)}else if(x==="style")ye(e,x,S);else if(x==="autofocus")Ff(e,!!S);else if(!s&&(x==="__value"||x==="value"&&S!=null))e.value=e.__value=S;else if(x==="selected"&&d)zp(e,S);else{var b=x;a||(b=fp(b));var k=b==="defaultValue"||b==="defaultChecked";if(S==null&&!s&&!k)if(i[x]=null,b==="value"||b==="checked"){let E=e;const L=t===void 0;if(b==="value"){let D=E.defaultValue;E.removeAttribute(b),E.defaultValue=D,E.value=E.__value=L?D:null}else{let D=E.defaultChecked;E.removeAttribute(b),E.defaultChecked=D,E.checked=L?D:!1}}else e.removeAttribute(x);else k||f.includes(b)&&(s||typeof S!="string")?(e[b]=S,b in i&&(i[b]=Je)):typeof S!="function"&&ye(e,b,S)}}}return l&&Ct(!0),u}function Ue(e,t,n=[],r=[],o,i=!1){Ta(n,r,s=>{var a=void 0,l={},u=e.nodeName==="SELECT",d=!1;if(Jn(()=>{var f=t(...s.map(c)),g=Lp(e,a,f,o,i);d&&u&&"value"in f&&gs(e,f.value);for(let v of Object.getOwnPropertySymbols(l))f[v]||ht(l[v]);for(let v of Object.getOwnPropertySymbols(f)){var h=f[v];v.description===ff&&(!a||h!==a[v])&&(l[v]&&ht(l[v]),l[v]=Rt(()=>Cp(e,()=>h))),g[v]=h}a=g}),u){var p=e;wr(()=>{gs(p,a.value,!0),Pp(p)})}d=!0})}function Ko(e){return e.__attributes??={[Cl]:e.nodeName.includes("-"),[Sl]:e.namespaceURI===cf}}var El=new Map;function Nl(e){var t=e.getAttribute("is")||e.nodeName,n=El.get(t);if(n)return n;El.set(t,n=[]);for(var r,o=e,i=Element.prototype;i!==o;){r=ya(o);for(var s in r)r[s].set&&n.push(s);o=zo(o)}return n}class vs{#t=new WeakMap;#e;#n;static entries=new WeakMap;constructor(t){this.#n=t}observe(t,n){var r=this.#t.get(t)||new Set;return r.add(n),this.#t.set(t,r),this.#o().observe(t,this.#n),()=>{var o=this.#t.get(t);o.delete(n),o.size===0&&(this.#t.delete(t),this.#e.unobserve(t))}}#o(){return this.#e??(this.#e=new ResizeObserver(t=>{for(var n of t){vs.entries.set(n.target,n);for(var r of this.#t.get(n.target)||[])r(n)}}))}}var Dp=new vs({box:"border-box"});function Pl(e,t,n){var r=Dp.observe(e,()=>n(e[t]));wr(()=>(nt(()=>n(e[t])),r))}function zl(e,t){return e===t||e?.[Ut]===t}function Et(e={},t,n,r){return wr(()=>{var o,i;return Fr(()=>{o=i,i=[],nt(()=>{e!==n(...i)&&(t(e,...i),o&&zl(n(...o),e)&&t(null,...o))})}),()=>{Xr(()=>{i&&zl(n(...i),e)&&t(null,...i)})}}),e}function Ll(e=!1){const t=Ze,n=t.l.u;if(!n)return;let r=()=>ns(t.s);if(e){let o=0,i={};const s=Yr(()=>{let a=!1;const l=t.s;for(const u in l)l[u]!==i[u]&&(i[u]=l[u],a=!0);return a&&o++,o});r=()=>c(s)}n.b.length&&Qa(()=>{Dl(t,r),Lo(n.b)}),Ke(()=>{const o=nt(()=>n.m.map(vf));return()=>{for(const i of o)typeof i=="function"&&i()}}),n.a.length&&Ke(()=>{Dl(t,r),Lo(n.a)})}function Dl(e,t){if(e.l.s)for(const n of e.l.s)c(n);t()}let Xo=!1;function Hp(e){var t=Xo;try{return Xo=!1,[e(),Xo]}finally{Xo=t}}const Mp={get(e,t){if(!e.exclude.includes(t))return e.props[t]},set(e,t){return!1},getOwnPropertyDescriptor(e,t){if(!e.exclude.includes(t)&&t in e.props)return{enumerable:!0,configurable:!0,value:e.props[t]}},has(e,t){return e.exclude.includes(t)?!1:t in e.props},ownKeys(e){return Reflect.ownKeys(e.props).filter(t=>!e.exclude.includes(t))}};function Te(e,t,n){return new Proxy({props:e,exclude:t},Mp)}const Tp={get(e,t){if(!e.exclude.includes(t))return c(e.version),t in e.special?e.special[t]():e.props[t]},set(e,t,n){if(!(t in e.special)){var r=$e;try{en(e.parent_effect),e.special[t]=y({get[t](){return e.props[t]}},t,ha)}finally{en(r)}}return e.special[t](n),Ra(e.version),!0},getOwnPropertyDescriptor(e,t){if(!e.exclude.includes(t)&&t in e.props)return{enumerable:!0,configurable:!0,value:e.props[t]}},deleteProperty(e,t){return e.exclude.includes(t)||(e.exclude.push(t),Ra(e.version)),!0},has(e,t){return e.exclude.includes(t)?!1:t in e.props},ownKeys(e){return Reflect.ownKeys(e.props).filter(t=>!e.exclude.includes(t))}};function Hl(e,t){return new Proxy({props:e,exclude:t,special:{},version:Un(0),parent_effect:$e},Tp)}const Vp={get(e,t){let n=e.props.length;for(;n--;){let r=e.props[n];if(Ir(r)&&(r=r()),typeof r=="object"&&r!==null&&t in r)return r[t]}},set(e,t,n){let r=e.props.length;for(;r--;){let o=e.props[r];Ir(o)&&(o=o());const i=hn(o,t);if(i&&i.set)return i.set(n),!0}return!1},getOwnPropertyDescriptor(e,t){let n=e.props.length;for(;n--;){let r=e.props[n];if(Ir(r)&&(r=r()),typeof r=="object"&&r!==null&&t in r){const o=hn(r,t);return o&&!o.configurable&&(o.configurable=!0),o}}},has(e,t){if(t===Ut||t===Zi)return!1;for(let n of e.props)if(Ir(n)&&(n=n()),n!=null&&t in n)return!0;return!1},ownKeys(e){const t=[];for(let n of e.props)if(Ir(n)&&(n=n()),!!n){for(const r in n)t.includes(r)||t.push(r);for(const r of Object.getOwnPropertySymbols(n))t.includes(r)||t.push(r)}return t}};function Ae(...e){return new Proxy({props:e},Vp)}function y(e,t,n,r){var o=!vr||(n&sf)!==0,i=(n&af)!==0,s=(n&lf)!==0,a=r,l=!0,u=()=>(l&&(l=!1,a=s?nt(r):r),a),d;if(i){var p=Ut in e||Zi in e;d=hn(e,t)?.set??(p&&t in e?x=>e[t]=x:void 0)}var f,g=!1;i?[f,g]=Hp(()=>e[t]):f=e[t],f===void 0&&r!==void 0&&(f=u(),d&&(o&&Nf(),d(f)));var h;if(o?h=()=>{var x=e[t];return x===void 0?u():(l=!0,x)}:h=()=>{var x=e[t];return x!==void 0&&(a=void 0),x===void 0?a:x},o&&(n&ha)===0)return h;if(d){var v=e.$$legacy;return function(x,S){return arguments.length>0?((!o||!S||v||g)&&d(S?h():x),x):h()}}var w=!1,b=((n&of)!==0?Yr:ji)(()=>(w=!1,h()));i&&c(b);var k=$e;return function(x,S){if(arguments.length>0){const E=S?c(b):o&&i?Mt(x):x;return j(b,E),w=!0,a!==void 0&&(a=E),x}return Qn&&w||(k.f&Yn)!==0?b.v:c(b)}}function Op(e){return new Ap(e)}class Ap{#t;#e;constructor(t){var n=new Map,r=(i,s)=>{var a=Za(s,!1,!1);return n.set(i,a),a};const o=new Proxy({...t.props||{},$$events:{}},{get(i,s){return c(n.get(s)??r(s,Reflect.get(i,s)))},has(i,s){return s===Zi?!0:(c(n.get(s)??r(s,Reflect.get(i,s))),Reflect.has(i,s))},set(i,s,a){return j(n.get(s)??r(s,a),a),Reflect.set(i,s,a)}});this.#e=(t.hydrate?mp:yl)(t.component,{target:t.target,anchor:t.anchor,props:o,context:t.context,intro:t.intro??!1,recover:t.recover}),(!t?.props?.$$host||t.sync===!1)&&m(),this.#t=o.$$events;for(const i of Object.keys(this.#e))i==="$set"||i==="$destroy"||i==="$on"||dr(this,i,{get(){return this.#e[i]},set(s){this.#e[i]=s},enumerable:!0});this.#e.$set=i=>{Object.assign(o,i)},this.#e.$destroy=()=>{yp(this.#e)}}$set(t){this.#e.$set(t)}$on(t,n){this.#t[t]=this.#t[t]||[];const r=(...o)=>n.call(this,...o);return this.#t[t].push(r),()=>{this.#t[t]=this.#t[t].filter(o=>o!==r)}}$destroy(){this.#e.$destroy()}}let Ml;typeof HTMLElement=="function"&&(Ml=class extends HTMLElement{$$ctor;$$s;$$c;$$cn=!1;$$d={};$$r=!1;$$p_d={};$$l={};$$l_u=new Map;$$me;constructor(e,t,n){super(),this.$$ctor=e,this.$$s=t,n&&this.attachShadow({mode:"open"})}addEventListener(e,t,n){if(this.$$l[e]=this.$$l[e]||[],this.$$l[e].push(t),this.$$c){const r=this.$$c.$on(e,t);this.$$l_u.set(t,r)}super.addEventListener(e,t,n)}removeEventListener(e,t,n){if(super.removeEventListener(e,t,n),this.$$c){const r=this.$$l_u.get(t);r&&(r(),this.$$l_u.delete(t))}}async connectedCallback(){if(this.$$cn=!0,!this.$$c){let e=function(r){return o=>{const i=document.createElement("slot");r!=="default"&&(i.name=r),H(o,i)}};if(await Promise.resolve(),!this.$$cn||this.$$c)return;const t={},n=Ip(this);for(const r of this.$$s)r in n&&(r==="default"&&!this.$$d.children?(this.$$d.children=e(r),t.default=!0):t[r]=e(r));for(const r of this.attributes){const o=this.$$g_p(r.name);o in this.$$d||(this.$$d[o]=Yo(o,r.value,this.$$p_d,"toProp"))}for(const r in this.$$p_d)!(r in this.$$d)&&this[r]!==void 0&&(this.$$d[r]=this[r],delete this[r]);this.$$c=Op({component:this.$$ctor,target:this.shadowRoot||this,props:{...this.$$d,$$slots:t,$$host:this}}),this.$$me=es(()=>{Fr(()=>{this.$$r=!0;for(const r of Po(this.$$c)){if(!this.$$p_d[r]?.reflect)continue;this.$$d[r]=this.$$c[r];const o=Yo(r,this.$$d[r],this.$$p_d,"toAttribute");o==null?this.removeAttribute(this.$$p_d[r].attribute||r):this.setAttribute(this.$$p_d[r].attribute||r,o)}this.$$r=!1})});for(const r in this.$$l)for(const o of this.$$l[r]){const i=this.$$c.$on(r,o);this.$$l_u.set(o,i)}this.$$l={}}}attributeChangedCallback(e,t,n){this.$$r||(e=this.$$g_p(e),this.$$d[e]=Yo(e,n,this.$$p_d,"toProp"),this.$$c?.$set({[e]:this.$$d[e]}))}disconnectedCallback(){this.$$cn=!1,Promise.resolve().then(()=>{!this.$$cn&&this.$$c&&(this.$$c.$destroy(),this.$$me(),this.$$c=void 0)})}$$g_p(e){return Po(this.$$p_d).find(t=>this.$$p_d[t].attribute===e||!this.$$p_d[t].attribute&&t.toLowerCase()===e)||e}});function Yo(e,t,n,r){const o=n[e]?.type;if(t=o==="Boolean"&&typeof t!="boolean"?t!=null:t,!r||!n[e])return t;if(r==="toAttribute")switch(o){case"Object":case"Array":return t==null?null:JSON.stringify(t);case"Boolean":return t?"":null;case"Number":return t??null;default:return t}else switch(o){case"Object":case"Array":return t&&JSON.parse(t);case"Boolean":return t;case"Number":return t!=null?+t:t;default:return t}}function Ip(e){const t={};return e.childNodes.forEach(n=>{t[n.slot||"default"]=!0}),t}function se(e,t,n,r,o,i){let s=class extends Ml{constructor(){super(e,n,o),this.$$p_d=t}static get observedAttributes(){return Po(t).map(a=>(t[a].attribute||a).toLowerCase())}};return Po(t).forEach(a=>{dr(s.prototype,a,{get(){return this.$$c&&a in this.$$c?this.$$c[a]:this.$$d[a]},set(l){l=Yo(a,l,t),this.$$d[a]=l;var u=this.$$c;if(u){var d=hn(u,a)?.get;d?u[a]=l:u.$set({[a]:l})}}})}),r.forEach(a=>{dr(s.prototype,a,{get(){return this.$$c?.[a]}})}),e.element=s,s}var qp={value:()=>{}};function jo(){for(var e=0,t=arguments.length,n={},r;e<t;++e){if(!(r=arguments[e]+"")||r in n||/[\s.]/.test(r))throw new Error("illegal type: "+r);n[r]=[]}return new Wo(n)}function Wo(e){this._=e}function Zp(e,t){return e.trim().split(/^|\s+/).map(function(n){var r="",o=n.indexOf(".");if(o>=0&&(r=n.slice(o+1),n=n.slice(0,o)),n&&!t.hasOwnProperty(n))throw new Error("unknown type: "+n);return{type:n,name:r}})}Wo.prototype=jo.prototype={constructor:Wo,on:function(e,t){var n=this._,r=Zp(e+"",n),o,i=-1,s=r.length;if(arguments.length<2){for(;++i<s;)if((o=(e=r[i]).type)&&(o=Rp(n[o],e.name)))return o;return}if(t!=null&&typeof t!="function")throw new Error("invalid callback: "+t);for(;++i<s;)if(o=(e=r[i]).type)n[o]=Tl(n[o],e.name,t);else if(t==null)for(o in n)n[o]=Tl(n[o],e.name,null);return this},copy:function(){var e={},t=this._;for(var n in t)e[n]=t[n].slice();return new Wo(e)},call:function(e,t){if((o=arguments.length-2)>0)for(var n=new Array(o),r=0,o,i;r<o;++r)n[r]=arguments[r+2];if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(i=this._[e],r=0,o=i.length;r<o;++r)i[r].value.apply(t,n)},apply:function(e,t,n){if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(var r=this._[e],o=0,i=r.length;o<i;++o)r[o].value.apply(t,n)}};function Rp(e,t){for(var n=0,r=e.length,o;n<r;++n)if((o=e[n]).name===t)return o.value}function Tl(e,t,n){for(var r=0,o=e.length;r<o;++r)if(e[r].name===t){e[r]=qp,e=e.slice(0,r).concat(e.slice(r+1));break}return n!=null&&e.push({name:t,value:n}),e}var ms="http://www.w3.org/1999/xhtml";const Vl={svg:"http://www.w3.org/2000/svg",xhtml:ms,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function Fo(e){var t=e+="",n=t.indexOf(":");return n>=0&&(t=e.slice(0,n))!=="xmlns"&&(e=e.slice(n+1)),Vl.hasOwnProperty(t)?{space:Vl[t],local:e}:e}function Bp(e){return function(){var t=this.ownerDocument,n=this.namespaceURI;return n===ms&&t.documentElement.namespaceURI===ms?t.createElement(e):t.createElementNS(n,e)}}function Kp(e){return function(){return this.ownerDocument.createElementNS(e.space,e.local)}}function Ol(e){var t=Fo(e);return(t.local?Kp:Bp)(t)}function Xp(){}function ys(e){return e==null?Xp:function(){return this.querySelector(e)}}function Yp(e){typeof e!="function"&&(e=ys(e));for(var t=this._groups,n=t.length,r=new Array(n),o=0;o<n;++o)for(var i=t[o],s=i.length,a=r[o]=new Array(s),l,u,d=0;d<s;++d)(l=i[d])&&(u=e.call(l,l.__data__,d,i))&&("__data__"in l&&(u.__data__=l.__data__),a[d]=u);return new Nt(r,this._parents)}function jp(e){return e==null?[]:Array.isArray(e)?e:Array.from(e)}function Wp(){return[]}function Al(e){return e==null?Wp:function(){return this.querySelectorAll(e)}}function Fp(e){return function(){return jp(e.apply(this,arguments))}}function Gp(e){typeof e=="function"?e=Fp(e):e=Al(e);for(var t=this._groups,n=t.length,r=[],o=[],i=0;i<n;++i)for(var s=t[i],a=s.length,l,u=0;u<a;++u)(l=s[u])&&(r.push(e.call(l,l.__data__,u,s)),o.push(l));return new Nt(r,o)}function Il(e){return function(){return this.matches(e)}}function ql(e){return function(t){return t.matches(e)}}var Up=Array.prototype.find;function Jp(e){return function(){return Up.call(this.children,e)}}function Qp(){return this.firstElementChild}function eg(e){return this.select(e==null?Qp:Jp(typeof e=="function"?e:ql(e)))}var tg=Array.prototype.filter;function ng(){return Array.from(this.children)}function rg(e){return function(){return tg.call(this.children,e)}}function og(e){return this.selectAll(e==null?ng:rg(typeof e=="function"?e:ql(e)))}function ig(e){typeof e!="function"&&(e=Il(e));for(var t=this._groups,n=t.length,r=new Array(n),o=0;o<n;++o)for(var i=t[o],s=i.length,a=r[o]=[],l,u=0;u<s;++u)(l=i[u])&&e.call(l,l.__data__,u,i)&&a.push(l);return new Nt(r,this._parents)}function Zl(e){return new Array(e.length)}function sg(){return new Nt(this._enter||this._groups.map(Zl),this._parents)}function Go(e,t){this.ownerDocument=e.ownerDocument,this.namespaceURI=e.namespaceURI,this._next=null,this._parent=e,this.__data__=t}Go.prototype={constructor:Go,appendChild:function(e){return this._parent.insertBefore(e,this._next)},insertBefore:function(e,t){return this._parent.insertBefore(e,t)},querySelector:function(e){return this._parent.querySelector(e)},querySelectorAll:function(e){return this._parent.querySelectorAll(e)}};function ag(e){return function(){return e}}function lg(e,t,n,r,o,i){for(var s=0,a,l=t.length,u=i.length;s<u;++s)(a=t[s])?(a.__data__=i[s],r[s]=a):n[s]=new Go(e,i[s]);for(;s<l;++s)(a=t[s])&&(o[s]=a)}function ug(e,t,n,r,o,i,s){var a,l,u=new Map,d=t.length,p=i.length,f=new Array(d),g;for(a=0;a<d;++a)(l=t[a])&&(f[a]=g=s.call(l,l.__data__,a,t)+"",u.has(g)?o[a]=l:u.set(g,l));for(a=0;a<p;++a)g=s.call(e,i[a],a,i)+"",(l=u.get(g))?(r[a]=l,l.__data__=i[a],u.delete(g)):n[a]=new Go(e,i[a]);for(a=0;a<d;++a)(l=t[a])&&u.get(f[a])===l&&(o[a]=l)}function cg(e){return e.__data__}function dg(e,t){if(!arguments.length)return Array.from(this,cg);var n=t?ug:lg,r=this._parents,o=this._groups;typeof e!="function"&&(e=ag(e));for(var i=o.length,s=new Array(i),a=new Array(i),l=new Array(i),u=0;u<i;++u){var d=r[u],p=o[u],f=p.length,g=fg(e.call(d,d&&d.__data__,u,r)),h=g.length,v=a[u]=new Array(h),w=s[u]=new Array(h),b=l[u]=new Array(f);n(d,p,v,w,b,g,t);for(var k=0,x=0,S,E;k<h;++k)if(S=v[k]){for(k>=x&&(x=k+1);!(E=w[x])&&++x<h;);S._next=E||null}}return s=new Nt(s,r),s._enter=a,s._exit=l,s}function fg(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function pg(){return new Nt(this._exit||this._groups.map(Zl),this._parents)}function gg(e,t,n){var r=this.enter(),o=this,i=this.exit();return typeof e=="function"?(r=e(r),r&&(r=r.selection())):r=r.append(e+""),t!=null&&(o=t(o),o&&(o=o.selection())),n==null?i.remove():n(i),r&&o?r.merge(o).order():o}function hg(e){for(var t=e.selection?e.selection():e,n=this._groups,r=t._groups,o=n.length,i=r.length,s=Math.min(o,i),a=new Array(o),l=0;l<s;++l)for(var u=n[l],d=r[l],p=u.length,f=a[l]=new Array(p),g,h=0;h<p;++h)(g=u[h]||d[h])&&(f[h]=g);for(;l<o;++l)a[l]=n[l];return new Nt(a,this._parents)}function vg(){for(var e=this._groups,t=-1,n=e.length;++t<n;)for(var r=e[t],o=r.length-1,i=r[o],s;--o>=0;)(s=r[o])&&(i&&s.compareDocumentPosition(i)^4&&i.parentNode.insertBefore(s,i),i=s);return this}function mg(e){e||(e=yg);function t(p,f){return p&&f?e(p.__data__,f.__data__):!p-!f}for(var n=this._groups,r=n.length,o=new Array(r),i=0;i<r;++i){for(var s=n[i],a=s.length,l=o[i]=new Array(a),u,d=0;d<a;++d)(u=s[d])&&(l[d]=u);l.sort(t)}return new Nt(o,this._parents).order()}function yg(e,t){return e<t?-1:e>t?1:e>=t?0:NaN}function wg(){var e=arguments[0];return arguments[0]=this,e.apply(null,arguments),this}function bg(){return Array.from(this)}function xg(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r=e[t],o=0,i=r.length;o<i;++o){var s=r[o];if(s)return s}return null}function _g(){let e=0;for(const t of this)++e;return e}function kg(){return!this.node()}function $g(e){for(var t=this._groups,n=0,r=t.length;n<r;++n)for(var o=t[n],i=0,s=o.length,a;i<s;++i)(a=o[i])&&e.call(a,a.__data__,i,o);return this}function Cg(e){return function(){this.removeAttribute(e)}}function Sg(e){return function(){this.removeAttributeNS(e.space,e.local)}}function Eg(e,t){return function(){this.setAttribute(e,t)}}function Ng(e,t){return function(){this.setAttributeNS(e.space,e.local,t)}}function Pg(e,t){return function(){var n=t.apply(this,arguments);n==null?this.removeAttribute(e):this.setAttribute(e,n)}}function zg(e,t){return function(){var n=t.apply(this,arguments);n==null?this.removeAttributeNS(e.space,e.local):this.setAttributeNS(e.space,e.local,n)}}function Lg(e,t){var n=Fo(e);if(arguments.length<2){var r=this.node();return n.local?r.getAttributeNS(n.space,n.local):r.getAttribute(n)}return this.each((t==null?n.local?Sg:Cg:typeof t=="function"?n.local?zg:Pg:n.local?Ng:Eg)(n,t))}function Rl(e){return e.ownerDocument&&e.ownerDocument.defaultView||e.document&&e||e.defaultView}function Dg(e){return function(){this.style.removeProperty(e)}}function Hg(e,t,n){return function(){this.style.setProperty(e,t,n)}}function Mg(e,t,n){return function(){var r=t.apply(this,arguments);r==null?this.style.removeProperty(e):this.style.setProperty(e,r,n)}}function Tg(e,t,n){return arguments.length>1?this.each((t==null?Dg:typeof t=="function"?Mg:Hg)(e,t,n??"")):$r(this.node(),e)}function $r(e,t){return e.style.getPropertyValue(t)||Rl(e).getComputedStyle(e,null).getPropertyValue(t)}function Vg(e){return function(){delete this[e]}}function Og(e,t){return function(){this[e]=t}}function Ag(e,t){return function(){var n=t.apply(this,arguments);n==null?delete this[e]:this[e]=n}}function Ig(e,t){return arguments.length>1?this.each((t==null?Vg:typeof t=="function"?Ag:Og)(e,t)):this.node()[e]}function Bl(e){return e.trim().split(/^|\s+/)}function ws(e){return e.classList||new Kl(e)}function Kl(e){this._node=e,this._names=Bl(e.getAttribute("class")||"")}Kl.prototype={add:function(e){var t=this._names.indexOf(e);t<0&&(this._names.push(e),this._node.setAttribute("class",this._names.join(" ")))},remove:function(e){var t=this._names.indexOf(e);t>=0&&(this._names.splice(t,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(e){return this._names.indexOf(e)>=0}};function Xl(e,t){for(var n=ws(e),r=-1,o=t.length;++r<o;)n.add(t[r])}function Yl(e,t){for(var n=ws(e),r=-1,o=t.length;++r<o;)n.remove(t[r])}function qg(e){return function(){Xl(this,e)}}function Zg(e){return function(){Yl(this,e)}}function Rg(e,t){return function(){(t.apply(this,arguments)?Xl:Yl)(this,e)}}function Bg(e,t){var n=Bl(e+"");if(arguments.length<2){for(var r=ws(this.node()),o=-1,i=n.length;++o<i;)if(!r.contains(n[o]))return!1;return!0}return this.each((typeof t=="function"?Rg:t?qg:Zg)(n,t))}function Kg(){this.textContent=""}function Xg(e){return function(){this.textContent=e}}function Yg(e){return function(){var t=e.apply(this,arguments);this.textContent=t??""}}function jg(e){return arguments.length?this.each(e==null?Kg:(typeof e=="function"?Yg:Xg)(e)):this.node().textContent}function Wg(){this.innerHTML=""}function Fg(e){return function(){this.innerHTML=e}}function Gg(e){return function(){var t=e.apply(this,arguments);this.innerHTML=t??""}}function Ug(e){return arguments.length?this.each(e==null?Wg:(typeof e=="function"?Gg:Fg)(e)):this.node().innerHTML}function Jg(){this.nextSibling&&this.parentNode.appendChild(this)}function Qg(){return this.each(Jg)}function eh(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function th(){return this.each(eh)}function nh(e){var t=typeof e=="function"?e:Ol(e);return this.select(function(){return this.appendChild(t.apply(this,arguments))})}function rh(){return null}function oh(e,t){var n=typeof e=="function"?e:Ol(e),r=t==null?rh:typeof t=="function"?t:ys(t);return this.select(function(){return this.insertBefore(n.apply(this,arguments),r.apply(this,arguments)||null)})}function ih(){var e=this.parentNode;e&&e.removeChild(this)}function sh(){return this.each(ih)}function ah(){var e=this.cloneNode(!1),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function lh(){var e=this.cloneNode(!0),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function uh(e){return this.select(e?lh:ah)}function ch(e){return arguments.length?this.property("__data__",e):this.node().__data__}function dh(e){return function(t){e.call(this,t,this.__data__)}}function fh(e){return e.trim().split(/^|\s+/).map(function(t){var n="",r=t.indexOf(".");return r>=0&&(n=t.slice(r+1),t=t.slice(0,r)),{type:t,name:n}})}function ph(e){return function(){var t=this.__on;if(t){for(var n=0,r=-1,o=t.length,i;n<o;++n)i=t[n],(!e.type||i.type===e.type)&&i.name===e.name?this.removeEventListener(i.type,i.listener,i.options):t[++r]=i;++r?t.length=r:delete this.__on}}}function gh(e,t,n){return function(){var r=this.__on,o,i=dh(t);if(r){for(var s=0,a=r.length;s<a;++s)if((o=r[s]).type===e.type&&o.name===e.name){this.removeEventListener(o.type,o.listener,o.options),this.addEventListener(o.type,o.listener=i,o.options=n),o.value=t;return}}this.addEventListener(e.type,i,n),o={type:e.type,name:e.name,value:t,listener:i,options:n},r?r.push(o):this.__on=[o]}}function hh(e,t,n){var r=fh(e+""),o,i=r.length,s;if(arguments.length<2){var a=this.node().__on;if(a){for(var l=0,u=a.length,d;l<u;++l)for(o=0,d=a[l];o<i;++o)if((s=r[o]).type===d.type&&s.name===d.name)return d.value}return}for(a=t?gh:ph,o=0;o<i;++o)this.each(a(r[o],t,n));return this}function jl(e,t,n){var r=Rl(e),o=r.CustomEvent;typeof o=="function"?o=new o(t,n):(o=r.document.createEvent("Event"),n?(o.initEvent(t,n.bubbles,n.cancelable),o.detail=n.detail):o.initEvent(t,!1,!1)),e.dispatchEvent(o)}function vh(e,t){return function(){return jl(this,e,t)}}function mh(e,t){return function(){return jl(this,e,t.apply(this,arguments))}}function yh(e,t){return this.each((typeof t=="function"?mh:vh)(e,t))}function*wh(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r=e[t],o=0,i=r.length,s;o<i;++o)(s=r[o])&&(yield s)}var Wl=[null];function Nt(e,t){this._groups=e,this._parents=t}function Qr(){return new Nt([[document.documentElement]],Wl)}function bh(){return this}Nt.prototype=Qr.prototype={constructor:Nt,select:Yp,selectAll:Gp,selectChild:eg,selectChildren:og,filter:ig,data:dg,enter:sg,exit:pg,join:gg,merge:hg,selection:bh,order:vg,sort:mg,call:wg,nodes:bg,node:xg,size:_g,empty:kg,each:$g,attr:Lg,style:Tg,property:Ig,classed:Bg,text:jg,html:Ug,raise:Qg,lower:th,append:nh,insert:oh,remove:sh,clone:uh,datum:ch,on:hh,dispatch:yh,[Symbol.iterator]:wh};function Vt(e){return typeof e=="string"?new Nt([[document.querySelector(e)]],[document.documentElement]):new Nt([[e]],Wl)}function xh(e){let t;for(;t=e.sourceEvent;)e=t;return e}function Kt(e,t){if(e=xh(e),t===void 0&&(t=e.currentTarget),t){var n=t.ownerSVGElement||t;if(n.createSVGPoint){var r=n.createSVGPoint();return r.x=e.clientX,r.y=e.clientY,r=r.matrixTransform(t.getScreenCTM().inverse()),[r.x,r.y]}if(t.getBoundingClientRect){var o=t.getBoundingClientRect();return[e.clientX-o.left-t.clientLeft,e.clientY-o.top-t.clientTop]}}return[e.pageX,e.pageY]}const _h={passive:!1},eo={capture:!0,passive:!1};function bs(e){e.stopImmediatePropagation()}function Cr(e){e.preventDefault(),e.stopImmediatePropagation()}function Fl(e){var t=e.document.documentElement,n=Vt(e).on("dragstart.drag",Cr,eo);"onselectstart"in t?n.on("selectstart.drag",Cr,eo):(t.__noselect=t.style.MozUserSelect,t.style.MozUserSelect="none")}function Gl(e,t){var n=e.document.documentElement,r=Vt(e).on("dragstart.drag",null);t&&(r.on("click.drag",Cr,eo),setTimeout(function(){r.on("click.drag",null)},0)),"onselectstart"in n?r.on("selectstart.drag",null):(n.style.MozUserSelect=n.__noselect,delete n.__noselect)}const Uo=e=>()=>e;function xs(e,{sourceEvent:t,subject:n,target:r,identifier:o,active:i,x:s,y:a,dx:l,dy:u,dispatch:d}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},subject:{value:n,enumerable:!0,configurable:!0},target:{value:r,enumerable:!0,configurable:!0},identifier:{value:o,enumerable:!0,configurable:!0},active:{value:i,enumerable:!0,configurable:!0},x:{value:s,enumerable:!0,configurable:!0},y:{value:a,enumerable:!0,configurable:!0},dx:{value:l,enumerable:!0,configurable:!0},dy:{value:u,enumerable:!0,configurable:!0},_:{value:d}})}xs.prototype.on=function(){var e=this._.on.apply(this._,arguments);return e===this._?this:e};function kh(e){return!e.ctrlKey&&!e.button}function $h(){return this.parentNode}function Ch(e,t){return t??{x:e.x,y:e.y}}function Sh(){return navigator.maxTouchPoints||"ontouchstart"in this}function Eh(){var e=kh,t=$h,n=Ch,r=Sh,o={},i=jo("start","drag","end"),s=0,a,l,u,d,p=0;function f(S){S.on("mousedown.drag",g).filter(r).on("touchstart.drag",w).on("touchmove.drag",b,_h).on("touchend.drag touchcancel.drag",k).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function g(S,E){if(!(d||!e.call(this,S,E))){var L=x(this,t.call(this,S,E),S,E,"mouse");L&&(Vt(S.view).on("mousemove.drag",h,eo).on("mouseup.drag",v,eo),Fl(S.view),bs(S),u=!1,a=S.clientX,l=S.clientY,L("start",S))}}function h(S){if(Cr(S),!u){var E=S.clientX-a,L=S.clientY-l;u=E*E+L*L>p}o.mouse("drag",S)}function v(S){Vt(S.view).on("mousemove.drag mouseup.drag",null),Gl(S.view,u),Cr(S),o.mouse("end",S)}function w(S,E){if(e.call(this,S,E)){var L=S.changedTouches,D=t.call(this,S,E),q=L.length,B,U;for(B=0;B<q;++B)(U=x(this,D,S,E,L[B].identifier,L[B]))&&(bs(S),U("start",S,L[B]))}}function b(S){var E=S.changedTouches,L=E.length,D,q;for(D=0;D<L;++D)(q=o[E[D].identifier])&&(Cr(S),q("drag",S,E[D]))}function k(S){var E=S.changedTouches,L=E.length,D,q;for(d&&clearTimeout(d),d=setTimeout(function(){d=null},500),D=0;D<L;++D)(q=o[E[D].identifier])&&(bs(S),q("end",S,E[D]))}function x(S,E,L,D,q,B){var U=i.copy(),O=Kt(B||L,E),$,C,_;if((_=n.call(S,new xs("beforestart",{sourceEvent:L,target:f,identifier:q,active:s,x:O[0],y:O[1],dx:0,dy:0,dispatch:U}),D))!=null)return $=_.x-O[0]||0,C=_.y-O[1]||0,function P(N,T,Z){var X=O,M;switch(N){case"start":o[q]=P,M=s++;break;case"end":delete o[q],--s;case"drag":O=Kt(Z||T,E),M=s;break}U.call(N,S,new xs(N,{sourceEvent:T,subject:_,target:f,identifier:q,active:M,x:O[0]+$,y:O[1]+C,dx:O[0]-X[0],dy:O[1]-X[1],dispatch:U}),D)}}return f.filter=function(S){return arguments.length?(e=typeof S=="function"?S:Uo(!!S),f):e},f.container=function(S){return arguments.length?(t=typeof S=="function"?S:Uo(S),f):t},f.subject=function(S){return arguments.length?(n=typeof S=="function"?S:Uo(S),f):n},f.touchable=function(S){return arguments.length?(r=typeof S=="function"?S:Uo(!!S),f):r},f.on=function(){var S=i.on.apply(i,arguments);return S===i?f:S},f.clickDistance=function(S){return arguments.length?(p=(S=+S)*S,f):Math.sqrt(p)},f}function _s(e,t,n){e.prototype=t.prototype=n,n.constructor=e}function Ul(e,t){var n=Object.create(e.prototype);for(var r in t)n[r]=t[r];return n}function to(){}var no=.7,Jo=1/no,Sr="\\s*([+-]?\\d+)\\s*",ro="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",on="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",Nh=/^#([0-9a-f]{3,8})$/,Ph=new RegExp(`^rgb\\(${Sr},${Sr},${Sr}\\)$`),zh=new RegExp(`^rgb\\(${on},${on},${on}\\)$`),Lh=new RegExp(`^rgba\\(${Sr},${Sr},${Sr},${ro}\\)$`),Dh=new RegExp(`^rgba\\(${on},${on},${on},${ro}\\)$`),Hh=new RegExp(`^hsl\\(${ro},${on},${on}\\)$`),Mh=new RegExp(`^hsla\\(${ro},${on},${on},${ro}\\)$`),Jl={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};_s(to,tr,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:Ql,formatHex:Ql,formatHex8:Th,formatHsl:Vh,formatRgb:eu,toString:eu});function Ql(){return this.rgb().formatHex()}function Th(){return this.rgb().formatHex8()}function Vh(){return iu(this).formatHsl()}function eu(){return this.rgb().formatRgb()}function tr(e){var t,n;return e=(e+"").trim().toLowerCase(),(t=Nh.exec(e))?(n=t[1].length,t=parseInt(t[1],16),n===6?tu(t):n===3?new xt(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):n===8?Qo(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):n===4?Qo(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=Ph.exec(e))?new xt(t[1],t[2],t[3],1):(t=zh.exec(e))?new xt(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=Lh.exec(e))?Qo(t[1],t[2],t[3],t[4]):(t=Dh.exec(e))?Qo(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=Hh.exec(e))?ou(t[1],t[2]/100,t[3]/100,1):(t=Mh.exec(e))?ou(t[1],t[2]/100,t[3]/100,t[4]):Jl.hasOwnProperty(e)?tu(Jl[e]):e==="transparent"?new xt(NaN,NaN,NaN,0):null}function tu(e){return new xt(e>>16&255,e>>8&255,e&255,1)}function Qo(e,t,n,r){return r<=0&&(e=t=n=NaN),new xt(e,t,n,r)}function Oh(e){return e instanceof to||(e=tr(e)),e?(e=e.rgb(),new xt(e.r,e.g,e.b,e.opacity)):new xt}function ks(e,t,n,r){return arguments.length===1?Oh(e):new xt(e,t,n,r??1)}function xt(e,t,n,r){this.r=+e,this.g=+t,this.b=+n,this.opacity=+r}_s(xt,ks,Ul(to,{brighter(e){return e=e==null?Jo:Math.pow(Jo,e),new xt(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?no:Math.pow(no,e),new xt(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new xt(nr(this.r),nr(this.g),nr(this.b),ei(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:nu,formatHex:nu,formatHex8:Ah,formatRgb:ru,toString:ru}));function nu(){return`#${rr(this.r)}${rr(this.g)}${rr(this.b)}`}function Ah(){return`#${rr(this.r)}${rr(this.g)}${rr(this.b)}${rr((isNaN(this.opacity)?1:this.opacity)*255)}`}function ru(){const e=ei(this.opacity);return`${e===1?"rgb(":"rgba("}${nr(this.r)}, ${nr(this.g)}, ${nr(this.b)}${e===1?")":`, ${e})`}`}function ei(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function nr(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function rr(e){return e=nr(e),(e<16?"0":"")+e.toString(16)}function ou(e,t,n,r){return r<=0?e=t=n=NaN:n<=0||n>=1?e=t=NaN:t<=0&&(e=NaN),new Xt(e,t,n,r)}function iu(e){if(e instanceof Xt)return new Xt(e.h,e.s,e.l,e.opacity);if(e instanceof to||(e=tr(e)),!e)return new Xt;if(e instanceof Xt)return e;e=e.rgb();var t=e.r/255,n=e.g/255,r=e.b/255,o=Math.min(t,n,r),i=Math.max(t,n,r),s=NaN,a=i-o,l=(i+o)/2;return a?(t===i?s=(n-r)/a+(n<r)*6:n===i?s=(r-t)/a+2:s=(t-n)/a+4,a/=l<.5?i+o:2-i-o,s*=60):a=l>0&&l<1?0:s,new Xt(s,a,l,e.opacity)}function Ih(e,t,n,r){return arguments.length===1?iu(e):new Xt(e,t,n,r??1)}function Xt(e,t,n,r){this.h=+e,this.s=+t,this.l=+n,this.opacity=+r}_s(Xt,Ih,Ul(to,{brighter(e){return e=e==null?Jo:Math.pow(Jo,e),new Xt(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?no:Math.pow(no,e),new Xt(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,n=this.l,r=n+(n<.5?n:1-n)*t,o=2*n-r;return new xt($s(e>=240?e-240:e+120,o,r),$s(e,o,r),$s(e<120?e+240:e-120,o,r),this.opacity)},clamp(){return new Xt(su(this.h),ti(this.s),ti(this.l),ei(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=ei(this.opacity);return`${e===1?"hsl(":"hsla("}${su(this.h)}, ${ti(this.s)*100}%, ${ti(this.l)*100}%${e===1?")":`, ${e})`}`}}));function su(e){return e=(e||0)%360,e<0?e+360:e}function ti(e){return Math.max(0,Math.min(1,e||0))}function $s(e,t,n){return(e<60?t+(n-t)*e/60:e<180?n:e<240?t+(n-t)*(240-e)/60:t)*255}const Cs=e=>()=>e;function qh(e,t){return function(n){return e+n*t}}function Zh(e,t,n){return e=Math.pow(e,n),t=Math.pow(t,n)-e,n=1/n,function(r){return Math.pow(e+r*t,n)}}function Rh(e){return(e=+e)==1?au:function(t,n){return n-t?Zh(t,n,e):Cs(isNaN(t)?n:t)}}function au(e,t){var n=t-e;return n?qh(e,n):Cs(isNaN(e)?t:e)}const ni=function e(t){var n=Rh(t);function r(o,i){var s=n((o=ks(o)).r,(i=ks(i)).r),a=n(o.g,i.g),l=n(o.b,i.b),u=au(o.opacity,i.opacity);return function(d){return o.r=s(d),o.g=a(d),o.b=l(d),o.opacity=u(d),o+""}}return r.gamma=e,r}(1);function Bh(e,t){t||(t=[]);var n=e?Math.min(t.length,e.length):0,r=t.slice(),o;return function(i){for(o=0;o<n;++o)r[o]=e[o]*(1-i)+t[o]*i;return r}}function Kh(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}function Xh(e,t){var n=t?t.length:0,r=e?Math.min(n,e.length):0,o=new Array(r),i=new Array(n),s;for(s=0;s<r;++s)o[s]=oo(e[s],t[s]);for(;s<n;++s)i[s]=t[s];return function(a){for(s=0;s<r;++s)i[s]=o[s](a);return i}}function Yh(e,t){var n=new Date;return e=+e,t=+t,function(r){return n.setTime(e*(1-r)+t*r),n}}function sn(e,t){return e=+e,t=+t,function(n){return e*(1-n)+t*n}}function jh(e,t){var n={},r={},o;(e===null||typeof e!="object")&&(e={}),(t===null||typeof t!="object")&&(t={});for(o in t)o in e?n[o]=oo(e[o],t[o]):r[o]=t[o];return function(i){for(o in n)r[o]=n[o](i);return r}}var Ss=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Es=new RegExp(Ss.source,"g");function Wh(e){return function(){return e}}function Fh(e){return function(t){return e(t)+""}}function lu(e,t){var n=Ss.lastIndex=Es.lastIndex=0,r,o,i,s=-1,a=[],l=[];for(e=e+"",t=t+"";(r=Ss.exec(e))&&(o=Es.exec(t));)(i=o.index)>n&&(i=t.slice(n,i),a[s]?a[s]+=i:a[++s]=i),(r=r[0])===(o=o[0])?a[s]?a[s]+=o:a[++s]=o:(a[++s]=null,l.push({i:s,x:sn(r,o)})),n=Es.lastIndex;return n<t.length&&(i=t.slice(n),a[s]?a[s]+=i:a[++s]=i),a.length<2?l[0]?Fh(l[0].x):Wh(t):(t=l.length,function(u){for(var d=0,p;d<t;++d)a[(p=l[d]).i]=p.x(u);return a.join("")})}function oo(e,t){var n=typeof t,r;return t==null||n==="boolean"?Cs(t):(n==="number"?sn:n==="string"?(r=tr(t))?(t=r,ni):lu:t instanceof tr?ni:t instanceof Date?Yh:Kh(t)?Bh:Array.isArray(t)?Xh:typeof t.valueOf!="function"&&typeof t.toString!="function"||isNaN(t)?jh:sn)(e,t)}var uu=180/Math.PI,cu={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function du(e,t,n,r,o,i){var s,a,l;return(s=Math.sqrt(e*e+t*t))&&(e/=s,t/=s),(l=e*n+t*r)&&(n-=e*l,r-=t*l),(a=Math.sqrt(n*n+r*r))&&(n/=a,r/=a,l/=a),e*r<t*n&&(e=-e,t=-t,l=-l,s=-s),{translateX:o,translateY:i,rotate:Math.atan2(t,e)*uu,skewX:Math.atan(l)*uu,scaleX:s,scaleY:a}}var ri;function Gh(e){const t=new(typeof DOMMatrix=="function"?DOMMatrix:WebKitCSSMatrix)(e+"");return t.isIdentity?cu:du(t.a,t.b,t.c,t.d,t.e,t.f)}function Uh(e){return e==null||(ri||(ri=document.createElementNS("http://www.w3.org/2000/svg","g")),ri.setAttribute("transform",e),!(e=ri.transform.baseVal.consolidate()))?cu:(e=e.matrix,du(e.a,e.b,e.c,e.d,e.e,e.f))}function fu(e,t,n,r){function o(u){return u.length?u.pop()+" ":""}function i(u,d,p,f,g,h){if(u!==p||d!==f){var v=g.push("translate(",null,t,null,n);h.push({i:v-4,x:sn(u,p)},{i:v-2,x:sn(d,f)})}else(p||f)&&g.push("translate("+p+t+f+n)}function s(u,d,p,f){u!==d?(u-d>180?d+=360:d-u>180&&(u+=360),f.push({i:p.push(o(p)+"rotate(",null,r)-2,x:sn(u,d)})):d&&p.push(o(p)+"rotate("+d+r)}function a(u,d,p,f){u!==d?f.push({i:p.push(o(p)+"skewX(",null,r)-2,x:sn(u,d)}):d&&p.push(o(p)+"skewX("+d+r)}function l(u,d,p,f,g,h){if(u!==p||d!==f){var v=g.push(o(g)+"scale(",null,",",null,")");h.push({i:v-4,x:sn(u,p)},{i:v-2,x:sn(d,f)})}else(p!==1||f!==1)&&g.push(o(g)+"scale("+p+","+f+")")}return function(u,d){var p=[],f=[];return u=e(u),d=e(d),i(u.translateX,u.translateY,d.translateX,d.translateY,p,f),s(u.rotate,d.rotate,p,f),a(u.skewX,d.skewX,p,f),l(u.scaleX,u.scaleY,d.scaleX,d.scaleY,p,f),u=d=null,function(g){for(var h=-1,v=f.length,w;++h<v;)p[(w=f[h]).i]=w.x(g);return p.join("")}}}var Jh=fu(Gh,"px, ","px)","deg)"),Qh=fu(Uh,", ",")",")"),ev=1e-12;function pu(e){return((e=Math.exp(e))+1/e)/2}function tv(e){return((e=Math.exp(e))-1/e)/2}function nv(e){return((e=Math.exp(2*e))-1)/(e+1)}const oi=function e(t,n,r){function o(i,s){var a=i[0],l=i[1],u=i[2],d=s[0],p=s[1],f=s[2],g=d-a,h=p-l,v=g*g+h*h,w,b;if(v<ev)b=Math.log(f/u)/t,w=function(D){return[a+D*g,l+D*h,u*Math.exp(t*D*b)]};else{var k=Math.sqrt(v),x=(f*f-u*u+r*v)/(2*u*n*k),S=(f*f-u*u-r*v)/(2*f*n*k),E=Math.log(Math.sqrt(x*x+1)-x),L=Math.log(Math.sqrt(S*S+1)-S);b=(L-E)/t,w=function(D){var q=D*b,B=pu(E),U=u/(n*k)*(B*nv(t*q+E)-tv(E));return[a+U*g,l+U*h,u*B/pu(t*q+E)]}}return w.duration=b*1e3*t/Math.SQRT2,w}return o.rho=function(i){var s=Math.max(.001,+i),a=s*s,l=a*a;return e(s,a,l)},o}(Math.SQRT2,2,4);var Er=0,io=0,so=0,gu=1e3,ii,ao,si=0,or=0,ai=0,lo=typeof performance=="object"&&performance.now?performance:Date,hu=typeof window=="object"&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(e){setTimeout(e,17)};function Ns(){return or||(hu(rv),or=lo.now()+ai)}function rv(){or=0}function li(){this._call=this._time=this._next=null}li.prototype=vu.prototype={constructor:li,restart:function(e,t,n){if(typeof e!="function")throw new TypeError("callback is not a function");n=(n==null?Ns():+n)+(t==null?0:+t),!this._next&&ao!==this&&(ao?ao._next=this:ii=this,ao=this),this._call=e,this._time=n,Ps()},stop:function(){this._call&&(this._call=null,this._time=1/0,Ps())}};function vu(e,t,n){var r=new li;return r.restart(e,t,n),r}function ov(){Ns(),++Er;for(var e=ii,t;e;)(t=or-e._time)>=0&&e._call.call(void 0,t),e=e._next;--Er}function mu(){or=(si=lo.now())+ai,Er=io=0;try{ov()}finally{Er=0,sv(),or=0}}function iv(){var e=lo.now(),t=e-si;t>gu&&(ai-=t,si=e)}function sv(){for(var e,t=ii,n,r=1/0;t;)t._call?(r>t._time&&(r=t._time),e=t,t=t._next):(n=t._next,t._next=null,t=e?e._next=n:ii=n);ao=e,Ps(r)}function Ps(e){if(!Er){io&&(io=clearTimeout(io));var t=e-or;t>24?(e<1/0&&(io=setTimeout(mu,e-lo.now()-ai)),so&&(so=clearInterval(so))):(so||(si=lo.now(),so=setInterval(iv,gu)),Er=1,hu(mu))}}function yu(e,t,n){var r=new li;return t=t==null?0:+t,r.restart(o=>{r.stop(),e(o+t)},t,n),r}var av=jo("start","end","cancel","interrupt"),lv=[],wu=0,bu=1,zs=2,ui=3,xu=4,Ls=5,ci=6;function di(e,t,n,r,o,i){var s=e.__transition;if(!s)e.__transition={};else if(n in s)return;uv(e,n,{name:t,index:r,group:o,on:av,tween:lv,time:i.time,delay:i.delay,duration:i.duration,ease:i.ease,timer:null,state:wu})}function Ds(e,t){var n=Yt(e,t);if(n.state>wu)throw new Error("too late; already scheduled");return n}function an(e,t){var n=Yt(e,t);if(n.state>ui)throw new Error("too late; already running");return n}function Yt(e,t){var n=e.__transition;if(!n||!(n=n[t]))throw new Error("transition not found");return n}function uv(e,t,n){var r=e.__transition,o;r[t]=n,n.timer=vu(i,0,n.time);function i(u){n.state=bu,n.timer.restart(s,n.delay,n.time),n.delay<=u&&s(u-n.delay)}function s(u){var d,p,f,g;if(n.state!==bu)return l();for(d in r)if(g=r[d],g.name===n.name){if(g.state===ui)return yu(s);g.state===xu?(g.state=ci,g.timer.stop(),g.on.call("interrupt",e,e.__data__,g.index,g.group),delete r[d]):+d<t&&(g.state=ci,g.timer.stop(),g.on.call("cancel",e,e.__data__,g.index,g.group),delete r[d])}if(yu(function(){n.state===ui&&(n.state=xu,n.timer.restart(a,n.delay,n.time),a(u))}),n.state=zs,n.on.call("start",e,e.__data__,n.index,n.group),n.state===zs){for(n.state=ui,o=new Array(f=n.tween.length),d=0,p=-1;d<f;++d)(g=n.tween[d].value.call(e,e.__data__,n.index,n.group))&&(o[++p]=g);o.length=p+1}}function a(u){for(var d=u<n.duration?n.ease.call(null,u/n.duration):(n.timer.restart(l),n.state=Ls,1),p=-1,f=o.length;++p<f;)o[p].call(e,d);n.state===Ls&&(n.on.call("end",e,e.__data__,n.index,n.group),l())}function l(){n.state=ci,n.timer.stop(),delete r[t];for(var u in r)return;delete e.__transition}}function fi(e,t){var n=e.__transition,r,o,i=!0,s;if(n){t=t==null?null:t+"";for(s in n){if((r=n[s]).name!==t){i=!1;continue}o=r.state>zs&&r.state<Ls,r.state=ci,r.timer.stop(),r.on.call(o?"interrupt":"cancel",e,e.__data__,r.index,r.group),delete n[s]}i&&delete e.__transition}}function cv(e){return this.each(function(){fi(this,e)})}function dv(e,t){var n,r;return function(){var o=an(this,e),i=o.tween;if(i!==n){r=n=i;for(var s=0,a=r.length;s<a;++s)if(r[s].name===t){r=r.slice(),r.splice(s,1);break}}o.tween=r}}function fv(e,t,n){var r,o;if(typeof n!="function")throw new Error;return function(){var i=an(this,e),s=i.tween;if(s!==r){o=(r=s).slice();for(var a={name:t,value:n},l=0,u=o.length;l<u;++l)if(o[l].name===t){o[l]=a;break}l===u&&o.push(a)}i.tween=o}}function pv(e,t){var n=this._id;if(e+="",arguments.length<2){for(var r=Yt(this.node(),n).tween,o=0,i=r.length,s;o<i;++o)if((s=r[o]).name===e)return s.value;return null}return this.each((t==null?dv:fv)(n,e,t))}function Hs(e,t,n){var r=e._id;return e.each(function(){var o=an(this,r);(o.value||(o.value={}))[t]=n.apply(this,arguments)}),function(o){return Yt(o,r).value[t]}}function _u(e,t){var n;return(typeof t=="number"?sn:t instanceof tr?ni:(n=tr(t))?(t=n,ni):lu)(e,t)}function gv(e){return function(){this.removeAttribute(e)}}function hv(e){return function(){this.removeAttributeNS(e.space,e.local)}}function vv(e,t,n){var r,o=n+"",i;return function(){var s=this.getAttribute(e);return s===o?null:s===r?i:i=t(r=s,n)}}function mv(e,t,n){var r,o=n+"",i;return function(){var s=this.getAttributeNS(e.space,e.local);return s===o?null:s===r?i:i=t(r=s,n)}}function yv(e,t,n){var r,o,i;return function(){var s,a=n(this),l;return a==null?void this.removeAttribute(e):(s=this.getAttribute(e),l=a+"",s===l?null:s===r&&l===o?i:(o=l,i=t(r=s,a)))}}function wv(e,t,n){var r,o,i;return function(){var s,a=n(this),l;return a==null?void this.removeAttributeNS(e.space,e.local):(s=this.getAttributeNS(e.space,e.local),l=a+"",s===l?null:s===r&&l===o?i:(o=l,i=t(r=s,a)))}}function bv(e,t){var n=Fo(e),r=n==="transform"?Qh:_u;return this.attrTween(e,typeof t=="function"?(n.local?wv:yv)(n,r,Hs(this,"attr."+e,t)):t==null?(n.local?hv:gv)(n):(n.local?mv:vv)(n,r,t))}function xv(e,t){return function(n){this.setAttribute(e,t.call(this,n))}}function _v(e,t){return function(n){this.setAttributeNS(e.space,e.local,t.call(this,n))}}function kv(e,t){var n,r;function o(){var i=t.apply(this,arguments);return i!==r&&(n=(r=i)&&_v(e,i)),n}return o._value=t,o}function $v(e,t){var n,r;function o(){var i=t.apply(this,arguments);return i!==r&&(n=(r=i)&&xv(e,i)),n}return o._value=t,o}function Cv(e,t){var n="attr."+e;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(t==null)return this.tween(n,null);if(typeof t!="function")throw new Error;var r=Fo(e);return this.tween(n,(r.local?kv:$v)(r,t))}function Sv(e,t){return function(){Ds(this,e).delay=+t.apply(this,arguments)}}function Ev(e,t){return t=+t,function(){Ds(this,e).delay=t}}function Nv(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?Sv:Ev)(t,e)):Yt(this.node(),t).delay}function Pv(e,t){return function(){an(this,e).duration=+t.apply(this,arguments)}}function zv(e,t){return t=+t,function(){an(this,e).duration=t}}function Lv(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?Pv:zv)(t,e)):Yt(this.node(),t).duration}function Dv(e,t){if(typeof t!="function")throw new Error;return function(){an(this,e).ease=t}}function Hv(e){var t=this._id;return arguments.length?this.each(Dv(t,e)):Yt(this.node(),t).ease}function Mv(e,t){return function(){var n=t.apply(this,arguments);if(typeof n!="function")throw new Error;an(this,e).ease=n}}function Tv(e){if(typeof e!="function")throw new Error;return this.each(Mv(this._id,e))}function Vv(e){typeof e!="function"&&(e=Il(e));for(var t=this._groups,n=t.length,r=new Array(n),o=0;o<n;++o)for(var i=t[o],s=i.length,a=r[o]=[],l,u=0;u<s;++u)(l=i[u])&&e.call(l,l.__data__,u,i)&&a.push(l);return new xn(r,this._parents,this._name,this._id)}function Ov(e){if(e._id!==this._id)throw new Error;for(var t=this._groups,n=e._groups,r=t.length,o=n.length,i=Math.min(r,o),s=new Array(r),a=0;a<i;++a)for(var l=t[a],u=n[a],d=l.length,p=s[a]=new Array(d),f,g=0;g<d;++g)(f=l[g]||u[g])&&(p[g]=f);for(;a<r;++a)s[a]=t[a];return new xn(s,this._parents,this._name,this._id)}function Av(e){return(e+"").trim().split(/^|\s+/).every(function(t){var n=t.indexOf(".");return n>=0&&(t=t.slice(0,n)),!t||t==="start"})}function Iv(e,t,n){var r,o,i=Av(t)?Ds:an;return function(){var s=i(this,e),a=s.on;a!==r&&(o=(r=a).copy()).on(t,n),s.on=o}}function qv(e,t){var n=this._id;return arguments.length<2?Yt(this.node(),n).on.on(e):this.each(Iv(n,e,t))}function Zv(e){return function(){var t=this.parentNode;for(var n in this.__transition)if(+n!==e)return;t&&t.removeChild(this)}}function Rv(){return this.on("end.remove",Zv(this._id))}function Bv(e){var t=this._name,n=this._id;typeof e!="function"&&(e=ys(e));for(var r=this._groups,o=r.length,i=new Array(o),s=0;s<o;++s)for(var a=r[s],l=a.length,u=i[s]=new Array(l),d,p,f=0;f<l;++f)(d=a[f])&&(p=e.call(d,d.__data__,f,a))&&("__data__"in d&&(p.__data__=d.__data__),u[f]=p,di(u[f],t,n,f,u,Yt(d,n)));return new xn(i,this._parents,t,n)}function Kv(e){var t=this._name,n=this._id;typeof e!="function"&&(e=Al(e));for(var r=this._groups,o=r.length,i=[],s=[],a=0;a<o;++a)for(var l=r[a],u=l.length,d,p=0;p<u;++p)if(d=l[p]){for(var f=e.call(d,d.__data__,p,l),g,h=Yt(d,n),v=0,w=f.length;v<w;++v)(g=f[v])&&di(g,t,n,v,f,h);i.push(f),s.push(d)}return new xn(i,s,t,n)}var Xv=Qr.prototype.constructor;function Yv(){return new Xv(this._groups,this._parents)}function jv(e,t){var n,r,o;return function(){var i=$r(this,e),s=(this.style.removeProperty(e),$r(this,e));return i===s?null:i===n&&s===r?o:o=t(n=i,r=s)}}function ku(e){return function(){this.style.removeProperty(e)}}function Wv(e,t,n){var r,o=n+"",i;return function(){var s=$r(this,e);return s===o?null:s===r?i:i=t(r=s,n)}}function Fv(e,t,n){var r,o,i;return function(){var s=$r(this,e),a=n(this),l=a+"";return a==null&&(l=a=(this.style.removeProperty(e),$r(this,e))),s===l?null:s===r&&l===o?i:(o=l,i=t(r=s,a))}}function Gv(e,t){var n,r,o,i="style."+t,s="end."+i,a;return function(){var l=an(this,e),u=l.on,d=l.value[i]==null?a||(a=ku(t)):void 0;(u!==n||o!==d)&&(r=(n=u).copy()).on(s,o=d),l.on=r}}function Uv(e,t,n){var r=(e+="")=="transform"?Jh:_u;return t==null?this.styleTween(e,jv(e,r)).on("end.style."+e,ku(e)):typeof t=="function"?this.styleTween(e,Fv(e,r,Hs(this,"style."+e,t))).each(Gv(this._id,e)):this.styleTween(e,Wv(e,r,t),n).on("end.style."+e,null)}function Jv(e,t,n){return function(r){this.style.setProperty(e,t.call(this,r),n)}}function Qv(e,t,n){var r,o;function i(){var s=t.apply(this,arguments);return s!==o&&(r=(o=s)&&Jv(e,s,n)),r}return i._value=t,i}function e1(e,t,n){var r="style."+(e+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(t==null)return this.tween(r,null);if(typeof t!="function")throw new Error;return this.tween(r,Qv(e,t,n??""))}function t1(e){return function(){this.textContent=e}}function n1(e){return function(){var t=e(this);this.textContent=t??""}}function r1(e){return this.tween("text",typeof e=="function"?n1(Hs(this,"text",e)):t1(e==null?"":e+""))}function o1(e){return function(t){this.textContent=e.call(this,t)}}function i1(e){var t,n;function r(){var o=e.apply(this,arguments);return o!==n&&(t=(n=o)&&o1(o)),t}return r._value=e,r}function s1(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(e==null)return this.tween(t,null);if(typeof e!="function")throw new Error;return this.tween(t,i1(e))}function a1(){for(var e=this._name,t=this._id,n=$u(),r=this._groups,o=r.length,i=0;i<o;++i)for(var s=r[i],a=s.length,l,u=0;u<a;++u)if(l=s[u]){var d=Yt(l,t);di(l,e,n,u,s,{time:d.time+d.delay+d.duration,delay:0,duration:d.duration,ease:d.ease})}return new xn(r,this._parents,e,n)}function l1(){var e,t,n=this,r=n._id,o=n.size();return new Promise(function(i,s){var a={value:s},l={value:function(){--o===0&&i()}};n.each(function(){var u=an(this,r),d=u.on;d!==e&&(t=(e=d).copy(),t._.cancel.push(a),t._.interrupt.push(a),t._.end.push(l)),u.on=t}),o===0&&i()})}var u1=0;function xn(e,t,n,r){this._groups=e,this._parents=t,this._name=n,this._id=r}function $u(){return++u1}var _n=Qr.prototype;xn.prototype={constructor:xn,select:Bv,selectAll:Kv,selectChild:_n.selectChild,selectChildren:_n.selectChildren,filter:Vv,merge:Ov,selection:Yv,transition:a1,call:_n.call,nodes:_n.nodes,node:_n.node,size:_n.size,empty:_n.empty,each:_n.each,on:qv,attr:bv,attrTween:Cv,style:Uv,styleTween:e1,text:r1,textTween:s1,remove:Rv,tween:pv,delay:Nv,duration:Lv,ease:Hv,easeVarying:Tv,end:l1,[Symbol.iterator]:_n[Symbol.iterator]};function c1(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}var d1={time:null,delay:0,duration:250,ease:c1};function f1(e,t){for(var n;!(n=e.__transition)||!(n=n[t]);)if(!(e=e.parentNode))throw new Error(`transition ${t} not found`);return n}function p1(e){var t,n;e instanceof xn?(t=e._id,e=e._name):(t=$u(),(n=d1).time=Ns(),e=e==null?null:e+"");for(var r=this._groups,o=r.length,i=0;i<o;++i)for(var s=r[i],a=s.length,l,u=0;u<a;++u)(l=s[u])&&di(l,e,t,u,s,n||f1(l,t));return new xn(r,this._parents,e,t)}Qr.prototype.interrupt=cv,Qr.prototype.transition=p1;const pi=e=>()=>e;function g1(e,{sourceEvent:t,target:n,transform:r,dispatch:o}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},target:{value:n,enumerable:!0,configurable:!0},transform:{value:r,enumerable:!0,configurable:!0},_:{value:o}})}function kn(e,t,n){this.k=e,this.x=t,this.y=n}kn.prototype={constructor:kn,scale:function(e){return e===1?this:new kn(this.k*e,this.x,this.y)},translate:function(e,t){return e===0&t===0?this:new kn(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var gi=new kn(1,0,0);Cu.prototype=kn.prototype;function Cu(e){for(;!e.__zoom;)if(!(e=e.parentNode))return gi;return e.__zoom}function Ms(e){e.stopImmediatePropagation()}function uo(e){e.preventDefault(),e.stopImmediatePropagation()}function h1(e){return(!e.ctrlKey||e.type==="wheel")&&!e.button}function v1(){var e=this;return e instanceof SVGElement?(e=e.ownerSVGElement||e,e.hasAttribute("viewBox")?(e=e.viewBox.baseVal,[[e.x,e.y],[e.x+e.width,e.y+e.height]]):[[0,0],[e.width.baseVal.value,e.height.baseVal.value]]):[[0,0],[e.clientWidth,e.clientHeight]]}function Su(){return this.__zoom||gi}function m1(e){return-e.deltaY*(e.deltaMode===1?.05:e.deltaMode?1:.002)*(e.ctrlKey?10:1)}function y1(){return navigator.maxTouchPoints||"ontouchstart"in this}function w1(e,t,n){var r=e.invertX(t[0][0])-n[0][0],o=e.invertX(t[1][0])-n[1][0],i=e.invertY(t[0][1])-n[0][1],s=e.invertY(t[1][1])-n[1][1];return e.translate(o>r?(r+o)/2:Math.min(0,r)||Math.max(0,o),s>i?(i+s)/2:Math.min(0,i)||Math.max(0,s))}function Eu(){var e=h1,t=v1,n=w1,r=m1,o=y1,i=[0,1/0],s=[[-1/0,-1/0],[1/0,1/0]],a=250,l=oi,u=jo("start","zoom","end"),d,p,f,g=500,h=150,v=0,w=10;function b(_){_.property("__zoom",Su).on("wheel.zoom",q,{passive:!1}).on("mousedown.zoom",B).on("dblclick.zoom",U).filter(o).on("touchstart.zoom",O).on("touchmove.zoom",$).on("touchend.zoom touchcancel.zoom",C).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}b.transform=function(_,P,N,T){var Z=_.selection?_.selection():_;Z.property("__zoom",Su),_!==Z?E(_,P,N,T):Z.interrupt().each(function(){L(this,arguments).event(T).start().zoom(null,typeof P=="function"?P.apply(this,arguments):P).end()})},b.scaleBy=function(_,P,N,T){b.scaleTo(_,function(){var Z=this.__zoom.k,X=typeof P=="function"?P.apply(this,arguments):P;return Z*X},N,T)},b.scaleTo=function(_,P,N,T){b.transform(_,function(){var Z=t.apply(this,arguments),X=this.__zoom,M=N==null?S(Z):typeof N=="function"?N.apply(this,arguments):N,Y=X.invert(M),ee=typeof P=="function"?P.apply(this,arguments):P;return n(x(k(X,ee),M,Y),Z,s)},N,T)},b.translateBy=function(_,P,N,T){b.transform(_,function(){return n(this.__zoom.translate(typeof P=="function"?P.apply(this,arguments):P,typeof N=="function"?N.apply(this,arguments):N),t.apply(this,arguments),s)},null,T)},b.translateTo=function(_,P,N,T,Z){b.transform(_,function(){var X=t.apply(this,arguments),M=this.__zoom,Y=T==null?S(X):typeof T=="function"?T.apply(this,arguments):T;return n(gi.translate(Y[0],Y[1]).scale(M.k).translate(typeof P=="function"?-P.apply(this,arguments):-P,typeof N=="function"?-N.apply(this,arguments):-N),X,s)},T,Z)};function k(_,P){return P=Math.max(i[0],Math.min(i[1],P)),P===_.k?_:new kn(P,_.x,_.y)}function x(_,P,N){var T=P[0]-N[0]*_.k,Z=P[1]-N[1]*_.k;return T===_.x&&Z===_.y?_:new kn(_.k,T,Z)}function S(_){return[(+_[0][0]+ +_[1][0])/2,(+_[0][1]+ +_[1][1])/2]}function E(_,P,N,T){_.on("start.zoom",function(){L(this,arguments).event(T).start()}).on("interrupt.zoom end.zoom",function(){L(this,arguments).event(T).end()}).tween("zoom",function(){var Z=this,X=arguments,M=L(Z,X).event(T),Y=t.apply(Z,X),ee=N==null?S(Y):typeof N=="function"?N.apply(Z,X):N,ne=Math.max(Y[1][0]-Y[0][0],Y[1][1]-Y[0][1]),R=Z.__zoom,W=typeof P=="function"?P.apply(Z,X):P,F=l(R.invert(ee).concat(ne/R.k),W.invert(ee).concat(ne/W.k));return function(ie){if(ie===1)ie=W;else{var G=F(ie),me=ne/G[2];ie=new kn(me,ee[0]-G[0]*me,ee[1]-G[1]*me)}M.zoom(null,ie)}})}function L(_,P,N){return!N&&_.__zooming||new D(_,P)}function D(_,P){this.that=_,this.args=P,this.active=0,this.sourceEvent=null,this.extent=t.apply(_,P),this.taps=0}D.prototype={event:function(_){return _&&(this.sourceEvent=_),this},start:function(){return++this.active===1&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(_,P){return this.mouse&&_!=="mouse"&&(this.mouse[1]=P.invert(this.mouse[0])),this.touch0&&_!=="touch"&&(this.touch0[1]=P.invert(this.touch0[0])),this.touch1&&_!=="touch"&&(this.touch1[1]=P.invert(this.touch1[0])),this.that.__zoom=P,this.emit("zoom"),this},end:function(){return--this.active===0&&(delete this.that.__zooming,this.emit("end")),this},emit:function(_){var P=Vt(this.that).datum();u.call(_,this.that,new g1(_,{sourceEvent:this.sourceEvent,target:b,transform:this.that.__zoom,dispatch:u}),P)}};function q(_,...P){if(!e.apply(this,arguments))return;var N=L(this,P).event(_),T=this.__zoom,Z=Math.max(i[0],Math.min(i[1],T.k*Math.pow(2,r.apply(this,arguments)))),X=Kt(_);if(N.wheel)(N.mouse[0][0]!==X[0]||N.mouse[0][1]!==X[1])&&(N.mouse[1]=T.invert(N.mouse[0]=X)),clearTimeout(N.wheel);else{if(T.k===Z)return;N.mouse=[X,T.invert(X)],fi(this),N.start()}uo(_),N.wheel=setTimeout(M,h),N.zoom("mouse",n(x(k(T,Z),N.mouse[0],N.mouse[1]),N.extent,s));function M(){N.wheel=null,N.end()}}function B(_,...P){if(f||!e.apply(this,arguments))return;var N=_.currentTarget,T=L(this,P,!0).event(_),Z=Vt(_.view).on("mousemove.zoom",ee,!0).on("mouseup.zoom",ne,!0),X=Kt(_,N),M=_.clientX,Y=_.clientY;Fl(_.view),Ms(_),T.mouse=[X,this.__zoom.invert(X)],fi(this),T.start();function ee(R){if(uo(R),!T.moved){var W=R.clientX-M,F=R.clientY-Y;T.moved=W*W+F*F>v}T.event(R).zoom("mouse",n(x(T.that.__zoom,T.mouse[0]=Kt(R,N),T.mouse[1]),T.extent,s))}function ne(R){Z.on("mousemove.zoom mouseup.zoom",null),Gl(R.view,T.moved),uo(R),T.event(R).end()}}function U(_,...P){if(e.apply(this,arguments)){var N=this.__zoom,T=Kt(_.changedTouches?_.changedTouches[0]:_,this),Z=N.invert(T),X=N.k*(_.shiftKey?.5:2),M=n(x(k(N,X),T,Z),t.apply(this,P),s);uo(_),a>0?Vt(this).transition().duration(a).call(E,M,T,_):Vt(this).call(b.transform,M,T,_)}}function O(_,...P){if(e.apply(this,arguments)){var N=_.touches,T=N.length,Z=L(this,P,_.changedTouches.length===T).event(_),X,M,Y,ee;for(Ms(_),M=0;M<T;++M)Y=N[M],ee=Kt(Y,this),ee=[ee,this.__zoom.invert(ee),Y.identifier],Z.touch0?!Z.touch1&&Z.touch0[2]!==ee[2]&&(Z.touch1=ee,Z.taps=0):(Z.touch0=ee,X=!0,Z.taps=1+!!d);d&&(d=clearTimeout(d)),X&&(Z.taps<2&&(p=ee[0],d=setTimeout(function(){d=null},g)),fi(this),Z.start())}}function $(_,...P){if(this.__zooming){var N=L(this,P).event(_),T=_.changedTouches,Z=T.length,X,M,Y,ee;for(uo(_),X=0;X<Z;++X)M=T[X],Y=Kt(M,this),N.touch0&&N.touch0[2]===M.identifier?N.touch0[0]=Y:N.touch1&&N.touch1[2]===M.identifier&&(N.touch1[0]=Y);if(M=N.that.__zoom,N.touch1){var ne=N.touch0[0],R=N.touch0[1],W=N.touch1[0],F=N.touch1[1],ie=(ie=W[0]-ne[0])*ie+(ie=W[1]-ne[1])*ie,G=(G=F[0]-R[0])*G+(G=F[1]-R[1])*G;M=k(M,Math.sqrt(ie/G)),Y=[(ne[0]+W[0])/2,(ne[1]+W[1])/2],ee=[(R[0]+F[0])/2,(R[1]+F[1])/2]}else if(N.touch0)Y=N.touch0[0],ee=N.touch0[1];else return;N.zoom("touch",n(x(M,Y,ee),N.extent,s))}}function C(_,...P){if(this.__zooming){var N=L(this,P).event(_),T=_.changedTouches,Z=T.length,X,M;for(Ms(_),f&&clearTimeout(f),f=setTimeout(function(){f=null},g),X=0;X<Z;++X)M=T[X],N.touch0&&N.touch0[2]===M.identifier?delete N.touch0:N.touch1&&N.touch1[2]===M.identifier&&delete N.touch1;if(N.touch1&&!N.touch0&&(N.touch0=N.touch1,delete N.touch1),N.touch0)N.touch0[1]=this.__zoom.invert(N.touch0[0]);else if(N.end(),N.taps===2&&(M=Kt(M,this),Math.hypot(p[0]-M[0],p[1]-M[1])<w)){var Y=Vt(this).on("dblclick.zoom");Y&&Y.apply(this,arguments)}}}return b.wheelDelta=function(_){return arguments.length?(r=typeof _=="function"?_:pi(+_),b):r},b.filter=function(_){return arguments.length?(e=typeof _=="function"?_:pi(!!_),b):e},b.touchable=function(_){return arguments.length?(o=typeof _=="function"?_:pi(!!_),b):o},b.extent=function(_){return arguments.length?(t=typeof _=="function"?_:pi([[+_[0][0],+_[0][1]],[+_[1][0],+_[1][1]]]),b):t},b.scaleExtent=function(_){return arguments.length?(i[0]=+_[0],i[1]=+_[1],b):[i[0],i[1]]},b.translateExtent=function(_){return arguments.length?(s[0][0]=+_[0][0],s[1][0]=+_[1][0],s[0][1]=+_[0][1],s[1][1]=+_[1][1],b):[[s[0][0],s[0][1]],[s[1][0],s[1][1]]]},b.constrain=function(_){return arguments.length?(n=_,b):n},b.duration=function(_){return arguments.length?(a=+_,b):a},b.interpolate=function(_){return arguments.length?(l=_,b):l},b.on=function(){var _=u.on.apply(u,arguments);return _===u?b:_},b.clickDistance=function(_){return arguments.length?(v=(_=+_)*_,b):Math.sqrt(v)},b.tapDistance=function(_){return arguments.length?(w=+_,b):w},b}const co={error001:()=>"[React Flow]: Seems like you have not used zustand provider as an ancestor. Help: https://reactflow.dev/error#001",error002:()=>"It looks like you've created a new nodeTypes or edgeTypes object. If this wasn't on purpose please define the nodeTypes/edgeTypes outside of the component or memoize them.",error003:e=>`Node type "${e}" not found. Using fallback type "default".`,error004:()=>"The React Flow parent container needs a width and a height to render the graph.",error005:()=>"Only child nodes can use a parent extent.",error006:()=>"Can't create edge. An edge needs a source and a target.",error007:e=>`The old edge with id=${e} does not exist.`,error009:e=>`Marker type "${e}" doesn't exist.`,error008:(e,{id:t,sourceHandle:n,targetHandle:r})=>`Couldn't create edge for ${e} handle id: "${e==="source"?n:r}", edge id: ${t}.`,error010:()=>"Handle: No node id found. Make sure to only use a Handle inside a custom Node.",error011:e=>`Edge type "${e}" not found. Using fallback type "default".`,error012:e=>`Node with id "${e}" does not exist, it may have been removed. This can happen when a node is deleted before the "onNodeClick" handler is called.`,error013:(e="react")=>`It seems that you haven't loaded the styles. Please import '@xyflow/${e}/dist/style.css' or base.css to make sure everything is working properly.`,error014:()=>"useNodeConnections: No node ID found. Call useNodeConnections inside a custom Node or provide a node ID.",error015:()=>"It seems that you are trying to drag a node that is not initialized. Please use onNodesChange as explained in the docs."},Ts=[[Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY],[Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY]],Nu=["Enter"," ","Escape"],b1={"node.a11yDescription.default":"Press enter or space to select a node. Press delete to remove it and escape to cancel.","node.a11yDescription.keyboardDisabled":"Press enter or space to select a node. You can then use the arrow keys to move the node around. Press delete to remove it and escape to cancel.","node.a11yDescription.ariaLiveMessage":({direction:e,x:t,y:n})=>`Moved selected node ${e}. New position, x: ${t}, y: ${n}`,"edge.a11yDescription.default":"Press enter or space to select an edge. You can then press delete to remove it or escape to cancel.","controls.ariaLabel":"Control Panel","controls.zoomIn.ariaLabel":"Zoom In","controls.zoomOut.ariaLabel":"Zoom Out","controls.fitView.ariaLabel":"Fit View","controls.interactive.ariaLabel":"Toggle Interactivity","minimap.ariaLabel":"Mini Map","handle.ariaLabel":"Handle"};var Nr;(function(e){e.Strict="strict",e.Loose="loose"})(Nr||(Nr={}));var ln;(function(e){e.Free="free",e.Vertical="vertical",e.Horizontal="horizontal"})(ln||(ln={}));var hi;(function(e){e.Partial="partial",e.Full="full"})(hi||(hi={}));const Vs={inProgress:!1,isValid:null,from:null,fromHandle:null,fromPosition:null,fromNode:null,to:null,toHandle:null,toPosition:null,toNode:null};var $n;(function(e){e.Bezier="default",e.Straight="straight",e.Step="step",e.SmoothStep="smoothstep",e.SimpleBezier="simplebezier"})($n||($n={}));var fo;(function(e){e.Arrow="arrow",e.ArrowClosed="arrowclosed"})(fo||(fo={}));var ve;(function(e){e.Left="left",e.Top="top",e.Right="right",e.Bottom="bottom"})(ve||(ve={}));const Pu={[ve.Left]:ve.Right,[ve.Right]:ve.Left,[ve.Top]:ve.Bottom,[ve.Bottom]:ve.Top};function x1(e,t){if(!e&&!t)return!0;if(!e||!t||e.size!==t.size)return!1;if(!e.size&&!t.size)return!0;for(const n of e.keys())if(!t.has(n))return!1;return!0}function zu(e,t,n){if(!n)return;const r=[];e.forEach((o,i)=>{t?.has(i)||r.push(o)}),r.length&&n(r)}function _1(e){return e===null?null:e?"valid":"invalid"}const Lu=e=>"id"in e&&"source"in e&&"target"in e,k1=e=>"id"in e&&"position"in e&&!("source"in e)&&!("target"in e),Os=e=>"id"in e&&"internals"in e&&!("source"in e)&&!("target"in e),po=(e,t=[0,0])=>{const{width:n,height:r}=On(e),o=e.origin??t,i=n*o[0],s=r*o[1];return{x:e.position.x-i,y:e.position.y-s}},$1=(e,t={nodeOrigin:[0,0]})=>{if(e.length===0)return{x:0,y:0,width:0,height:0};const n=e.reduce((r,o)=>{const i=typeof o=="string";let s=!t.nodeLookup&&!i?o:void 0;t.nodeLookup&&(s=i?t.nodeLookup.get(o):Os(o)?o:t.nodeLookup.get(o.id));const a=s?yi(s,t.nodeOrigin):{x:0,y:0,x2:0,y2:0};return vi(r,a)},{x:1/0,y:1/0,x2:-1/0,y2:-1/0});return mi(n)},go=(e,t={})=>{if(e.size===0)return{x:0,y:0,width:0,height:0};let n={x:1/0,y:1/0,x2:-1/0,y2:-1/0};return e.forEach(r=>{if(t.filter===void 0||t.filter(r)){const o=yi(r);n=vi(n,o)}}),mi(n)},As=(e,t,[n,r,o]=[0,0,1],i=!1,s=!1)=>{const a={...mo(t,[n,r,o]),width:t.width/o,height:t.height/o},l=[];for(const u of e.values()){const{measured:d,selectable:p=!0,hidden:f=!1}=u;if(s&&!p||f)continue;const g=d.width??u.width??u.initialWidth??null,h=d.height??u.height??u.initialHeight??null,v=ho(a,zr(u)),w=(g??0)*(h??0),b=i&&v>0;(!u.internals.handleBounds||b||v>=w||u.dragging)&&l.push(u)}return l},C1=(e,t)=>{const n=new Set;return e.forEach(r=>{n.add(r.id)}),t.filter(r=>n.has(r.source)||n.has(r.target))};function S1(e,t){const n=new Map,r=t?.nodes?new Set(t.nodes.map(o=>o.id)):null;return e.forEach(o=>{o.measured.width&&o.measured.height&&(t?.includeHiddenNodes||!o.hidden)&&(!r||r.has(o.id))&&n.set(o.id,o)}),n}async function E1({nodes:e,width:t,height:n,panZoom:r,minZoom:o,maxZoom:i},s){if(e.size===0)return Promise.resolve(!0);const a=S1(e,s),l=go(a),u=qs(l,t,n,s?.minZoom??o,s?.maxZoom??i,s?.padding??.1);return await r.setViewport(u,{duration:s?.duration,ease:s?.ease,interpolate:s?.interpolate}),Promise.resolve(!0)}function Du({nodeId:e,nextPosition:t,nodeLookup:n,nodeOrigin:r=[0,0],nodeExtent:o,onError:i}){const s=n.get(e),a=s.parentId?n.get(s.parentId):void 0,{x:l,y:u}=a?a.internals.positionAbsolute:{x:0,y:0},d=s.origin??r;let p=s.extent||o;if(s.extent==="parent"&&!s.expandParent)if(!a)i?.("005",co.error005());else{const g=a.measured.width,h=a.measured.height;g&&h&&(p=[[l,u],[l+g,u+h]])}else a&&Dr(s.extent)&&(p=[[s.extent[0][0]+l,s.extent[0][1]+u],[s.extent[1][0]+l,s.extent[1][1]+u]]);const f=Dr(p)?ir(t,p,s.measured):t;return(s.measured.width===void 0||s.measured.height===void 0)&&i?.("015",co.error015()),{position:{x:f.x-l+(s.measured.width??0)*d[0],y:f.y-u+(s.measured.height??0)*d[1]},positionAbsolute:f}}async function N1({nodesToRemove:e=[],edgesToRemove:t=[],nodes:n,edges:r,onBeforeDelete:o}){const i=new Set(e.map(p=>p.id)),s=[];for(const p of n){if(p.deletable===!1)continue;const f=i.has(p.id),g=!f&&p.parentId&&s.find(h=>h.id===p.parentId);(f||g)&&s.push(p)}const a=new Set(t.map(p=>p.id)),l=r.filter(p=>p.deletable!==!1),u=C1(s,l);for(const p of l)a.has(p.id)&&!u.find(f=>f.id===p.id)&&u.push(p);if(!o)return{edges:u,nodes:s};const d=await o({nodes:s,edges:u});return typeof d=="boolean"?d?{edges:u,nodes:s}:{edges:[],nodes:[]}:d}const Pr=(e,t=0,n=1)=>Math.min(Math.max(e,t),n),ir=(e={x:0,y:0},t,n)=>({x:Pr(e.x,t[0][0],t[1][0]-(n?.width??0)),y:Pr(e.y,t[0][1],t[1][1]-(n?.height??0))});function Hu(e,t,n){const{width:r,height:o}=On(n),{x:i,y:s}=n.internals.positionAbsolute;return ir(e,[[i,s],[i+r,s+o]],t)}const Mu=(e,t,n)=>e<t?Pr(Math.abs(e-t),1,t)/t:e>n?-Pr(Math.abs(e-n),1,t)/t:0,Tu=(e,t,n=15,r=40)=>{const o=Mu(e.x,r,t.width-r)*n,i=Mu(e.y,r,t.height-r)*n;return[o,i]},vi=(e,t)=>({x:Math.min(e.x,t.x),y:Math.min(e.y,t.y),x2:Math.max(e.x2,t.x2),y2:Math.max(e.y2,t.y2)}),Is=({x:e,y:t,width:n,height:r})=>({x:e,y:t,x2:e+n,y2:t+r}),mi=({x:e,y:t,x2:n,y2:r})=>({x:e,y:t,width:n-e,height:r-t}),zr=(e,t=[0,0])=>{const{x:n,y:r}=Os(e)?e.internals.positionAbsolute:po(e,t);return{x:n,y:r,width:e.measured?.width??e.width??e.initialWidth??0,height:e.measured?.height??e.height??e.initialHeight??0}},yi=(e,t=[0,0])=>{const{x:n,y:r}=Os(e)?e.internals.positionAbsolute:po(e,t);return{x:n,y:r,x2:n+(e.measured?.width??e.width??e.initialWidth??0),y2:r+(e.measured?.height??e.height??e.initialHeight??0)}},Vu=(e,t)=>mi(vi(Is(e),Is(t))),ho=(e,t)=>{const n=Math.max(0,Math.min(e.x+e.width,t.x+t.width)-Math.max(e.x,t.x)),r=Math.max(0,Math.min(e.y+e.height,t.y+t.height)-Math.max(e.y,t.y));return Math.ceil(n*r)},Ou=e=>Cn(e.width)&&Cn(e.height)&&Cn(e.x)&&Cn(e.y),Cn=e=>!isNaN(e)&&isFinite(e),P1=(e,t)=>{},vo=(e,t=[1,1])=>({x:t[0]*Math.round(e.x/t[0]),y:t[1]*Math.round(e.y/t[1])}),mo=({x:e,y:t},[n,r,o],i=!1,s=[1,1])=>{const a={x:(e-n)/o,y:(t-r)/o};return i?vo(a,s):a},wi=({x:e,y:t},[n,r,o])=>({x:e*o+n,y:t*o+r});function Lr(e,t){if(typeof e=="number")return Math.floor((t-t/(1+e))*.5);if(typeof e=="string"&&e.endsWith("px")){const n=parseFloat(e);if(!Number.isNaN(n))return Math.floor(n)}if(typeof e=="string"&&e.endsWith("%")){const n=parseFloat(e);if(!Number.isNaN(n))return Math.floor(t*n*.01)}return console.error(`[React Flow] The padding value "${e}" is invalid. Please provide a number or a string with a valid unit (px or %).`),0}function z1(e,t,n){if(typeof e=="string"||typeof e=="number"){const r=Lr(e,n),o=Lr(e,t);return{top:r,right:o,bottom:r,left:o,x:o*2,y:r*2}}if(typeof e=="object"){const r=Lr(e.top??e.y??0,n),o=Lr(e.bottom??e.y??0,n),i=Lr(e.left??e.x??0,t),s=Lr(e.right??e.x??0,t);return{top:r,right:s,bottom:o,left:i,x:i+s,y:r+o}}return{top:0,right:0,bottom:0,left:0,x:0,y:0}}function L1(e,t,n,r,o,i){const{x:s,y:a}=wi(e,[t,n,r]),{x:l,y:u}=wi({x:e.x+e.width,y:e.y+e.height},[t,n,r]),d=o-l,p=i-u;return{left:Math.floor(s),top:Math.floor(a),right:Math.floor(d),bottom:Math.floor(p)}}const qs=(e,t,n,r,o,i)=>{const s=z1(i,t,n),a=(t-s.x)/e.width,l=(n-s.y)/e.height,u=Math.min(a,l),d=Pr(u,r,o),p=e.x+e.width/2,f=e.y+e.height/2,g=t/2-p*d,h=n/2-f*d,v=L1(e,g,h,d,t,n),w={left:Math.min(v.left-s.left,0),top:Math.min(v.top-s.top,0),right:Math.min(v.right-s.right,0),bottom:Math.min(v.bottom-s.bottom,0)};return{x:g-w.left+w.right,y:h-w.top+w.bottom,zoom:d}},sr=()=>typeof navigator<"u"&&navigator?.userAgent?.indexOf("Mac")>=0;function Dr(e){return e!=null&&e!=="parent"}function On(e){return{width:e.measured?.width??e.width??e.initialWidth??0,height:e.measured?.height??e.height??e.initialHeight??0}}function Au(e){return(e.measured?.width??e.width??e.initialWidth)!==void 0&&(e.measured?.height??e.height??e.initialHeight)!==void 0}function D1(e,t={width:0,height:0},n,r,o){const i={...e},s=r.get(n);if(s){const a=s.origin||o;i.x+=s.internals.positionAbsolute.x-(t.width??0)*a[0],i.y+=s.internals.positionAbsolute.y-(t.height??0)*a[1]}return i}function H1(e){return{...b1,...e||{}}}function Zs(e,{snapGrid:t=[0,0],snapToGrid:n=!1,transform:r,containerBounds:o}){const{x:i,y:s}=un(e),a=mo({x:i-(o?.left??0),y:s-(o?.top??0)},r),{x:l,y:u}=n?vo(a,t):a;return{xSnapped:l,ySnapped:u,...a}}const Iu=e=>({width:e.offsetWidth,height:e.offsetHeight}),qu=e=>e?.getRootNode?.()||window?.document,M1=["INPUT","SELECT","TEXTAREA"];function Zu(e){const t=e.composedPath?.()?.[0]||e.target;return t?.nodeType!==1?!1:M1.includes(t.nodeName)||t.hasAttribute("contenteditable")||!!t.closest(".nokey")}const Ru=e=>"clientX"in e,un=(e,t)=>{const n=Ru(e),r=n?e.clientX:e.touches?.[0].clientX,o=n?e.clientY:e.touches?.[0].clientY;return{x:r-(t?.left??0),y:o-(t?.top??0)}},Bu=(e,t,n,r,o)=>{const i=t.querySelectorAll(`.${e}`);return!i||!i.length?null:Array.from(i).map(s=>{const a=s.getBoundingClientRect();return{id:s.getAttribute("data-handleid"),type:e,nodeId:o,position:s.getAttribute("data-handlepos"),x:(a.left-n.left)/r,y:(a.top-n.top)/r,...Iu(s)}})};function T1({sourceX:e,sourceY:t,targetX:n,targetY:r,sourceControlX:o,sourceControlY:i,targetControlX:s,targetControlY:a}){const l=e*.125+o*.375+s*.375+n*.125,u=t*.125+i*.375+a*.375+r*.125,d=Math.abs(l-e),p=Math.abs(u-t);return[l,u,d,p]}function bi(e,t){return e>=0?.5*e:t*25*Math.sqrt(-e)}function Ku({pos:e,x1:t,y1:n,x2:r,y2:o,c:i}){switch(e){case ve.Left:return[t-bi(t-r,i),n];case ve.Right:return[t+bi(r-t,i),n];case ve.Top:return[t,n-bi(n-o,i)];case ve.Bottom:return[t,n+bi(o-n,i)]}}function Xu({sourceX:e,sourceY:t,sourcePosition:n=ve.Bottom,targetX:r,targetY:o,targetPosition:i=ve.Top,curvature:s=.25}){const[a,l]=Ku({pos:n,x1:e,y1:t,x2:r,y2:o,c:s}),[u,d]=Ku({pos:i,x1:r,y1:o,x2:e,y2:t,c:s}),[p,f,g,h]=T1({sourceX:e,sourceY:t,targetX:r,targetY:o,sourceControlX:a,sourceControlY:l,targetControlX:u,targetControlY:d});return[`M${e},${t} C${a},${l} ${u},${d} ${r},${o}`,p,f,g,h]}function Yu({sourceX:e,sourceY:t,targetX:n,targetY:r}){const o=Math.abs(n-e)/2,i=n<e?n+o:n-o,s=Math.abs(r-t)/2,a=r<t?r+s:r-s;return[i,a,o,s]}function V1({sourceNode:e,targetNode:t,selected:n=!1,zIndex:r,elevateOnSelect:o=!1}){if(r!==void 0)return r;const i=o&&n?1e3:0,s=Math.max(e.parentId?e.internals.z:0,t.parentId?t.internals.z:0);return i+s}function O1({sourceNode:e,targetNode:t,width:n,height:r,transform:o}){const i=vi(yi(e),yi(t));i.x===i.x2&&(i.x2+=1),i.y===i.y2&&(i.y2+=1);const s={x:-o[0]/o[2],y:-o[1]/o[2],width:n/o[2],height:r/o[2]};return ho(s,mi(i))>0}const A1=({source:e,sourceHandle:t,target:n,targetHandle:r})=>`xy-edge__${e}${t||""}-${n}${r||""}`,I1=(e,t)=>t.some(n=>n.source===e.source&&n.target===e.target&&(n.sourceHandle===e.sourceHandle||!n.sourceHandle&&!e.sourceHandle)&&(n.targetHandle===e.targetHandle||!n.targetHandle&&!e.targetHandle)),q1=(e,t)=>{if(!e.source||!e.target)return t;let n;return Lu(e)?n={...e}:n={...e,id:A1(e)},I1(n,t)?t:(n.sourceHandle===null&&delete n.sourceHandle,n.targetHandle===null&&delete n.targetHandle,t.concat(n))};function ju({sourceX:e,sourceY:t,targetX:n,targetY:r}){const[o,i,s,a]=Yu({sourceX:e,sourceY:t,targetX:n,targetY:r});return[`M ${e},${t}L ${n},${r}`,o,i,s,a]}const Wu={[ve.Left]:{x:-1,y:0},[ve.Right]:{x:1,y:0},[ve.Top]:{x:0,y:-1},[ve.Bottom]:{x:0,y:1}},Z1=({source:e,sourcePosition:t=ve.Bottom,target:n})=>t===ve.Left||t===ve.Right?e.x<n.x?{x:1,y:0}:{x:-1,y:0}:e.y<n.y?{x:0,y:1}:{x:0,y:-1},Fu=(e,t)=>Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2));function R1({source:e,sourcePosition:t=ve.Bottom,target:n,targetPosition:r=ve.Top,center:o,offset:i,stepPosition:s}){const a=Wu[t],l=Wu[r],u={x:e.x+a.x*i,y:e.y+a.y*i},d={x:n.x+l.x*i,y:n.y+l.y*i},p=Z1({source:u,sourcePosition:t,target:d}),f=p.x!==0?"x":"y",g=p[f];let h=[],v,w;const b={x:0,y:0},k={x:0,y:0},[,,x,S]=Yu({sourceX:e.x,sourceY:e.y,targetX:n.x,targetY:n.y});if(a[f]*l[f]===-1){f==="x"?(v=o.x??u.x+(d.x-u.x)*s,w=o.y??(u.y+d.y)/2):(v=o.x??(u.x+d.x)/2,w=o.y??u.y+(d.y-u.y)*s);const E=[{x:v,y:u.y},{x:v,y:d.y}],L=[{x:u.x,y:w},{x:d.x,y:w}];a[f]===g?h=f==="x"?E:L:h=f==="x"?L:E}else{const E=[{x:u.x,y:d.y}],L=[{x:d.x,y:u.y}];if(f==="x"?h=a.x===g?L:E:h=a.y===g?E:L,t===r){const O=Math.abs(e[f]-n[f]);if(O<=i){const $=Math.min(i-1,i-O);a[f]===g?b[f]=(u[f]>e[f]?-1:1)*$:k[f]=(d[f]>n[f]?-1:1)*$}}if(t!==r){const O=f==="x"?"y":"x",$=a[f]===l[O],C=u[O]>d[O],_=u[O]<d[O];(a[f]===1&&(!$&&C||$&&_)||a[f]!==1&&(!$&&_||$&&C))&&(h=f==="x"?E:L)}const D={x:u.x+b.x,y:u.y+b.y},q={x:d.x+k.x,y:d.y+k.y},B=Math.max(Math.abs(D.x-h[0].x),Math.abs(q.x-h[0].x)),U=Math.max(Math.abs(D.y-h[0].y),Math.abs(q.y-h[0].y));B>=U?(v=(D.x+q.x)/2,w=h[0].y):(v=h[0].x,w=(D.y+q.y)/2)}return[[e,{x:u.x+b.x,y:u.y+b.y},...h,{x:d.x+k.x,y:d.y+k.y},n],v,w,x,S]}function B1(e,t,n,r){const o=Math.min(Fu(e,t)/2,Fu(t,n)/2,r),{x:i,y:s}=t;if(e.x===i&&i===n.x||e.y===s&&s===n.y)return`L${i} ${s}`;if(e.y===s){const u=e.x<n.x?-1:1,d=e.y<n.y?1:-1;return`L ${i+o*u},${s}Q ${i},${s} ${i},${s+o*d}`}const a=e.x<n.x?1:-1,l=e.y<n.y?-1:1;return`L ${i},${s+o*l}Q ${i},${s} ${i+o*a},${s}`}function Rs({sourceX:e,sourceY:t,sourcePosition:n=ve.Bottom,targetX:r,targetY:o,targetPosition:i=ve.Top,borderRadius:s=5,centerX:a,centerY:l,offset:u=20,stepPosition:d=.5}){const[p,f,g,h,v]=R1({source:{x:e,y:t},sourcePosition:n,target:{x:r,y:o},targetPosition:i,center:{x:a,y:l},offset:u,stepPosition:d});return[p.reduce((w,b,k)=>{let x="";return k>0&&k<p.length-1?x=B1(p[k-1],b,p[k+1],s):x=`${k===0?"M":"L"}${b.x} ${b.y}`,w+=x,w},""),f,g,h,v]}function Gu(e){return e&&!!(e.internals.handleBounds||e.handles?.length)&&!!(e.measured.width||e.width||e.initialWidth)}function K1(e){const{sourceNode:t,targetNode:n}=e;if(!Gu(t)||!Gu(n))return null;const r=t.internals.handleBounds||Uu(t.handles),o=n.internals.handleBounds||Uu(n.handles),i=Ju(r?.source??[],e.sourceHandle),s=Ju(e.connectionMode===Nr.Strict?o?.target??[]:(o?.target??[]).concat(o?.source??[]),e.targetHandle);if(!i||!s)return e.onError?.("008",co.error008(i?"target":"source",{id:e.id,sourceHandle:e.sourceHandle,targetHandle:e.targetHandle})),null;const a=i?.position||ve.Bottom,l=s?.position||ve.Top,u=yo(t,i,a),d=yo(n,s,l);return{sourceX:u.x,sourceY:u.y,targetX:d.x,targetY:d.y,sourcePosition:a,targetPosition:l}}function Uu(e){if(!e)return null;const t=[],n=[];for(const r of e)r.width=r.width??1,r.height=r.height??1,r.type==="source"?t.push(r):r.type==="target"&&n.push(r);return{source:t,target:n}}function yo(e,t,n=ve.Left,r=!1){const o=(t?.x??0)+e.internals.positionAbsolute.x,i=(t?.y??0)+e.internals.positionAbsolute.y,{width:s,height:a}=t??On(e);if(r)return{x:o+s/2,y:i+a/2};switch(t?.position??n){case ve.Top:return{x:o+s/2,y:i};case ve.Right:return{x:o+s,y:i+a/2};case ve.Bottom:return{x:o+s/2,y:i+a};case ve.Left:return{x:o,y:i+a/2}}}function Ju(e,t){return e&&(t?e.find(n=>n.id===t):e[0])||null}function Bs(e,t){return e?typeof e=="string"?e:`${t?`${t}__`:""}${Object.keys(e).sort().map(n=>`${n}=${e[n]}`).join("&")}`:""}function X1(e,{id:t,defaultColor:n,defaultMarkerStart:r,defaultMarkerEnd:o}){const i=new Set;return e.reduce((s,a)=>([a.markerStart||r,a.markerEnd||o].forEach(l=>{if(l&&typeof l=="object"){const u=Bs(l,t);i.has(u)||(s.push({id:u,color:l.color||n,...l}),i.add(u))}}),s),[]).sort((s,a)=>s.id.localeCompare(a.id))}function Y1(e,t,n,r,o){let i=.5;o==="start"?i=0:o==="end"&&(i=1);let s=[(e.x+e.width*i)*t.zoom+t.x,e.y*t.zoom+t.y-r],a=[-100*i,-100];switch(n){case ve.Right:s=[(e.x+e.width)*t.zoom+t.x+r,(e.y+e.height*i)*t.zoom+t.y],a=[0,-100*i];break;case ve.Bottom:s[1]=(e.y+e.height)*t.zoom+t.y+r,a[1]=0;break;case ve.Left:s=[e.x*t.zoom+t.x-r,(e.y+e.height*i)*t.zoom+t.y],a=[-100,-100*i];break}return`translate(${s[0]}px, ${s[1]}px) translate(${a[0]}%, ${a[1]}%)`}const Ks={nodeOrigin:[0,0],nodeExtent:Ts,elevateNodesOnSelect:!0,defaults:{}},j1={...Ks,checkEquality:!0};function Xs(e,t){const n={...e};for(const r in t)t[r]!==void 0&&(n[r]=t[r]);return n}function W1(e,t,n){const r=Xs(Ks,n);for(const o of e.values())if(o.parentId)Ys(o,e,t,r);else{const i=po(o,r.nodeOrigin),s=Dr(o.extent)?o.extent:r.nodeExtent,a=ir(i,s,On(o));o.internals.positionAbsolute=a}}function F1(e,t,n,r){const o=Xs(j1,r);let i=e.length>0;const s=new Map(t),a=o?.elevateNodesOnSelect?1e3:0;t.clear(),n.clear();for(const l of e){let u=s.get(l.id);if(o.checkEquality&&l===u?.internals.userNode)t.set(l.id,u);else{const d=po(l,o.nodeOrigin),p=Dr(l.extent)?l.extent:o.nodeExtent,f=ir(d,p,On(l));u={...o.defaults,...l,measured:{width:l.measured?.width,height:l.measured?.height},internals:{positionAbsolute:f,handleBounds:l.measured?u?.internals.handleBounds:void 0,z:Qu(l,a),userNode:l}},t.set(l.id,u)}(u.measured===void 0||u.measured.width===void 0||u.measured.height===void 0)&&!u.hidden&&(i=!1),l.parentId&&Ys(u,t,n,r)}return i}function G1(e,t){if(!e.parentId)return;const n=t.get(e.parentId);n?n.set(e.id,e):t.set(e.parentId,new Map([[e.id,e]]))}function Ys(e,t,n,r){const{elevateNodesOnSelect:o,nodeOrigin:i,nodeExtent:s}=Xs(Ks,r),a=e.parentId,l=t.get(a);if(!l){console.warn(`Parent node ${a} not found. Please make sure that parent nodes are in front of their child nodes in the nodes array.`);return}G1(e,n);const u=o?1e3:0,{x:d,y:p,z:f}=U1(e,l,i,s,u),{positionAbsolute:g}=e.internals,h=d!==g.x||p!==g.y;(h||f!==e.internals.z)&&t.set(e.id,{...e,internals:{...e.internals,positionAbsolute:h?{x:d,y:p}:g,z:f}})}function Qu(e,t){return(Cn(e.zIndex)?e.zIndex:0)+(e.selected?t:0)}function U1(e,t,n,r,o){const{x:i,y:s}=t.internals.positionAbsolute,a=On(e),l=po(e,n),u=Dr(e.extent)?ir(l,e.extent,a):l;let d=ir({x:i+u.x,y:s+u.y},r,a);e.extent==="parent"&&(d=Hu(d,a,t));const p=Qu(e,o),f=t.internals.z??0;return{x:d.x,y:d.y,z:f>=p?f+1:p}}function J1(e,t,n,r=[0,0]){const o=[],i=new Map;for(const s of e){const a=t.get(s.parentId);if(!a)continue;const l=i.get(s.parentId)?.expandedRect??zr(a),u=Vu(l,s.rect);i.set(s.parentId,{expandedRect:u,parent:a})}return i.size>0&&i.forEach(({expandedRect:s,parent:a},l)=>{const u=a.internals.positionAbsolute,d=On(a),p=a.origin??r,f=s.x<u.x?Math.round(Math.abs(u.x-s.x)):0,g=s.y<u.y?Math.round(Math.abs(u.y-s.y)):0,h=Math.max(d.width,Math.round(s.width)),v=Math.max(d.height,Math.round(s.height)),w=(h-d.width)*p[0],b=(v-d.height)*p[1];(f>0||g>0||w||b)&&(o.push({id:l,type:"position",position:{x:a.position.x-f+w,y:a.position.y-g+b}}),n.get(l)?.forEach(k=>{e.some(x=>x.id===k.id)||o.push({id:k.id,type:"position",position:{x:k.position.x+f,y:k.position.y+g}})})),(d.width<s.width||d.height<s.height||f||g)&&o.push({id:l,type:"dimensions",setAttributes:!0,dimensions:{width:h+(f?p[0]*f-w:0),height:v+(g?p[1]*g-b:0)}})}),o}function Q1(e,t,n,r,o,i){const s=r?.querySelector(".xyflow__viewport");let a=!1;if(!s)return{changes:[],updatedInternals:a};const l=[],u=window.getComputedStyle(s),{m22:d}=new window.DOMMatrixReadOnly(u.transform),p=[];for(const f of e.values()){const g=t.get(f.id);if(!g)continue;if(g.hidden){t.set(g.id,{...g,internals:{...g.internals,handleBounds:void 0}}),a=!0;continue}const h=Iu(f.nodeElement),v=g.measured.width!==h.width||g.measured.height!==h.height;if(h.width&&h.height&&(v||!g.internals.handleBounds||f.force)){const w=f.nodeElement.getBoundingClientRect(),b=Dr(g.extent)?g.extent:i;let{positionAbsolute:k}=g.internals;g.parentId&&g.extent==="parent"?k=Hu(k,h,t.get(g.parentId)):b&&(k=ir(k,b,h));const x={...g,measured:h,internals:{...g.internals,positionAbsolute:k,handleBounds:{source:Bu("source",f.nodeElement,w,d,g.id),target:Bu("target",f.nodeElement,w,d,g.id)}}};t.set(g.id,x),g.parentId&&Ys(x,t,n,{nodeOrigin:o}),a=!0,v&&(l.push({id:g.id,type:"dimensions",dimensions:h}),g.expandParent&&g.parentId&&p.push({id:g.id,parentId:g.parentId,rect:zr(x,o)}))}}if(p.length>0){const f=J1(p,t,n,o);l.push(...f)}return{changes:l,updatedInternals:a}}async function e0({delta:e,panZoom:t,transform:n,translateExtent:r,width:o,height:i}){if(!t||!e.x&&!e.y)return Promise.resolve(!1);const s=await t.setViewportConstrained({x:n[0]+e.x,y:n[1]+e.y,zoom:n[2]},[[0,0],[o,i]],r),a=!!s&&(s.x!==n[0]||s.y!==n[1]||s.k!==n[2]);return Promise.resolve(a)}function ec(e,t,n,r,o,i){let s=o;const a=r.get(s)||new Map;r.set(s,a.set(n,t)),s=`${o}-${e}`;const l=r.get(s)||new Map;if(r.set(s,l.set(n,t)),i){s=`${o}-${e}-${i}`;const u=r.get(s)||new Map;r.set(s,u.set(n,t))}}function t0(e,t,n){e.clear(),t.clear();for(const r of n){const{source:o,target:i,sourceHandle:s=null,targetHandle:a=null}=r,l={edgeId:r.id,source:o,target:i,sourceHandle:s,targetHandle:a},u=`${o}-${s}--${i}-${a}`,d=`${i}-${a}--${o}-${s}`;ec("source",l,d,e,o,s),ec("target",l,u,e,i,a),t.set(r.id,r)}}function n0(e,t){if(e===null||t===null)return!1;const n=Array.isArray(e)?e:[e],r=Array.isArray(t)?t:[t];if(n.length!==r.length)return!1;for(let o=0;o<n.length;o++)if(n[o].id!==r[o].id||n[o].type!==r[o].type||!Object.is(n[o].data,r[o].data))return!1;return!0}function tc(e,t){if(!e.parentId)return!1;const n=t.get(e.parentId);return n?n.selected?!0:tc(n,t):!1}function nc(e,t,n){let r=e;do{if(r?.matches?.(t))return!0;if(r===n)return!1;r=r?.parentElement}while(r);return!1}function r0(e,t,n,r){const o=new Map;for(const[i,s]of e)if((s.selected||s.id===r)&&(!s.parentId||!tc(s,e))&&(s.draggable||t&&typeof s.draggable>"u")){const a=e.get(i);a&&o.set(i,{id:i,position:a.position||{x:0,y:0},distance:{x:n.x-a.internals.positionAbsolute.x,y:n.y-a.internals.positionAbsolute.y},extent:a.extent,parentId:a.parentId,origin:a.origin,expandParent:a.expandParent,internals:{positionAbsolute:a.internals.positionAbsolute||{x:0,y:0}},measured:{width:a.measured.width??0,height:a.measured.height??0}})}return o}function js({nodeId:e,dragItems:t,nodeLookup:n,dragging:r=!0}){const o=[];for(const[s,a]of t){const l=n.get(s)?.internals.userNode;l&&o.push({...l,position:a.position,dragging:r})}if(!e)return[o[0],o];const i=n.get(e)?.internals.userNode;return[i?{...i,position:t.get(e)?.position||i.position,dragging:r}:o[0],o]}function o0({dragItems:e,snapGrid:t,x:n,y:r}){const o=e.values().next().value;if(!o)return null;const i={x:n-o.distance.x,y:r-o.distance.y},s=vo(i,t);return{x:s.x-i.x,y:s.y-i.y}}function i0({onNodeMouseDown:e,getStoreItems:t,onDragStart:n,onDrag:r,onDragStop:o}){let i={x:null,y:null},s=0,a=new Map,l=!1,u={x:0,y:0},d=null,p=!1,f=null,g=!1,h=!1,v=null;function w({noDragClassName:k,handleSelector:x,domNode:S,isSelectable:E,nodeId:L,nodeClickDistance:D=0}){f=Vt(S);function q({x:$,y:C}){const{nodeLookup:_,nodeExtent:P,snapGrid:N,snapToGrid:T,nodeOrigin:Z,onNodeDrag:X,onSelectionDrag:M,onError:Y,updateNodePositions:ee}=t();i={x:$,y:C};let ne=!1;const R=a.size>1,W=R&&P?Is(go(a)):null,F=R&&T?o0({dragItems:a,snapGrid:N,x:$,y:C}):null;for(const[ie,G]of a){if(!_.has(ie))continue;let me={x:$-G.distance.x,y:C-G.distance.y};T&&(me=F?{x:Math.round(me.x+F.x),y:Math.round(me.y+F.y)}:vo(me,N));let we=null;if(R&&P&&!G.extent&&W){const{positionAbsolute:te}=G.internals,de=te.x-W.x+P[0][0],fe=te.x+G.measured.width-W.x2+P[1][0],le=te.y-W.y+P[0][1],Ne=te.y+G.measured.height-W.y2+P[1][1];we=[[de,le],[fe,Ne]]}const{position:re,positionAbsolute:Q}=Du({nodeId:ie,nextPosition:me,nodeLookup:_,nodeExtent:we||P,nodeOrigin:Z,onError:Y});ne=ne||G.position.x!==re.x||G.position.y!==re.y,G.position=re,G.internals.positionAbsolute=Q}if(h=h||ne,!!ne&&(ee(a,!0),v&&(r||X||!L&&M))){const[ie,G]=js({nodeId:L,dragItems:a,nodeLookup:_});r?.(v,a,ie,G),X?.(v,ie,G),L||M?.(v,G)}}async function B(){if(!d)return;const{transform:$,panBy:C,autoPanSpeed:_,autoPanOnNodeDrag:P}=t();if(!P){l=!1,cancelAnimationFrame(s);return}const[N,T]=Tu(u,d,_);(N!==0||T!==0)&&(i.x=(i.x??0)-N/$[2],i.y=(i.y??0)-T/$[2],await C({x:N,y:T})&&q(i)),s=requestAnimationFrame(B)}function U($){const{nodeLookup:C,multiSelectionActive:_,nodesDraggable:P,transform:N,snapGrid:T,snapToGrid:Z,selectNodesOnDrag:X,onNodeDragStart:M,onSelectionDragStart:Y,unselectNodesAndEdges:ee}=t();p=!0,(!X||!E)&&!_&&L&&(C.get(L)?.selected||ee()),E&&X&&L&&e?.(L);const ne=Zs($.sourceEvent,{transform:N,snapGrid:T,snapToGrid:Z,containerBounds:d});if(i=ne,a=r0(C,P,ne,L),a.size>0&&(n||M||!L&&Y)){const[R,W]=js({nodeId:L,dragItems:a,nodeLookup:C});n?.($.sourceEvent,a,R,W),M?.($.sourceEvent,R,W),L||Y?.($.sourceEvent,W)}}const O=Eh().clickDistance(D).on("start",$=>{const{domNode:C,nodeDragThreshold:_,transform:P,snapGrid:N,snapToGrid:T}=t();d=C?.getBoundingClientRect()||null,g=!1,h=!1,v=$.sourceEvent,_===0&&U($),i=Zs($.sourceEvent,{transform:P,snapGrid:N,snapToGrid:T,containerBounds:d}),u=un($.sourceEvent,d)}).on("drag",$=>{const{autoPanOnNodeDrag:C,transform:_,snapGrid:P,snapToGrid:N,nodeDragThreshold:T,nodeLookup:Z}=t(),X=Zs($.sourceEvent,{transform:_,snapGrid:P,snapToGrid:N,containerBounds:d});if(v=$.sourceEvent,($.sourceEvent.type==="touchmove"&&$.sourceEvent.touches.length>1||L&&!Z.has(L))&&(g=!0),!g){if(!l&&C&&p&&(l=!0,B()),!p){const M=X.xSnapped-(i.x??0),Y=X.ySnapped-(i.y??0);Math.sqrt(M*M+Y*Y)>T&&U($)}(i.x!==X.xSnapped||i.y!==X.ySnapped)&&a&&p&&(u=un($.sourceEvent,d),q(X))}}).on("end",$=>{if(!(!p||g)&&(l=!1,p=!1,cancelAnimationFrame(s),a.size>0)){const{nodeLookup:C,updateNodePositions:_,onNodeDragStop:P,onSelectionDragStop:N}=t();if(h&&(_(a,!1),h=!1),o||P||!L&&N){const[T,Z]=js({nodeId:L,dragItems:a,nodeLookup:C,dragging:!1});o?.($.sourceEvent,a,T,Z),P?.($.sourceEvent,T,Z),L||N?.($.sourceEvent,Z)}}}).filter($=>{const C=$.target;return!$.button&&(!k||!nc(C,`.${k}`,S))&&(!x||nc(C,x,S))});f.call(O)}function b(){f?.on(".drag",null)}return{update:w,destroy:b}}function s0(e,t,n){const r=[],o={x:e.x-n,y:e.y-n,width:n*2,height:n*2};for(const i of t.values())ho(o,zr(i))>0&&r.push(i);return r}const a0=250;function l0(e,t,n,r){let o=[],i=1/0;const s=s0(e,n,t+a0);for(const a of s){const l=[...a.internals.handleBounds?.source??[],...a.internals.handleBounds?.target??[]];for(const u of l){if(r.nodeId===u.nodeId&&r.type===u.type&&r.id===u.id)continue;const{x:d,y:p}=yo(a,u,u.position,!0),f=Math.sqrt(Math.pow(d-e.x,2)+Math.pow(p-e.y,2));f>t||(f<i?(o=[{...u,x:d,y:p}],i=f):f===i&&o.push({...u,x:d,y:p}))}}if(!o.length)return null;if(o.length>1){const a=r.type==="source"?"target":"source";return o.find(l=>l.type===a)??o[0]}return o[0]}function rc(e,t,n,r,o,i=!1){const s=r.get(e);if(!s)return null;const a=o==="strict"?s.internals.handleBounds?.[t]:[...s.internals.handleBounds?.source??[],...s.internals.handleBounds?.target??[]],l=(n?a?.find(u=>u.id===n):a?.[0])??null;return l&&i?{...l,...yo(s,l,l.position,!0)}:l}function oc(e,t){return e||(t?.classList.contains("target")?"target":t?.classList.contains("source")?"source":null)}function u0(e,t){let n=null;return t?n=!0:e&&!t&&(n=!1),n}const ic=()=>!0;function c0(e,{connectionMode:t,connectionRadius:n,handleId:r,nodeId:o,edgeUpdaterType:i,isTarget:s,domNode:a,nodeLookup:l,lib:u,autoPanOnConnect:d,flowId:p,panBy:f,cancelConnection:g,onConnectStart:h,onConnect:v,onConnectEnd:w,isValidConnection:b=ic,onReconnectEnd:k,updateConnection:x,getTransform:S,getFromHandle:E,autoPanSpeed:L,dragThreshold:D=1,handleDomNode:q}){const B=qu(e.target);let U=0,O;const{x:$,y:C}=un(e),_=oc(i,q),P=a?.getBoundingClientRect();let N=!1;if(!P||!_)return;const T=rc(o,_,r,l,t);if(!T)return;let Z=un(e,P),X=!1,M=null,Y=!1,ee=null;function ne(){if(!d||!P)return;const[we,re]=Tu(Z,P,L);f({x:we,y:re}),U=requestAnimationFrame(ne)}const R={...T,nodeId:o,type:_,position:T.position},W=l.get(o);let F={inProgress:!0,isValid:null,from:yo(W,R,ve.Left,!0),fromHandle:R,fromPosition:R.position,fromNode:W,to:Z,toHandle:null,toPosition:Pu[R.position],toNode:null};function ie(){N=!0,x(F),h?.(e,{nodeId:o,handleId:r,handleType:_})}D===0&&ie();function G(we){if(!N){const{x:de,y:fe}=un(we),le=de-$,Ne=fe-C;if(!(le*le+Ne*Ne>D*D))return;ie()}if(!E()||!R){me(we);return}const re=S();Z=un(we,P),O=l0(mo(Z,re,!1,[1,1]),n,l,R),X||(ne(),X=!0);const Q=sc(we,{handle:O,connectionMode:t,fromNodeId:o,fromHandleId:r,fromType:s?"target":"source",isValidConnection:b,doc:B,lib:u,flowId:p,nodeLookup:l});ee=Q.handleDomNode,M=Q.connection,Y=u0(!!O,Q.isValid);const te={...F,isValid:Y,to:Q.toHandle&&Y?wi({x:Q.toHandle.x,y:Q.toHandle.y},re):Z,toHandle:Q.toHandle,toPosition:Y&&Q.toHandle?Q.toHandle.position:Pu[R.position],toNode:Q.toHandle?l.get(Q.toHandle.nodeId):null};Y&&O&&F.toHandle&&te.toHandle&&F.toHandle.type===te.toHandle.type&&F.toHandle.nodeId===te.toHandle.nodeId&&F.toHandle.id===te.toHandle.id&&F.to.x===te.to.x&&F.to.y===te.to.y||(x(te),F=te)}function me(we){if(N){(O||ee)&&M&&Y&&v?.(M);const{inProgress:re,...Q}=F,te={...Q,toPosition:F.toHandle?F.toPosition:null};w?.(we,te),i&&k?.(we,te)}g(),cancelAnimationFrame(U),X=!1,Y=!1,M=null,ee=null,B.removeEventListener("mousemove",G),B.removeEventListener("mouseup",me),B.removeEventListener("touchmove",G),B.removeEventListener("touchend",me)}B.addEventListener("mousemove",G),B.addEventListener("mouseup",me),B.addEventListener("touchmove",G),B.addEventListener("touchend",me)}function sc(e,{handle:t,connectionMode:n,fromNodeId:r,fromHandleId:o,fromType:i,doc:s,lib:a,flowId:l,isValidConnection:u=ic,nodeLookup:d}){const p=i==="target",f=t?s.querySelector(`.${a}-flow__handle[data-id="${l}-${t?.nodeId}-${t?.id}-${t?.type}"]`):null,{x:g,y:h}=un(e),v=s.elementFromPoint(g,h),w=v?.classList.contains(`${a}-flow__handle`)?v:f,b={handleDomNode:w,isValid:!1,connection:null,toHandle:null};if(w){const k=oc(void 0,w),x=w.getAttribute("data-nodeid"),S=w.getAttribute("data-handleid"),E=w.classList.contains("connectable"),L=w.classList.contains("connectableend");if(!x||!k)return b;const D={source:p?x:r,sourceHandle:p?S:o,target:p?r:x,targetHandle:p?o:S};b.connection=D;const q=E&&L&&(n===Nr.Strict?p&&k==="source"||!p&&k==="target":x!==r||S!==o);b.isValid=q&&u(D),b.toHandle=rc(x,k,S,d,n,!0)}return b}const ac={onPointerDown:c0,isValid:sc};function d0({domNode:e,panZoom:t,getTransform:n,getViewScale:r}){const o=Vt(e);function i({translateExtent:a,width:l,height:u,zoomStep:d=1,pannable:p=!0,zoomable:f=!0,inversePan:g=!1}){const h=x=>{if(x.sourceEvent.type!=="wheel"||!t)return;const S=n(),E=x.sourceEvent.ctrlKey&&sr()?10:1,L=-x.sourceEvent.deltaY*(x.sourceEvent.deltaMode===1?.05:x.sourceEvent.deltaMode?1:.002)*d,D=S[2]*Math.pow(2,L*E);t.scaleTo(D)};let v=[0,0];const w=x=>{(x.sourceEvent.type==="mousedown"||x.sourceEvent.type==="touchstart")&&(v=[x.sourceEvent.clientX??x.sourceEvent.touches[0].clientX,x.sourceEvent.clientY??x.sourceEvent.touches[0].clientY])},b=x=>{const S=n();if(x.sourceEvent.type!=="mousemove"&&x.sourceEvent.type!=="touchmove"||!t)return;const E=[x.sourceEvent.clientX??x.sourceEvent.touches[0].clientX,x.sourceEvent.clientY??x.sourceEvent.touches[0].clientY],L=[E[0]-v[0],E[1]-v[1]];v=E;const D=r()*Math.max(S[2],Math.log(S[2]))*(g?-1:1),q={x:S[0]-L[0]*D,y:S[1]-L[1]*D},B=[[0,0],[l,u]];t.setViewportConstrained({x:q.x,y:q.y,zoom:S[2]},B,a)},k=Eu().on("start",w).on("zoom",p?b:null).on("zoom.wheel",f?h:null);o.call(k,{})}function s(){o.on("zoom",null)}return{update:i,destroy:s,pointer:Kt}}const f0=(e,t)=>e.x!==t.x||e.y!==t.y||e.zoom!==t.k,xi=e=>({x:e.x,y:e.y,zoom:e.k}),Ws=({x:e,y:t,zoom:n})=>gi.translate(e,t).scale(n),Hr=(e,t)=>e.target.closest(`.${t}`),lc=(e,t)=>t===2&&Array.isArray(e)&&e.includes(2),p0=e=>((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2,Fs=(e,t=0,n=p0,r=()=>{})=>{const o=typeof t=="number"&&t>0;return o||r(),o?e.transition().duration(t).ease(n).on("end",r):e},uc=e=>{const t=e.ctrlKey&&sr()?10:1;return-e.deltaY*(e.deltaMode===1?.05:e.deltaMode?1:.002)*t};function g0({zoomPanValues:e,noWheelClassName:t,d3Selection:n,d3Zoom:r,panOnScrollMode:o,panOnScrollSpeed:i,zoomOnPinch:s,onPanZoomStart:a,onPanZoom:l,onPanZoomEnd:u}){return d=>{if(Hr(d,t))return!1;d.preventDefault(),d.stopImmediatePropagation();const p=n.property("__zoom").k||1;if(d.ctrlKey&&s){const w=Kt(d),b=uc(d),k=p*Math.pow(2,b);r.scaleTo(n,k,w,d);return}const f=d.deltaMode===1?20:1;let g=o===ln.Vertical?0:d.deltaX*f,h=o===ln.Horizontal?0:d.deltaY*f;!sr()&&d.shiftKey&&o!==ln.Vertical&&(g=d.deltaY*f,h=0),r.translateBy(n,-(g/p)*i,-(h/p)*i,{internal:!0});const v=xi(n.property("__zoom"));clearTimeout(e.panScrollTimeout),e.isPanScrolling||(e.isPanScrolling=!0,a?.(d,v)),e.isPanScrolling&&(l?.(d,v),e.panScrollTimeout=setTimeout(()=>{u?.(d,v),e.isPanScrolling=!1},150))}}function h0({noWheelClassName:e,preventScrolling:t,d3ZoomHandler:n}){return function(r,o){const i=r.type==="wheel",s=!t&&i&&!r.ctrlKey,a=Hr(r,e);if(r.ctrlKey&&i&&a&&r.preventDefault(),s||a)return null;r.preventDefault(),n.call(this,r,o)}}function v0({zoomPanValues:e,onDraggingChange:t,onPanZoomStart:n}){return r=>{if(r.sourceEvent?.internal)return;const o=xi(r.transform);e.mouseButton=r.sourceEvent?.button||0,e.isZoomingOrPanning=!0,e.prevViewport=o,r.sourceEvent?.type==="mousedown"&&t(!0),n&&n?.(r.sourceEvent,o)}}function m0({zoomPanValues:e,panOnDrag:t,onPaneContextMenu:n,onTransformChange:r,onPanZoom:o}){return i=>{e.usedRightMouseButton=!!(n&&lc(t,e.mouseButton??0)),i.sourceEvent?.sync||r([i.transform.x,i.transform.y,i.transform.k]),o&&!i.sourceEvent?.internal&&o?.(i.sourceEvent,xi(i.transform))}}function y0({zoomPanValues:e,panOnDrag:t,panOnScroll:n,onDraggingChange:r,onPanZoomEnd:o,onPaneContextMenu:i}){return s=>{if(!s.sourceEvent?.internal&&(e.isZoomingOrPanning=!1,i&&lc(t,e.mouseButton??0)&&!e.usedRightMouseButton&&s.sourceEvent&&i(s.sourceEvent),e.usedRightMouseButton=!1,r(!1),o&&f0(e.prevViewport,s.transform))){const a=xi(s.transform);e.prevViewport=a,clearTimeout(e.timerId),e.timerId=setTimeout(()=>{o?.(s.sourceEvent,a)},n?150:0)}}}function w0({zoomActivationKeyPressed:e,zoomOnScroll:t,zoomOnPinch:n,panOnDrag:r,panOnScroll:o,zoomOnDoubleClick:i,userSelectionActive:s,noWheelClassName:a,noPanClassName:l,lib:u}){return d=>{const p=e||t,f=n&&d.ctrlKey;if(d.button===1&&d.type==="mousedown"&&(Hr(d,`${u}-flow__node`)||Hr(d,`${u}-flow__edge`)))return!0;if(!r&&!p&&!o&&!i&&!n||s||Hr(d,a)&&d.type==="wheel"||Hr(d,l)&&(d.type!=="wheel"||o&&d.type==="wheel"&&!e)||!n&&d.ctrlKey&&d.type==="wheel")return!1;if(!n&&d.type==="touchstart"&&d.touches?.length>1)return d.preventDefault(),!1;if(!p&&!o&&!f&&d.type==="wheel"||!r&&(d.type==="mousedown"||d.type==="touchstart")||Array.isArray(r)&&!r.includes(d.button)&&d.type==="mousedown")return!1;const g=Array.isArray(r)&&r.includes(d.button)||!d.button||d.button<=1;return(!d.ctrlKey||d.type==="wheel")&&g}}function b0({domNode:e,minZoom:t,maxZoom:n,paneClickDistance:r,translateExtent:o,viewport:i,onPanZoom:s,onPanZoomStart:a,onPanZoomEnd:l,onDraggingChange:u}){const d={isZoomingOrPanning:!1,usedRightMouseButton:!1,prevViewport:{x:0,y:0,zoom:0},mouseButton:0,timerId:void 0,panScrollTimeout:void 0,isPanScrolling:!1},p=e.getBoundingClientRect(),f=Eu().clickDistance(!Cn(r)||r<0?0:r).scaleExtent([t,n]).translateExtent(o),g=Vt(e).call(f);x({x:i.x,y:i.y,zoom:Pr(i.zoom,t,n)},[[0,0],[p.width,p.height]],o);const h=g.on("wheel.zoom"),v=g.on("dblclick.zoom");f.wheelDelta(uc);function w($,C){return g?new Promise(_=>{f?.interpolate(C?.interpolate==="linear"?oo:oi).transform(Fs(g,C?.duration,C?.ease,()=>_(!0)),$)}):Promise.resolve(!1)}function b({noWheelClassName:$,noPanClassName:C,onPaneContextMenu:_,userSelectionActive:P,panOnScroll:N,panOnDrag:T,panOnScrollMode:Z,panOnScrollSpeed:X,preventScrolling:M,zoomOnPinch:Y,zoomOnScroll:ee,zoomOnDoubleClick:ne,zoomActivationKeyPressed:R,lib:W,onTransformChange:F}){P&&!d.isZoomingOrPanning&&k();const ie=N&&!R&&!P?g0({zoomPanValues:d,noWheelClassName:$,d3Selection:g,d3Zoom:f,panOnScrollMode:Z,panOnScrollSpeed:X,zoomOnPinch:Y,onPanZoomStart:a,onPanZoom:s,onPanZoomEnd:l}):h0({noWheelClassName:$,preventScrolling:M,d3ZoomHandler:h});if(g.on("wheel.zoom",ie,{passive:!1}),!P){const me=v0({zoomPanValues:d,onDraggingChange:u,onPanZoomStart:a});f.on("start",me);const we=m0({zoomPanValues:d,panOnDrag:T,onPaneContextMenu:!!_,onPanZoom:s,onTransformChange:F});f.on("zoom",we);const re=y0({zoomPanValues:d,panOnDrag:T,panOnScroll:N,onPaneContextMenu:_,onPanZoomEnd:l,onDraggingChange:u});f.on("end",re)}const G=w0({zoomActivationKeyPressed:R,panOnDrag:T,zoomOnScroll:ee,panOnScroll:N,zoomOnDoubleClick:ne,zoomOnPinch:Y,userSelectionActive:P,noPanClassName:C,noWheelClassName:$,lib:W});f.filter(G),ne?g.on("dblclick.zoom",v):g.on("dblclick.zoom",null)}function k(){f.on("zoom",null)}async function x($,C,_){const P=Ws($),N=f?.constrain()(P,C,_);return N&&await w(N),new Promise(T=>T(N))}async function S($,C){const _=Ws($);return await w(_,C),new Promise(P=>P(_))}function E($){if(g){const C=Ws($),_=g.property("__zoom");(_.k!==$.zoom||_.x!==$.x||_.y!==$.y)&&f?.transform(g,C,null,{sync:!0})}}function L(){const $=g?Cu(g.node()):{x:0,y:0,k:1};return{x:$.x,y:$.y,zoom:$.k}}function D($,C){return g?new Promise(_=>{f?.interpolate(C?.interpolate==="linear"?oo:oi).scaleTo(Fs(g,C?.duration,C?.ease,()=>_(!0)),$)}):Promise.resolve(!1)}function q($,C){return g?new Promise(_=>{f?.interpolate(C?.interpolate==="linear"?oo:oi).scaleBy(Fs(g,C?.duration,C?.ease,()=>_(!0)),$)}):Promise.resolve(!1)}function B($){f?.scaleExtent($)}function U($){f?.translateExtent($)}function O($){const C=!Cn($)||$<0?0:$;f?.clickDistance(C)}return{update:b,destroy:k,setViewport:S,setViewportConstrained:x,getViewport:L,scaleTo:D,scaleBy:q,setScaleExtent:B,setTranslateExtent:U,syncViewport:E,setClickDistance:O}}var cc;(function(e){e.Line="line",e.Handle="handle"})(cc||(cc={}));var x0=J("<div><!></div>");function An(e,t){ue(t,!0);let n=y(t,"id",7,null),r=y(t,"type",7,"source"),o=y(t,"position",23,()=>ve.Top),i=y(t,"style",7),s=y(t,"class",7),a=y(t,"isConnectable",7),l=y(t,"isConnectableStart",7,!0),u=y(t,"isConnectableEnd",7,!0),d=y(t,"isValidConnection",7),p=y(t,"onconnect",7),f=y(t,"ondisconnect",7),g=y(t,"children",7),h=Te(t,["$$slots","$$events","$$legacy","$$host","id","type","position","style","class","isConnectable","isConnectableStart","isConnectableEnd","isValidConnection","onconnect","ondisconnect","children"]);const v=Dn("svelteflow__node_id"),w=Dn("svelteflow__node_connectable");let b=z(()=>r()==="target"),k=z(()=>a()!==void 0?a():w.value),x=jt(),S=z(()=>x.ariaLabelConfig),E=null;Qa(()=>{if(p()||f()){x.edges;let M=x.connectionLookup.get(`${v}-${r()}${n()?`-${n()}`:""}`);if(E&&!x1(M,E)){const Y=M??new Map;zu(E,Y,f()),zu(Y,E,p())}E=new Map(M)}});let L=z(()=>{if(!x.connection.inProgress)return[!1,!1,!1,!1,null];const{fromHandle:M,toHandle:Y,isValid:ee}=x.connection,ne=M&&M.nodeId===v&&M.type===r()&&M.id===n(),R=Y&&Y.nodeId===v&&Y.type===r()&&Y.id===n(),W=x.connectionMode===Nr.Strict?M?.type!==r():v!==M?.nodeId||n()!==M?.id;return[!0,ne,R,W,R&&ee]}),D=z(()=>qr(c(L),5)),q=z(()=>c(D)[0]),B=z(()=>c(D)[1]),U=z(()=>c(D)[2]),O=z(()=>c(D)[3]),$=z(()=>c(D)[4]);function C(M){const Y=x.onbeforeconnect?x.onbeforeconnect(M):M;Y&&(x.addEdge(Y),x.onconnect?.(M))}function _(M){const Y=Ru(M);M.currentTarget&&(Y&&M.button===0||!Y)&&ac.onPointerDown(M,{handleId:n(),nodeId:v,isTarget:c(b),connectionRadius:x.connectionRadius,domNode:x.domNode,nodeLookup:x.nodeLookup,connectionMode:x.connectionMode,lib:"svelte",autoPanOnConnect:x.autoPanOnConnect,flowId:x.flowId,isValidConnection:d()??x.isValidConnection,updateConnection:x.updateConnection,cancelConnection:x.cancelConnection,panBy:x.panBy,onConnect:C,onConnectStart:(ee,ne)=>{x.onconnectstart?.(ee,{nodeId:ne.nodeId,handleId:ne.handleId,handleType:ne.handleType})},onConnectEnd:(ee,ne)=>{x.onconnectend?.(ee,ne)},getTransform:()=>[x.viewport.x,x.viewport.y,x.viewport.zoom],getFromHandle:()=>x.connection.fromHandle,dragThreshold:x.connectionDragThreshold,handleDomNode:M.currentTarget})}function P(M){if(!v||!x.clickConnectStartHandle&&!l())return;if(!x.clickConnectStartHandle){x.onclickconnectstart?.(M,{nodeId:v,handleId:n(),handleType:r()}),x.clickConnectStartHandle={nodeId:v,type:r(),id:n()};return}const Y=qu(M.target),ee=d()??x.isValidConnection,{connectionMode:ne,clickConnectStartHandle:R,flowId:W,nodeLookup:F}=x,{connection:ie,isValid:G}=ac.isValid(M,{handle:{nodeId:v,id:n(),type:r()},connectionMode:ne,fromNodeId:R.nodeId,fromHandleId:R.id??null,fromType:R.type,isValidConnection:ee,flowId:W,doc:Y,lib:"svelte",nodeLookup:F});G&&ie&&C(ie);const me=structuredClone(Na(x.connection));delete me.inProgress,me.toPosition=me.toHandle?me.toHandle.position:null,x.onclickconnectend?.(M,me),x.clickConnectStartHandle=null}var N={get id(){return n()},set id(M=null){n(M),m()},get type(){return r()},set type(M="source"){r(M),m()},get position(){return o()},set position(M=ve.Top){o(M),m()},get style(){return i()},set style(M){i(M),m()},get class(){return s()},set class(M){s(M),m()},get isConnectable(){return a()},set isConnectable(M){a(M),m()},get isConnectableStart(){return l()},set isConnectableStart(M=!0){l(M),m()},get isConnectableEnd(){return u()},set isConnectableEnd(M=!0){u(M),m()},get isValidConnection(){return d()},set isValidConnection(M){d(M),m()},get onconnect(){return p()},set onconnect(M){p(M),m()},get ondisconnect(){return f()},set ondisconnect(M){f(M),m()},get children(){return g()},set children(M){g(M),m()}},T=x0(),Z=()=>{};Ue(T,M=>({"data-handleid":n(),"data-nodeid":v,"data-handlepos":o(),"data-id":`${x.flowId??""}-${v??""}-${n()??"null"??""}-${r()??""}`,class:["svelte-flow__handle",`svelte-flow__handle-${o()}`,x.noDragClass,x.noPanClass,o(),s()],onmousedown:_,ontouchstart:_,onclick:x.clickConnect?P:void 0,onkeypress:Z,style:i(),role:"button","aria-label":c(S)["handle.ariaLabel"],tabindex:"-1",...h,[Vn]:M}),[()=>({valid:c($),connectingto:c(U),connectingfrom:c(B),source:!c(b),target:c(b),connectablestart:l(),connectableend:u(),connectable:c(k),connectionindicator:c(k)&&(!c(q)||c(O))&&(c(q)||x.clickConnectStartHandle?u():l())})]);var X=I(T);return Xe(X,()=>g()??tt),A(T),H(e,T),ce(N)}se(An,{id:{},type:{},position:{},style:{},class:{},isConnectable:{},isConnectableStart:{},isConnectableEnd:{},isValidConnection:{},onconnect:{},ondisconnect:{},children:{}},[],[],!0);var _0=J("<!> <!>",1);function Gs(e,t){ue(t,!0);let n=y(t,"data",7),r=y(t,"targetPosition",23,()=>ve.Top),o=y(t,"sourcePosition",23,()=>ve.Bottom);var i={get data(){return n()},set data(d){n(d),m()},get targetPosition(){return r()},set targetPosition(d=ve.Top){r(d),m()},get sourcePosition(){return o()},set sourcePosition(d=ve.Bottom){o(d),m()}},s=_0(),a=oe(s);An(a,{type:"target",get position(){return r()}});var l=V(a),u=V(l);return An(u,{type:"source",get position(){return o()}}),xe(()=>Oe(l,` ${n()?.label??""} `)),H(e,s),ce(i)}se(Gs,{data:{},targetPosition:{},sourcePosition:{}},[],[],!0);var k0=J(" <!>",1);function dc(e,t){ue(t,!0);let n=y(t,"data",23,()=>({label:"Node"})),r=y(t,"sourcePosition",23,()=>ve.Bottom);var o={get data(){return n()},set data(l={label:"Node"}){n(l),m()},get sourcePosition(){return r()},set sourcePosition(l=ve.Bottom){r(l),m()}};he();var i=k0(),s=oe(i),a=V(s);return An(a,{type:"source",get position(){return r()}}),xe(()=>Oe(s,`${n()?.label??""} `)),H(e,i),ce(o)}se(dc,{data:{},sourcePosition:{}},[],[],!0);var $0=J(" <!>",1);function fc(e,t){ue(t,!0);let n=y(t,"data",23,()=>({label:"Node"})),r=y(t,"targetPosition",23,()=>ve.Top);var o={get data(){return n()},set data(l={label:"Node"}){n(l),m()},get targetPosition(){return r()},set targetPosition(l=ve.Top){r(l),m()}};he();var i=$0(),s=oe(i),a=V(s);return An(a,{type:"target",get position(){return r()}}),xe(()=>Oe(s,`${n()?.label??""} `)),H(e,i),ce(o)}se(fc,{data:{},targetPosition:{}},[],[],!0);function pc(e,t){}se(pc,{},[],[],!0);function Us(e,t,n){if(!n||!t)return;const r=n==="root"?t:t.querySelector(`.svelte-flow__${n}`);r&&r.appendChild(e)}function gc(e,t){const n=z(jt),r=z(()=>c(n).domNode);let o;return c(r)?Us(e,c(r),t):o=es(()=>{Ke(()=>{Us(e,c(r),t),o?.()})}),{async update(i){Us(e,c(r),i)},destroy(){e.parentNode&&e.parentNode.removeChild(e),o?.()}}}function hc(){let e=Se(typeof window>"u");if(c(e)){const t=es(()=>{Ke(()=>{j(e,!1),t?.()})})}return{get value(){return c(e)}}}const vc=e=>k1(e),C0=e=>Lu(e);function cn(e){return e===void 0?void 0:`${e}px`}const _i={ArrowUp:{x:0,y:-1},ArrowDown:{x:0,y:1},ArrowLeft:{x:-1,y:0},ArrowRight:{x:1,y:0}};var S0=J("<div><!></div>");const E0={hash:"svelte-w2n27y",code:".transparent.svelte-w2n27y {background:transparent;}"};function mc(e,t){ue(t,!0),He(e,E0);let n=y(t,"x",7,0),r=y(t,"y",7,0),o=y(t,"width",7),i=y(t,"height",7),s=y(t,"selectEdgeOnClick",7,!1),a=y(t,"transparent",7,!1),l=y(t,"class",7),u=y(t,"children",7),d=Te(t,["$$slots","$$events","$$legacy","$$host","x","y","width","height","selectEdgeOnClick","transparent","class","children"]);const p=jt(),f=Dn("svelteflow__edge_id");let g=z(()=>p.visible.edges.get(f)?.zIndex);var h={get x(){return n()},set x(k=0){n(k),m()},get y(){return r()},set y(k=0){r(k),m()},get width(){return o()},set width(k){o(k),m()},get height(){return i()},set height(k){i(k),m()},get selectEdgeOnClick(){return s()},set selectEdgeOnClick(k=!1){s(k),m()},get transparent(){return a()},set transparent(k=!1){a(k),m()},get class(){return l()},set class(k){l(k),m()},get children(){return u()},set children(k){u(k),m()}},v=S0(),w=()=>{s()&&f&&p.handleEdgeSelection(f)};Ue(v,k=>({class:["svelte-flow__edge-label",{transparent:a()},l()],tabindex:"-1",onclick:w,...d,[nn]:k}),[()=>({display:hc().value?"none":void 0,cursor:s()?"pointer":void 0,transform:`translate(-50%, -50%) translate(${n()??""}px,${r()??""}px)`,"pointer-events":"all",width:cn(o()),height:cn(i()),"z-index":c(g)})],void 0,"svelte-w2n27y");var b=I(v);return Xe(b,()=>u()??tt),A(v),pt(v,(k,x)=>gc?.(k,x),()=>"edge-labels"),H(e,v),ce(h)}se(mc,{x:{},y:{},width:{},height:{},selectEdgeOnClick:{},transparent:{},class:{},children:{}},[],[],!0);var N0=ge("<path></path>"),P0=ge('<path fill="none"></path><!><!>',1);function wo(e,t){ue(t,!0);let n=y(t,"id",7),r=y(t,"path",7),o=y(t,"label",7),i=y(t,"labelX",7),s=y(t,"labelY",7),a=y(t,"labelStyle",7),l=y(t,"markerStart",7),u=y(t,"markerEnd",7),d=y(t,"style",7),p=y(t,"interactionWidth",7,20),f=y(t,"class",7),g=Te(t,["$$slots","$$events","$$legacy","$$host","id","path","label","labelX","labelY","labelStyle","markerStart","markerEnd","style","interactionWidth","class"]);var h={get id(){return n()},set id(E){n(E),m()},get path(){return r()},set path(E){r(E),m()},get label(){return o()},set label(E){o(E),m()},get labelX(){return i()},set labelX(E){i(E),m()},get labelY(){return s()},set labelY(E){s(E),m()},get labelStyle(){return a()},set labelStyle(E){a(E),m()},get markerStart(){return l()},set markerStart(E){l(E),m()},get markerEnd(){return u()},set markerEnd(E){u(E),m()},get style(){return d()},set style(E){d(E),m()},get interactionWidth(){return p()},set interactionWidth(E=20){p(E),m()},get class(){return f()},set class(E){f(E),m()}},v=P0(),w=oe(v),b=V(w);{var k=E=>{var L=N0();Ue(L,()=>({d:r(),"stroke-opacity":0,"stroke-width":p(),fill:"none",class:"svelte-flow__edge-interaction",...g})),H(E,L)};ae(b,E=>{p()>0&&E(k)})}var x=V(b);{var S=E=>{mc(E,{get x(){return i()},get y(){return s()},get style(){return a()},selectEdgeOnClick:!0,children:(L,D)=>{he();var q=_e();xe(()=>Oe(q,o())),H(L,q)},$$slots:{default:!0}})};ae(x,E=>{o()&&E(S)})}return xe(()=>{ye(w,"id",n()),ye(w,"d",r()),bt(w,0,bn(["svelte-flow__edge-path",f()])),ye(w,"marker-start",l()),ye(w,"marker-end",u()),rt(w,d())}),H(e,v),ce(h)}se(wo,{id:{},path:{},label:{},labelX:{},labelY:{},labelStyle:{},markerStart:{},markerEnd:{},style:{},interactionWidth:{},class:{}},[],[],!0);function Js(e,t){ue(t,!0);let n=y(t,"id",7),r=y(t,"interactionWidth",7),o=y(t,"label",7),i=y(t,"labelStyle",7),s=y(t,"markerEnd",7),a=y(t,"markerStart",7),l=y(t,"pathOptions",7),u=y(t,"sourcePosition",7),d=y(t,"sourceX",7),p=y(t,"sourceY",7),f=y(t,"style",7),g=y(t,"targetPosition",7),h=y(t,"targetX",7),v=y(t,"targetY",7),w=z(()=>Xu({sourceX:d(),sourceY:p(),targetX:h(),targetY:v(),sourcePosition:u(),targetPosition:g(),curvature:l()?.curvature})),b=z(()=>qr(c(w),3)),k=z(()=>c(b)[0]),x=z(()=>c(b)[1]),S=z(()=>c(b)[2]);var E={get id(){return n()},set id(L){n(L),m()},get interactionWidth(){return r()},set interactionWidth(L){r(L),m()},get label(){return o()},set label(L){o(L),m()},get labelStyle(){return i()},set labelStyle(L){i(L),m()},get markerEnd(){return s()},set markerEnd(L){s(L),m()},get markerStart(){return a()},set markerStart(L){a(L),m()},get pathOptions(){return l()},set pathOptions(L){l(L),m()},get sourcePosition(){return u()},set sourcePosition(L){u(L),m()},get sourceX(){return d()},set sourceX(L){d(L),m()},get sourceY(){return p()},set sourceY(L){p(L),m()},get style(){return f()},set style(L){f(L),m()},get targetPosition(){return g()},set targetPosition(L){g(L),m()},get targetX(){return h()},set targetX(L){h(L),m()},get targetY(){return v()},set targetY(L){v(L),m()}};return wo(e,{get id(){return n()},get path(){return c(k)},get labelX(){return c(x)},get labelY(){return c(S)},get label(){return o()},get labelStyle(){return i()},get markerStart(){return a()},get markerEnd(){return s()},get interactionWidth(){return r()},get style(){return f()}}),ce(E)}se(Js,{id:{},interactionWidth:{},label:{},labelStyle:{},markerEnd:{},markerStart:{},pathOptions:{},sourcePosition:{},sourceX:{},sourceY:{},style:{},targetPosition:{},targetX:{},targetY:{}},[],[],!0);function yc(e,t){ue(t,!0);let n=y(t,"interactionWidth",7),r=y(t,"label",7),o=y(t,"labelStyle",7),i=y(t,"style",7),s=y(t,"markerEnd",7),a=y(t,"markerStart",7),l=y(t,"sourcePosition",7),u=y(t,"sourceX",7),d=y(t,"sourceY",7),p=y(t,"targetPosition",7),f=y(t,"targetX",7),g=y(t,"targetY",7),h=z(()=>Rs({sourceX:u(),sourceY:d(),targetX:f(),targetY:g(),sourcePosition:l(),targetPosition:p()})),v=z(()=>qr(c(h),3)),w=z(()=>c(v)[0]),b=z(()=>c(v)[1]),k=z(()=>c(v)[2]);var x={get interactionWidth(){return n()},set interactionWidth(S){n(S),m()},get label(){return r()},set label(S){r(S),m()},get labelStyle(){return o()},set labelStyle(S){o(S),m()},get style(){return i()},set style(S){i(S),m()},get markerEnd(){return s()},set markerEnd(S){s(S),m()},get markerStart(){return a()},set markerStart(S){a(S),m()},get sourcePosition(){return l()},set sourcePosition(S){l(S),m()},get sourceX(){return u()},set sourceX(S){u(S),m()},get sourceY(){return d()},set sourceY(S){d(S),m()},get targetPosition(){return p()},set targetPosition(S){p(S),m()},get targetX(){return f()},set targetX(S){f(S),m()},get targetY(){return g()},set targetY(S){g(S),m()}};return wo(e,{get path(){return c(w)},get labelX(){return c(b)},get labelY(){return c(k)},get label(){return r()},get labelStyle(){return o()},get markerStart(){return a()},get markerEnd(){return s()},get interactionWidth(){return n()},get style(){return i()}}),ce(x)}se(yc,{interactionWidth:{},label:{},labelStyle:{},style:{},markerEnd:{},markerStart:{},sourcePosition:{},sourceX:{},sourceY:{},targetPosition:{},targetX:{},targetY:{}},[],[],!0);function wc(e,t){ue(t,!0);let n=y(t,"sourceX",7),r=y(t,"sourceY",7),o=y(t,"targetX",7),i=y(t,"targetY",7),s=y(t,"label",7),a=y(t,"labelStyle",7),l=y(t,"markerStart",7),u=y(t,"markerEnd",7),d=y(t,"interactionWidth",7),p=y(t,"style",7),f=z(()=>ju({sourceX:n(),sourceY:r(),targetX:o(),targetY:i()})),g=z(()=>qr(c(f),3)),h=z(()=>c(g)[0]),v=z(()=>c(g)[1]),w=z(()=>c(g)[2]);var b={get sourceX(){return n()},set sourceX(k){n(k),m()},get sourceY(){return r()},set sourceY(k){r(k),m()},get targetX(){return o()},set targetX(k){o(k),m()},get targetY(){return i()},set targetY(k){i(k),m()},get label(){return s()},set label(k){s(k),m()},get labelStyle(){return a()},set labelStyle(k){a(k),m()},get markerStart(){return l()},set markerStart(k){l(k),m()},get markerEnd(){return u()},set markerEnd(k){u(k),m()},get interactionWidth(){return d()},set interactionWidth(k){d(k),m()},get style(){return p()},set style(k){p(k),m()}};return wo(e,{get path(){return c(h)},get labelX(){return c(v)},get labelY(){return c(w)},get label(){return s()},get labelStyle(){return a()},get markerStart(){return l()},get markerEnd(){return u()},get interactionWidth(){return d()},get style(){return p()}}),ce(b)}se(wc,{sourceX:{},sourceY:{},targetX:{},targetY:{},label:{},labelStyle:{},markerStart:{},markerEnd:{},interactionWidth:{},style:{}},[],[],!0);function bc(e,t){ue(t,!0);let n=y(t,"sourceX",7),r=y(t,"sourceY",7),o=y(t,"sourcePosition",7),i=y(t,"targetX",7),s=y(t,"targetY",7),a=y(t,"targetPosition",7),l=y(t,"label",7),u=y(t,"labelStyle",7),d=y(t,"markerStart",7),p=y(t,"markerEnd",7),f=y(t,"interactionWidth",7),g=y(t,"style",7),h=z(()=>Rs({sourceX:n(),sourceY:r(),targetX:i(),targetY:s(),sourcePosition:o(),targetPosition:a(),borderRadius:0})),v=z(()=>qr(c(h),3)),w=z(()=>c(v)[0]),b=z(()=>c(v)[1]),k=z(()=>c(v)[2]);var x={get sourceX(){return n()},set sourceX(S){n(S),m()},get sourceY(){return r()},set sourceY(S){r(S),m()},get sourcePosition(){return o()},set sourcePosition(S){o(S),m()},get targetX(){return i()},set targetX(S){i(S),m()},get targetY(){return s()},set targetY(S){s(S),m()},get targetPosition(){return a()},set targetPosition(S){a(S),m()},get label(){return l()},set label(S){l(S),m()},get labelStyle(){return u()},set labelStyle(S){u(S),m()},get markerStart(){return d()},set markerStart(S){d(S),m()},get markerEnd(){return p()},set markerEnd(S){p(S),m()},get interactionWidth(){return f()},set interactionWidth(S){f(S),m()},get style(){return g()},set style(S){g(S),m()}};return wo(e,{get path(){return c(w)},get labelX(){return c(b)},get labelY(){return c(k)},get label(){return l()},get labelStyle(){return u()},get markerStart(){return d()},get markerEnd(){return p()},get interactionWidth(){return f()},get style(){return g()}}),ce(x)}se(bc,{sourceX:{},sourceY:{},sourcePosition:{},targetX:{},targetY:{},targetPosition:{},label:{},labelStyle:{},markerStart:{},markerEnd:{},interactionWidth:{},style:{}},[],[],!0);class z0{#t;#e;constructor(t,n){this.#t=t,this.#e=Rf(n)}get current(){return this.#e(),this.#t()}}const L0=/\(.+\)/,D0=new Set(["all","print","screen","and","or","not","only"]);class H0 extends z0{constructor(t,n){let r=L0.test(t)||t.split(/[\s,]+/).some(i=>D0.has(i.trim()))?t:`(${t})`;const o=window.matchMedia(r);super(()=>o.matches,i=>ss(o,"change",i))}}function M0(e,t,n,r){const o=new Map;return As(e,{x:0,y:0,width:n,height:r},t,!0).forEach(i=>{o.set(i.id,i)}),o}function xc(e){const{edges:t,defaultEdgeOptions:n,nodeLookup:r,previousEdges:o,connectionMode:i,onerror:s,onlyRenderVisible:a,elevateEdgesOnSelect:l}=e,u=new Map;for(const d of t){const p=r.get(d.source),f=r.get(d.target);if(!p||!f)continue;if(a){const{visibleNodes:v,transform:w,width:b,height:k}=e;if(O1({sourceNode:p,targetNode:f,width:b,height:k,transform:w}))v.set(p.id,p),v.set(f.id,f);else continue}const g=o.get(d.id);if(g&&d===g.edge&&p==g.sourceNode&&f==g.targetNode){u.set(d.id,g);continue}const h=K1({id:d.id,sourceNode:p,targetNode:f,sourceHandle:d.sourceHandle||null,targetHandle:d.targetHandle||null,connectionMode:i,onError:s});h&&u.set(d.id,{...n,...d,...h,zIndex:V1({selected:d.selected,zIndex:d.zIndex??n.zIndex,sourceNode:p,targetNode:f,elevateOnSelect:l}),sourceNode:p,targetNode:f,edge:d})}return u}const _c={input:dc,output:fc,default:Gs,group:pc},kc={straight:wc,smoothstep:yc,default:Js,step:bc};function T0(e,t,n,r,o,i){if(t&&!n&&r&&o){const s=go(i,{filter:a=>!!((a.width||a.initialWidth)&&(a.height||a.initialHeight))});return qs(s,r,o,.5,2,.1)}else return n??{x:0,y:0,zoom:1}}function V0(e){class t{#t=z(()=>e.props.id??"1");get flowId(){return c(this.#t)}set flowId(r){j(this.#t,r)}#e=Se(null);get domNode(){return c(this.#e)}set domNode(r){j(this.#e,r)}#n=Se(null);get panZoom(){return c(this.#n)}set panZoom(r){j(this.#n,r)}#o=Se(e.width??0);get width(){return c(this.#o)}set width(r){j(this.#o,r)}#c=Se(e.height??0);get height(){return c(this.#c)}set height(r){j(this.#c,r)}#i=z(()=>{const r=F1(e.nodes,this.nodeLookup,this.parentLookup,{nodeExtent:this.nodeExtent,nodeOrigin:this.nodeOrigin,elevateNodesOnSelect:e.props.elevateNodesOnSelect??!0,checkEquality:!0});return this.fitViewQueued&&r&&(this.fitViewOptions?.duration?this.resolveFitView():queueMicrotask(()=>{this.resolveFitView()})),r});get nodesInitialized(){return c(this.#i)}set nodesInitialized(r){j(this.#i,r)}#a=z(()=>this.panZoom!==null);get viewportInitialized(){return c(this.#a)}set viewportInitialized(r){j(this.#a,r)}#s=z(()=>(t0(this.connectionLookup,this.edgeLookup,e.edges),e.edges));get _edges(){return c(this.#s)}set _edges(r){j(this.#s,r)}get nodes(){return this.nodesInitialized,e.nodes}set nodes(r){e.nodes=r}get edges(){return this._edges}set edges(r){e.edges=r}_prevSelectedNodes=[];_prevSelectedNodeIds=new Set;#r=z(()=>{const r=this._prevSelectedNodeIds.size,o=new Set,i=this.nodes.filter(s=>(s.selected&&(o.add(s.id),this._prevSelectedNodeIds.delete(s.id)),s.selected));return(r!==o.size||this._prevSelectedNodeIds.size>0)&&(this._prevSelectedNodes=i),this._prevSelectedNodeIds=o,this._prevSelectedNodes});get selectedNodes(){return c(this.#r)}set selectedNodes(r){j(this.#r,r)}_prevSelectedEdges=[];_prevSelectedEdgeIds=new Set;#l=z(()=>{const r=this._prevSelectedEdgeIds.size,o=new Set,i=this.edges.filter(s=>(s.selected&&(o.add(s.id),this._prevSelectedEdgeIds.delete(s.id)),s.selected));return(r!==o.size||this._prevSelectedEdgeIds.size>0)&&(this._prevSelectedEdges=i),this._prevSelectedEdgeIds=o,this._prevSelectedEdges});get selectedEdges(){return c(this.#l)}set selectedEdges(r){j(this.#l,r)}selectionChangeHandlers=new Map;nodeLookup=new Map;parentLookup=new Map;connectionLookup=new Map;edgeLookup=new Map;_prevVisibleEdges=new Map;#d=z(()=>{const{nodes:r,_edges:o,_prevVisibleEdges:i,nodeLookup:s,connectionMode:a,onerror:l,onlyRenderVisibleElements:u,defaultEdgeOptions:d}=this;let p,f;const g={edges:o,defaultEdgeOptions:d,previousEdges:i,nodeLookup:s,connectionMode:a,elevateEdgesOnSelect:e.props.elevateEdgesOnSelect??!0,onerror:l};if(u){const{viewport:h,width:v,height:w}=this,b=[h.x,h.y,h.zoom];p=M0(s,b,v,w),f=xc({...g,onlyRenderVisible:!0,visibleNodes:p,transform:b,width:v,height:w})}else p=this.nodeLookup,f=xc(g);return{nodes:p,edges:f}});get visible(){return c(this.#d)}set visible(r){j(this.#d,r)}#f=z(()=>e.props.nodesDraggable??!0);get nodesDraggable(){return c(this.#f)}set nodesDraggable(r){j(this.#f,r)}#g=z(()=>e.props.nodesConnectable??!0);get nodesConnectable(){return c(this.#g)}set nodesConnectable(r){j(this.#g,r)}#u=z(()=>e.props.elementsSelectable??!0);get elementsSelectable(){return c(this.#u)}set elementsSelectable(r){j(this.#u,r)}#p=z(()=>e.props.nodesFocusable??!0);get nodesFocusable(){return c(this.#p)}set nodesFocusable(r){j(this.#p,r)}#h=z(()=>e.props.edgesFocusable??!0);get edgesFocusable(){return c(this.#h)}set edgesFocusable(r){j(this.#h,r)}#v=z(()=>e.props.disableKeyboardA11y??!1);get disableKeyboardA11y(){return c(this.#v)}set disableKeyboardA11y(r){j(this.#v,r)}#m=z(()=>e.props.minZoom??.5);get minZoom(){return c(this.#m)}set minZoom(r){j(this.#m,r)}#y=z(()=>e.props.maxZoom??2);get maxZoom(){return c(this.#y)}set maxZoom(r){j(this.#y,r)}#w=z(()=>e.props.nodeOrigin??[0,0]);get nodeOrigin(){return c(this.#w)}set nodeOrigin(r){j(this.#w,r)}#b=z(()=>e.props.nodeExtent??Ts);get nodeExtent(){return c(this.#b)}set nodeExtent(r){j(this.#b,r)}#x=z(()=>e.props.translateExtent??Ts);get translateExtent(){return c(this.#x)}set translateExtent(r){j(this.#x,r)}#_=z(()=>e.props.defaultEdgeOptions??{});get defaultEdgeOptions(){return c(this.#_)}set defaultEdgeOptions(r){j(this.#_,r)}#k=z(()=>e.props.nodeDragThreshold??1);get nodeDragThreshold(){return c(this.#k)}set nodeDragThreshold(r){j(this.#k,r)}#$=z(()=>e.props.autoPanOnNodeDrag??!0);get autoPanOnNodeDrag(){return c(this.#$)}set autoPanOnNodeDrag(r){j(this.#$,r)}#C=z(()=>e.props.autoPanOnConnect??!0);get autoPanOnConnect(){return c(this.#C)}set autoPanOnConnect(r){j(this.#C,r)}#S=z(()=>e.props.autoPanOnNodeFocus??!0);get autoPanOnNodeFocus(){return c(this.#S)}set autoPanOnNodeFocus(r){j(this.#S,r)}#E=z(()=>e.props.connectionDragThreshold??1);get connectionDragThreshold(){return c(this.#E)}set connectionDragThreshold(r){j(this.#E,r)}fitViewQueued=e.props.fitView??!1;fitViewOptions=e.props.fitViewOptions;fitViewResolver=null;#N=z(()=>e.props.snapGrid??null);get snapGrid(){return c(this.#N)}set snapGrid(r){j(this.#N,r)}#P=Se(!1);get dragging(){return c(this.#P)}set dragging(r){j(this.#P,r)}#z=Se(null);get selectionRect(){return c(this.#z)}set selectionRect(r){j(this.#z,r)}#L=Se(!1);get selectionKeyPressed(){return c(this.#L)}set selectionKeyPressed(r){j(this.#L,r)}#D=Se(!1);get multiselectionKeyPressed(){return c(this.#D)}set multiselectionKeyPressed(r){j(this.#D,r)}#H=Se(!1);get deleteKeyPressed(){return c(this.#H)}set deleteKeyPressed(r){j(this.#H,r)}#M=Se(!1);get panActivationKeyPressed(){return c(this.#M)}set panActivationKeyPressed(r){j(this.#M,r)}#T=Se(!1);get zoomActivationKeyPressed(){return c(this.#T)}set zoomActivationKeyPressed(r){j(this.#T,r)}#V=Se(null);get selectionRectMode(){return c(this.#V)}set selectionRectMode(r){j(this.#V,r)}#O=Se("");get ariaLiveMessage(){return c(this.#O)}set ariaLiveMessage(r){j(this.#O,r)}#A=z(()=>e.props.selectionMode??hi.Partial);get selectionMode(){return c(this.#A)}set selectionMode(r){j(this.#A,r)}#I=z(()=>({..._c,...e.props.nodeTypes}));get nodeTypes(){return c(this.#I)}set nodeTypes(r){j(this.#I,r)}#q=z(()=>({...kc,...e.props.edgeTypes}));get edgeTypes(){return c(this.#q)}set edgeTypes(r){j(this.#q,r)}#Z=z(()=>e.props.noPanClass??"nopan");get noPanClass(){return c(this.#Z)}set noPanClass(r){j(this.#Z,r)}#R=z(()=>e.props.noDragClass??"nodrag");get noDragClass(){return c(this.#R)}set noDragClass(r){j(this.#R,r)}#B=z(()=>e.props.noWheelClass??"nowheel");get noWheelClass(){return c(this.#B)}set noWheelClass(r){j(this.#B,r)}#K=z(()=>H1(e.props.ariaLabelConfig));get ariaLabelConfig(){return c(this.#K)}set ariaLabelConfig(r){j(this.#K,r)}#X=Se(T0(this.nodesInitialized,e.props.fitView,e.props.initialViewport,this.width,this.height,this.nodeLookup));get _viewport(){return c(this.#X)}set _viewport(r){j(this.#X,r)}get viewport(){return e.viewport??this._viewport}set viewport(r){e.viewport&&(e.viewport=r),this._viewport=r}#Y=Se(Vs);get _connection(){return c(this.#Y)}set _connection(r){j(this.#Y,r)}#j=z(()=>this._connection.inProgress?{...this._connection,to:mo(this._connection.to,[this.viewport.x,this.viewport.y,this.viewport.zoom])}:this._connection);get connection(){return c(this.#j)}set connection(r){j(this.#j,r)}#W=z(()=>e.props.connectionMode??Nr.Strict);get connectionMode(){return c(this.#W)}set connectionMode(r){j(this.#W,r)}#F=z(()=>e.props.connectionRadius??20);get connectionRadius(){return c(this.#F)}set connectionRadius(r){j(this.#F,r)}#G=z(()=>e.props.isValidConnection??(()=>!0));get isValidConnection(){return c(this.#G)}set isValidConnection(r){j(this.#G,r)}#U=z(()=>e.props.selectNodesOnDrag??!0);get selectNodesOnDrag(){return c(this.#U)}set selectNodesOnDrag(r){j(this.#U,r)}#J=z(()=>e.props.defaultMarkerColor===void 0?"#b1b1b7":e.props.defaultMarkerColor);get defaultMarkerColor(){return c(this.#J)}set defaultMarkerColor(r){j(this.#J,r)}#Q=z(()=>X1(e.edges,{defaultColor:this.defaultMarkerColor,id:this.flowId,defaultMarkerStart:this.defaultEdgeOptions.markerStart,defaultMarkerEnd:this.defaultEdgeOptions.markerEnd}));get markers(){return c(this.#Q)}set markers(r){j(this.#Q,r)}#ee=z(()=>e.props.onlyRenderVisibleElements??!1);get onlyRenderVisibleElements(){return c(this.#ee)}set onlyRenderVisibleElements(r){j(this.#ee,r)}#te=z(()=>e.props.onflowerror??P1);get onerror(){return c(this.#te)}set onerror(r){j(this.#te,r)}#ne=z(()=>e.props.ondelete);get ondelete(){return c(this.#ne)}set ondelete(r){j(this.#ne,r)}#re=z(()=>e.props.onbeforedelete);get onbeforedelete(){return c(this.#re)}set onbeforedelete(r){j(this.#re,r)}#oe=z(()=>e.props.onbeforeconnect);get onbeforeconnect(){return c(this.#oe)}set onbeforeconnect(r){j(this.#oe,r)}#ie=z(()=>e.props.onconnect);get onconnect(){return c(this.#ie)}set onconnect(r){j(this.#ie,r)}#se=z(()=>e.props.onconnectstart);get onconnectstart(){return c(this.#se)}set onconnectstart(r){j(this.#se,r)}#ae=z(()=>e.props.onconnectend);get onconnectend(){return c(this.#ae)}set onconnectend(r){j(this.#ae,r)}#le=z(()=>e.props.onbeforereconnect);get onbeforereconnect(){return c(this.#le)}set onbeforereconnect(r){j(this.#le,r)}#ue=z(()=>e.props.onreconnect);get onreconnect(){return c(this.#ue)}set onreconnect(r){j(this.#ue,r)}#ce=z(()=>e.props.onreconnectstart);get onreconnectstart(){return c(this.#ce)}set onreconnectstart(r){j(this.#ce,r)}#de=z(()=>e.props.onreconnectend);get onreconnectend(){return c(this.#de)}set onreconnectend(r){j(this.#de,r)}#fe=z(()=>e.props.clickConnect??!0);get clickConnect(){return c(this.#fe)}set clickConnect(r){j(this.#fe,r)}#pe=z(()=>e.props.onclickconnectstart);get onclickconnectstart(){return c(this.#pe)}set onclickconnectstart(r){j(this.#pe,r)}#ge=z(()=>e.props.onclickconnectend);get onclickconnectend(){return c(this.#ge)}set onclickconnectend(r){j(this.#ge,r)}#he=Se(null);get clickConnectStartHandle(){return c(this.#he)}set clickConnectStartHandle(r){j(this.#he,r)}#ve=z(()=>e.props.onselectiondrag);get onselectiondrag(){return c(this.#ve)}set onselectiondrag(r){j(this.#ve,r)}#me=z(()=>e.props.onselectiondragstart);get onselectiondragstart(){return c(this.#me)}set onselectiondragstart(r){j(this.#me,r)}#ye=z(()=>e.props.onselectiondragstop);get onselectiondragstop(){return c(this.#ye)}set onselectiondragstop(r){j(this.#ye,r)}resolveFitView=async()=>{this.panZoom&&(await E1({nodes:this.nodeLookup,width:this.width,height:this.height,panZoom:this.panZoom,minZoom:this.minZoom,maxZoom:this.maxZoom},this.fitViewOptions),this.fitViewResolver?.resolve(!0),this.fitViewQueued=!1,this.fitViewOptions=void 0,this.fitViewResolver=null)};_prefersDark=new H0("(prefers-color-scheme: dark)",e.props.colorModeSSR==="dark");#we=z(()=>e.props.colorMode==="system"?this._prefersDark.current?"dark":"light":e.props.colorMode??"light");get colorMode(){return c(this.#we)}set colorMode(r){j(this.#we,r)}constructor(){}resetStoreValues(){this.dragging=!1,this.selectionRect=null,this.selectionRectMode=null,this.selectionKeyPressed=!1,this.multiselectionKeyPressed=!1,this.deleteKeyPressed=!1,this.panActivationKeyPressed=!1,this.zoomActivationKeyPressed=!1,this._connection=Vs,this.clickConnectStartHandle=null,this.viewport=e.props.initialViewport??{x:0,y:0,zoom:1},this.ariaLiveMessage=""}}return new t}function jt(){const e=Dn(ki);if(!e)throw new Error("To call useStore outside of <SvelteFlow /> you need to wrap your component in a <SvelteFlowProvider />");return e.getStore()}const ki=Symbol();function $c(e){const t=V0(e);function n(O){t.nodeTypes={..._c,...O}}function r(O){t.edgeTypes={...kc,...O}}function o(O){t.edges=q1(O,t.edges)}const i=(O,$=!1)=>{t.nodes=t.nodes.map(C=>{const _=O.get(C.id);return _?{...C,position:_.position,dragging:$}:C})};function s(O){const{changes:$,updatedInternals:C}=Q1(O,t.nodeLookup,t.parentLookup,t.domNode,t.nodeOrigin);if(!C)return;W1(t.nodeLookup,t.parentLookup,{nodeOrigin:t.nodeOrigin,nodeExtent:t.nodeExtent}),t.fitViewQueued&&t.resolveFitView();const _=new Map;for(const P of $){const N=t.nodeLookup.get(P.id)?.internals.userNode;if(!N)continue;const T={...N};switch(P.type){case"dimensions":{const Z={...T.measured,...P.dimensions};P.setAttributes&&(T.width=P.dimensions?.width??T.width,T.height=P.dimensions?.height??T.height),T.measured=Z;break}case"position":T.position=P.position??T.position;break}_.set(P.id,T)}t.nodes=t.nodes.map(P=>_.get(P.id)??P)}function a(O){const $=t.fitViewResolver??Promise.withResolvers();return t.fitViewQueued=!0,t.fitViewOptions=O,t.fitViewResolver=$,t.nodes=[...t.nodes],$.promise}async function l(O,$,C){const _=typeof C?.zoom<"u"?C.zoom:t.maxZoom,P=t.panZoom;return P?(await P.setViewport({x:t.width/2-O*_,y:t.height/2-$*_,zoom:_},{duration:C?.duration,ease:C?.ease,interpolate:C?.interpolate}),Promise.resolve(!0)):Promise.resolve(!1)}function u(O,$){const C=t.panZoom;return C?C.scaleBy(O,$):Promise.resolve(!1)}function d(O){return u(1.2,O)}function p(O){return u(1/1.2,O)}function f(O){const $=t.panZoom;$&&($.setScaleExtent([O,t.maxZoom]),t.minZoom=O)}function g(O){const $=t.panZoom;$&&($.setScaleExtent([t.minZoom,O]),t.maxZoom=O)}function h(O){const $=t.panZoom;$&&($.setTranslateExtent(O),t.translateExtent=O)}function v(O){t.panZoom?.setClickDistance(O)}function w(O,$=null){let C=!1;const _=O.map(P=>(!$||$.has(P.id))&&P.selected?(C=!0,{...P,selected:!1}):P);return[C,_]}function b(O){const $=O?.nodes?new Set(O.nodes.map(Z=>Z.id)):null,[C,_]=w(t.nodes,$);C&&(t.nodes=_);const P=O?.edges?new Set(O.edges.map(Z=>Z.id)):null,[N,T]=w(t.edges,P);N&&(t.edges=T)}function k(O){const $=t.multiselectionKeyPressed;t.nodes=t.nodes.map(C=>{const _=O.includes(C.id),P=$&&C.selected||_;if(C.selected!==P){const N=t.nodeLookup.get(C.id);return N&&(N.selected=P),C.selected=P,{...C}}return C}),$||b({nodes:[]})}function x(O){const $=t.multiselectionKeyPressed;t.edges=t.edges.map(C=>{const _=O.includes(C.id),P=$&&C.selected||_;return C.selected!==P?{...C,selected:P}:C}),$||b({edges:[]})}function S(O,$,C){const _=t.nodeLookup.get(O);if(!_){console.warn("012",co.error012(O));return}t.selectionRect=null,t.selectionRectMode=null,_.selected?($||_.selected&&t.multiselectionKeyPressed)&&(b({nodes:[_],edges:[]}),requestAnimationFrame(()=>C?.blur())):k([O])}function E(O){const $=t.edgeLookup.get(O);if(!$){console.warn("012",co.error012(O));return}($.selectable||t.elementsSelectable&&typeof $.selectable>"u")&&(t.selectionRect=null,t.selectionRectMode=null,$.selected?$.selected&&t.multiselectionKeyPressed&&b({nodes:[],edges:[$]}):x([O]))}function L(O,$){const{nodeExtent:C,snapGrid:_,nodeOrigin:P,nodeLookup:N,nodesDraggable:T,onerror:Z}=t,X=new Map,M=_?.[0]??5,Y=_?.[1]??5,ee=O.x*M*$,ne=O.y*Y*$;for(const R of N.values()){if(!(R.selected&&(R.draggable||T&&typeof R.draggable>"u")))continue;let W={x:R.internals.positionAbsolute.x+ee,y:R.internals.positionAbsolute.y+ne};_&&(W=vo(W,_));const{position:F,positionAbsolute:ie}=Du({nodeId:R.id,nextPosition:W,nodeLookup:N,nodeExtent:C,nodeOrigin:P,onError:Z});R.position=F,R.internals.positionAbsolute=ie,X.set(R.id,R)}i(X)}function D(O){return e0({delta:O,panZoom:t.panZoom,transform:[t.viewport.x,t.viewport.y,t.viewport.zoom],translateExtent:t.translateExtent,width:t.width,height:t.height})}const q=O=>{t._connection={...O}};function B(){t._connection=Vs}function U(){t.resetStoreValues(),b()}return Object.assign(t,{setNodeTypes:n,setEdgeTypes:r,addEdge:o,updateNodePositions:i,updateNodeInternals:s,zoomIn:d,zoomOut:p,fitView:a,setCenter:l,setMinZoom:f,setMaxZoom:g,setTranslateExtent:h,setPaneClickDistance:v,unselectNodesAndEdges:b,addSelectedNodes:k,addSelectedEdges:x,handleNodeSelection:S,handleEdgeSelection:E,moveSelectedNodes:L,panBy:D,updateConnection:q,cancelConnection:B,reset:U})}function O0(e,t){const{minZoom:n,maxZoom:r,initialViewport:o,onPanZoomStart:i,onPanZoom:s,onPanZoomEnd:a,translateExtent:l,paneClickDistance:u,setPanZoomInstance:d,onDraggingChange:p,onTransformChange:f}=t,g=b0({domNode:e,minZoom:n,maxZoom:r,translateExtent:l,viewport:o,paneClickDistance:u,onPanZoom:s,onPanZoomStart:i,onPanZoomEnd:a,onDraggingChange:p}),h=g.getViewport();return(o.x!==h.x||o.y!==h.y||o.zoom!==h.zoom)&&f([h.x,h.y,h.zoom]),d(g),g.update(t),{update(v){g.update(v)}}}var A0=J('<div class="svelte-flow__zoom svelte-flow__container"><!></div>');function Cc(e,t){ue(t,!0);let n=y(t,"store",15),r=y(t,"panOnScrollMode",23,()=>ln.Free),o=y(t,"preventScrolling",7,!0),i=y(t,"zoomOnScroll",7,!0),s=y(t,"zoomOnDoubleClick",7,!0),a=y(t,"zoomOnPinch",7,!0),l=y(t,"panOnDrag",7,!0),u=y(t,"panOnScroll",7,!1),d=y(t,"paneClickDistance",7,1),p=y(t,"onmovestart",7),f=y(t,"onmove",7),g=y(t,"onmoveend",7),h=y(t,"oninit",7),v=y(t,"children",7),w=z(()=>n().panActivationKeyPressed||l()),b=z(()=>n().panActivationKeyPressed||u());const{viewport:k}=n();let x=!1;Ke(()=>{!x&&n().viewportInitialized&&(h()?.(),x=!0)});var S={get store(){return n()},set store(D){n(D),m()},get panOnScrollMode(){return r()},set panOnScrollMode(D=ln.Free){r(D),m()},get preventScrolling(){return o()},set preventScrolling(D=!0){o(D),m()},get zoomOnScroll(){return i()},set zoomOnScroll(D=!0){i(D),m()},get zoomOnDoubleClick(){return s()},set zoomOnDoubleClick(D=!0){s(D),m()},get zoomOnPinch(){return a()},set zoomOnPinch(D=!0){a(D),m()},get panOnDrag(){return l()},set panOnDrag(D=!0){l(D),m()},get panOnScroll(){return u()},set panOnScroll(D=!1){u(D),m()},get paneClickDistance(){return d()},set paneClickDistance(D=1){d(D),m()},get onmovestart(){return p()},set onmovestart(D){p(D),m()},get onmove(){return f()},set onmove(D){f(D),m()},get onmoveend(){return g()},set onmoveend(D){g(D),m()},get oninit(){return h()},set oninit(D){h(D),m()},get children(){return v()},set children(D){v(D),m()}},E=A0(),L=I(E);return Xe(L,v),A(E),pt(E,(D,q)=>O0?.(D,q),()=>({viewport:n().viewport,minZoom:n().minZoom,maxZoom:n().maxZoom,initialViewport:k,onDraggingChange:D=>{n(n().dragging=D,!0)},setPanZoomInstance:D=>{n(n().panZoom=D,!0)},onPanZoomStart:p(),onPanZoom:f(),onPanZoomEnd:g(),zoomOnScroll:i(),zoomOnDoubleClick:s(),zoomOnPinch:a(),panOnScroll:c(b),panOnDrag:c(w),panOnScrollSpeed:.5,panOnScrollMode:r()||ln.Free,zoomActivationKeyPressed:n().zoomActivationKeyPressed,preventScrolling:typeof o()=="boolean"?o():!0,noPanClassName:n().noPanClass,noWheelClassName:n().noWheelClass,userSelectionActive:!!n().selectionRect,translateExtent:n().translateExtent,lib:"svelte",paneClickDistance:d(),onTransformChange:D=>{n(n().viewport={x:D[0],y:D[1],zoom:D[2]},!0)}})),H(e,E),ce(S)}se(Cc,{store:{},panOnScrollMode:{},preventScrolling:{},zoomOnScroll:{},zoomOnDoubleClick:{},zoomOnPinch:{},panOnDrag:{},panOnScroll:{},paneClickDistance:{},onmovestart:{},onmove:{},onmoveend:{},oninit:{},children:{}},[],[],!0);function Sc(e,t){return n=>{n.target===t&&e?.(n)}}function Ec(e){return t=>{const n=e.has(t.id);return!!t.selected!==n?{...t,selected:n}:t}}function Nc(e,t){if(e.size!==t.size)return!1;for(const n of e)if(!t.has(n))return!1;return!0}var I0=J("<div><!></div>");function Pc(e,t){ue(t,!0);let n=y(t,"store",15),r=y(t,"panOnDrag",7,!0),o=y(t,"selectionOnDrag",7),i=y(t,"onpaneclick",7),s=y(t,"onpanecontextmenu",7),a=y(t,"onselectionstart",7),l=y(t,"onselectionend",7),u=y(t,"children",7),d,p=null,f=new Set,g=new Set,h=z(()=>n().panActivationKeyPressed||r()),v=z(()=>n().selectionKeyPressed||n().selectionRect||o()&&c(h)!==!0),w=z(()=>n().elementsSelectable&&(c(v)||n().selectionRectMode==="user")),b=!1;function k(C){if(b||n().connection.inProgress){b=!1;return}i()?.({event:C}),n().unselectNodesAndEdges(),n(n().selectionRectMode=null,!0)}function x(C){if(p=d?.getBoundingClientRect(),!n().elementsSelectable||!c(v)||C.button!==0||C.target!==d||!p)return;C.target?.setPointerCapture?.(C.pointerId);const{x:_,y:P}=un(C,p);n().unselectNodesAndEdges(),n(n().selectionRect={width:0,height:0,startX:_,startY:P,x:_,y:P},!0),a()?.(C)}function S(C){if(!c(v)||!p||!n().selectionRect)return;b=!0;const _=un(C,p),{startX:P=0,startY:N=0}=n().selectionRect,T={...n().selectionRect,x:_.x<P?_.x:P,y:_.y<N?_.y:N,width:Math.abs(_.x-P),height:Math.abs(_.y-N)},Z=f,X=g;f=new Set(As(n().nodeLookup,T,[n().viewport.x,n().viewport.y,n().viewport.zoom],n().selectionMode===hi.Partial,!0).map(Y=>Y.id));const M=n().defaultEdgeOptions.selectable??!0;g=new Set;for(const Y of f){const ee=n().connectionLookup.get(Y);if(ee)for(const{edgeId:ne}of ee.values()){const R=n().edgeLookup.get(ne);R&&(R.selectable??M)&&g.add(ne)}}Nc(Z,f)||n(n().nodes=n().nodes.map(Ec(f)),!0),Nc(X,g)||n(n().edges=n().edges.map(Ec(g)),!0),n(n().selectionRectMode="user",!0),n(n().selectionRect=T,!0)}function E(C){C.button===0&&(C.target?.releasePointerCapture?.(C.pointerId),!c(v)&&n().selectionRectMode==="user"&&C.target===d&&k?.(C),n(n().selectionRect=null,!0),f.size>0&&n(n().selectionRectMode="nodes",!0),n().selectionKeyPressed&&(b=!1),l()?.(C))}const L=C=>{if(Array.isArray(c(h))&&c(h).includes(2)){C.preventDefault();return}s()?.({event:C})};var D={get store(){return n()},set store(C){n(C),m()},get panOnDrag(){return r()},set panOnDrag(C=!0){r(C),m()},get selectionOnDrag(){return o()},set selectionOnDrag(C){o(C),m()},get onpaneclick(){return i()},set onpaneclick(C){i(C),m()},get onpanecontextmenu(){return s()},set onpanecontextmenu(C){s(C),m()},get onselectionstart(){return a()},set onselectionstart(C){a(C),m()},get onselectionend(){return l()},set onselectionend(C){l(C),m()},get children(){return u()},set children(C){u(C),m()}},q=I0();let B;var U=z(()=>c(w)?void 0:Sc(k,d));q.__click=function(...C){c(U)?.apply(this,C)},q.__pointerdown=function(...C){(c(w)?x:void 0)?.apply(this,C)},q.__pointermove=function(...C){(c(w)?S:void 0)?.apply(this,C)},q.__pointerup=function(...C){(c(w)?E:void 0)?.apply(this,C)};var O=z(()=>Sc(L,d));q.__contextmenu=function(...C){c(O)?.apply(this,C)};var $=I(q);return Xe($,u),A(q),Et(q,C=>d=C,()=>d),xe(C=>B=bt(q,1,"svelte-flow__pane svelte-flow__container",null,B,C),[()=>({draggable:r()===!0||Array.isArray(r())&&r().includes(0),dragging:n().dragging,selection:c(v)})]),H(e,q),ce(D)}wn(["click","pointerdown","pointermove","pointerup","contextmenu"]),se(Pc,{store:{},panOnDrag:{},selectionOnDrag:{},onpaneclick:{},onpanecontextmenu:{},onselectionstart:{},onselectionend:{},children:{}},[],[],!0);var q0=J('<div class="svelte-flow__viewport xyflow__viewport svelte-flow__container"><!></div>');function zc(e,t){ue(t,!0);let n=y(t,"store",15),r=y(t,"children",7);var o={get store(){return n()},set store(l){n(l),m()},get children(){return r()},set children(l){r(l),m()}},i=q0();let s;var a=I(i);return Xe(a,r),A(i),xe(l=>s=rt(i,"",s,l),[()=>({transform:`translate(${n().viewport.x??""}px, ${n().viewport.y??""}px) scale(${n().viewport.zoom??""})`})]),H(e,i),ce(o)}se(zc,{store:{},children:{}},[],[],!0);function Lc(e,t){const{store:n,onDrag:r,onDragStart:o,onDragStop:i,onNodeMouseDown:s}=t,a=i0({onDrag:r,onDragStart:o,onDragStop:i,onNodeMouseDown:s,getStoreItems:()=>{const{snapGrid:u,viewport:d}=n;return{nodes:n.nodes,nodeLookup:n.nodeLookup,edges:n.edges,nodeExtent:n.nodeExtent,snapGrid:u||[0,0],snapToGrid:!!u,nodeOrigin:n.nodeOrigin,multiSelectionActive:n.multiselectionKeyPressed,domNode:n.domNode,transform:[d.x,d.y,d.zoom],autoPanOnNodeDrag:n.autoPanOnNodeDrag,nodesDraggable:n.nodesDraggable,selectNodesOnDrag:n.selectNodesOnDrag,nodeDragThreshold:n.nodeDragThreshold,unselectNodesAndEdges:n.unselectNodesAndEdges,updateNodePositions:n.updateNodePositions,onSelectionDrag:n.onselectiondrag,onSelectionDragStart:n.onselectiondragstart,onSelectionDragStop:n.onselectiondragstop,panBy:n.panBy}}});function l(u,d){if(d.disabled){a.destroy();return}a.update({domNode:u,noDragClassName:d.noDragClass,handleSelector:d.handleSelector,nodeId:d.nodeId,isSelectable:d.isSelectable,nodeClickDistance:d.nodeClickDistance})}return l(e,t),{update(u){l(e,u)},destroy(){a.destroy()}}}var Z0=J('<div aria-live="assertive" aria-atomic="true" class="a11y-live-msg svelte-62ze0y"> </div>'),R0=J('<div class="a11y-hidden svelte-62ze0y"> </div> <div class="a11y-hidden svelte-62ze0y"> </div> <!>',1);const B0={hash:"svelte-62ze0y",code:".a11y-hidden.svelte-62ze0y {display:none;}.a11y-live-msg.svelte-62ze0y {position:absolute;width:1px;height:1px;margin:-1px;border:0;padding:0;overflow:hidden;clip:rect(0px, 0px, 0px, 0px);clip-path:inset(100%);}"};function Dc(e,t){ue(t,!0),He(e,B0);let n=y(t,"store",7);var r={get store(){return n()},set store(p){n(p),m()}},o=R0(),i=oe(o),s=I(i,!0);A(i);var a=V(i,2),l=I(a,!0);A(a);var u=V(a,2);{var d=p=>{var f=Z0(),g=I(f,!0);A(f),xe(()=>{ye(f,"id",`${K0}-${n().flowId}`),Oe(g,n().ariaLiveMessage)}),H(p,f)};ae(u,p=>{n().disableKeyboardA11y||p(d)})}return xe(()=>{ye(i,"id",`${Hc}-${n().flowId}`),Oe(s,n().disableKeyboardA11y?n().ariaLabelConfig["node.a11yDescription.default"]:n().ariaLabelConfig["node.a11yDescription.keyboardDisabled"]),ye(a,"id",`${Mc}-${n().flowId}`),Oe(l,n().ariaLabelConfig["edge.a11yDescription.default"])}),H(e,o),ce(r)}se(Dc,{store:{}},[],[],!0);const Hc="svelte-flow__node-desc",Mc="svelte-flow__edge-desc",K0="svelte-flow__aria-live";var X0=J("<div><!></div>");function Tc(e,t){ue(t,!0);let n=y(t,"store",15),r=y(t,"node",7),o=y(t,"resizeObserver",7),i=y(t,"nodeClickDistance",7),s=y(t,"onnodeclick",7),a=y(t,"onnodedrag",7),l=y(t,"onnodedragstart",7),u=y(t,"onnodedragstop",7),d=y(t,"onnodepointerenter",7),p=y(t,"onnodepointerleave",7),f=y(t,"onnodepointermove",7),g=y(t,"onnodecontextmenu",7),h=z(()=>gt(r().data,()=>({}),!0)),v=z(()=>gt(r().selected,!1)),w=z(()=>r().draggable),b=z(()=>r().selectable),k=z(()=>gt(r().deletable,!0)),x=z(()=>r().connectable),S=z(()=>r().focusable),E=z(()=>gt(r().hidden,!1)),L=z(()=>gt(r().dragging,!1)),D=z(()=>gt(r().style,"")),q=z(()=>r().class),B=z(()=>gt(r().type,"default")),U=z(()=>r().parentId),O=z(()=>r().sourcePosition),$=z(()=>r().targetPosition),C=z(()=>gt(r().measured,()=>({width:0,height:0}),!0).width),_=z(()=>gt(r().measured,()=>({width:0,height:0}),!0).height),P=z(()=>r().initialWidth),N=z(()=>r().initialHeight),T=z(()=>r().width),Z=z(()=>r().height),X=z(()=>r().dragHandle),M=z(()=>gt(r().internals.z,0)),Y=z(()=>r().internals.positionAbsolute.x),ee=z(()=>r().internals.positionAbsolute.y),ne=z(()=>r().internals.userNode),{id:R}=r(),W=z(()=>c(w)??n().nodesDraggable),F=z(()=>c(b)??n().elementsSelectable),ie=z(()=>c(x)??n().nodesConnectable),G=z(()=>Au(r())),me=z(()=>!!r().internals.handleBounds),we=z(()=>c(G)&&c(me)),re=z(()=>c(S)??n().nodesFocusable);function Q(pe){return n().parentLookup.has(pe)}let te=z(()=>Q(R)),de=Se(null),fe=null,le=c(B),Ne=c(O),ke=c($),K=z(()=>n().nodeTypes[c(B)]??Gs),st=z(()=>n().ariaLabelConfig);mr("svelteflow__node_connectable",{get value(){return c(ie)}}),mr("svelteflow__node_id",R);let De=z(()=>{const pe=c(C)===void 0?c(T)??c(P):c(T),Be=c(_)===void 0?c(Z)??c(N):c(Z);if(!(pe===void 0&&Be===void 0&&c(D)===void 0))return`${c(D)};${pe?`width:${cn(pe)};`:""}${Be?`height:${cn(Be)};`:""}`});Ke(()=>{(c(B)!==le||c(O)!==Ne||c($)!==ke)&&c(de)!==null&&requestAnimationFrame(()=>{c(de)!==null&&n().updateNodeInternals(new Map([[R,{id:R,nodeElement:c(de),force:!0}]]))}),le=c(B),Ne=c(O),ke=c($)}),Ke(()=>{o()&&(!c(we)||c(de)!==fe)&&(fe&&o().unobserve(fe),c(de)&&o().observe(c(de)),fe=c(de))}),Ro(()=>{fe&&o()?.unobserve(fe)});function qe(pe){c(F)&&(!n().selectNodesOnDrag||!c(W)||n().nodeDragThreshold>0)&&n().handleNodeSelection(R),s()?.({node:c(ne),event:pe})}function Me(pe){if(!(Zu(pe)||n().disableKeyboardA11y))if(Nu.includes(pe.key)&&c(F)){const Be=pe.key==="Escape";n().handleNodeSelection(R,Be,c(de))}else c(W)&&r().selected&&Object.prototype.hasOwnProperty.call(_i,pe.key)&&(pe.preventDefault(),n(n().ariaLiveMessage=c(st)["node.a11yDescription.ariaLiveMessage"]({direction:pe.key.replace("Arrow","").toLowerCase(),x:~~r().internals.positionAbsolute.x,y:~~r().internals.positionAbsolute.y}),!0),n().moveSelectedNodes(_i[pe.key],pe.shiftKey?4:1))}const at=()=>{if(n().disableKeyboardA11y||!n().autoPanOnNodeFocus||!c(de)?.matches(":focus-visible"))return;const{width:pe,height:Be,viewport:ut}=n();As(new Map([[R,r()]]),{x:0,y:0,width:pe,height:Be},[ut.x,ut.y,ut.zoom],!0).length>0||n().setCenter(r().position.x+(r().measured.width??0)/2,r().position.y+(r().measured.height??0)/2,{zoom:ut.zoom})};var lt={get store(){return n()},set store(pe){n(pe),m()},get node(){return r()},set node(pe){r(pe),m()},get resizeObserver(){return o()},set resizeObserver(pe){o(pe),m()},get nodeClickDistance(){return i()},set nodeClickDistance(pe){i(pe),m()},get onnodeclick(){return s()},set onnodeclick(pe){s(pe),m()},get onnodedrag(){return a()},set onnodedrag(pe){a(pe),m()},get onnodedragstart(){return l()},set onnodedragstart(pe){l(pe),m()},get onnodedragstop(){return u()},set onnodedragstop(pe){u(pe),m()},get onnodepointerenter(){return d()},set onnodepointerenter(pe){d(pe),m()},get onnodepointerleave(){return p()},set onnodepointerleave(pe){p(pe),m()},get onnodepointermove(){return f()},set onnodepointermove(pe){f(pe),m()},get onnodecontextmenu(){return g()},set onnodecontextmenu(pe){g(pe),m()}},At=Ce(),Ve=oe(At);{var Fe=pe=>{var Be=X0();Ue(Be,(et,zt)=>({"data-id":R,class:["svelte-flow__node",`svelte-flow__node-${c(B)}`,c(q)],style:c(De),onclick:qe,onpointerenter:d()?je=>d()({node:c(ne),event:je}):void 0,onpointerleave:p()?je=>p()({node:c(ne),event:je}):void 0,onpointermove:f()?je=>f()({node:c(ne),event:je}):void 0,oncontextmenu:g()?je=>g()({node:c(ne),event:je}):void 0,onkeydown:c(re)?Me:void 0,onfocus:c(re)?at:void 0,tabIndex:c(re)?0:void 0,role:r().ariaRole??(c(re)?"group":void 0),"aria-roledescription":"node","aria-describedby":n().disableKeyboardA11y?void 0:`${Hc}-${n().flowId}`,...r().domAttributes,[Vn]:et,[nn]:zt}),[()=>({dragging:c(L),selected:c(v),draggable:c(W),connectable:c(ie),selectable:c(F),nopan:c(W),parent:c(te)}),()=>({"z-index":c(M),transform:`translate(${c(Y)??""}px, ${c(ee)??""}px)`,visibility:c(G)?"visible":"hidden"})]);var ut=I(Be);ds(ut,()=>c(K),(et,zt)=>{zt(et,{get data(){return c(h)},get id(){return R},get selected(){return c(v)},get selectable(){return c(F)},get deletable(){return c(k)},get sourcePosition(){return c(O)},get targetPosition(){return c($)},get zIndex(){return c(M)},get dragging(){return c(L)},get draggable(){return c(W)},get dragHandle(){return c(X)},get parentId(){return c(U)},get type(){return c(B)},get isConnectable(){return c(ie)},get positionAbsoluteX(){return c(Y)},get positionAbsoluteY(){return c(ee)},get width(){return c(T)},get height(){return c(Z)}})}),A(Be),pt(Be,(et,zt)=>Lc?.(et,zt),()=>({nodeId:R,isSelectable:c(F),disabled:!c(W),handleSelector:c(X),noDragClass:n().noDragClass,nodeClickDistance:i(),onNodeMouseDown:n().handleNodeSelection,onDrag:(et,zt,je,kt)=>{a()?.({event:et,targetNode:je,nodes:kt})},onDragStart:(et,zt,je,kt)=>{l()?.({event:et,targetNode:je,nodes:kt})},onDragStop:(et,zt,je,kt)=>{u()?.({event:et,targetNode:je,nodes:kt})},store:n()})),Et(Be,et=>j(de,et),()=>c(de)),H(pe,Be)};ae(Ve,pe=>{c(E)||pe(Fe)})}return H(e,At),ce(lt)}se(Tc,{store:{},node:{},resizeObserver:{},nodeClickDistance:{},onnodeclick:{},onnodedrag:{},onnodedragstart:{},onnodedragstop:{},onnodepointerenter:{},onnodepointerleave:{},onnodepointermove:{},onnodecontextmenu:{}},[],[],!0);var Y0=J('<div class="svelte-flow__nodes"></div>');function Vc(e,t){ue(t,!0);let n=y(t,"store",15),r=y(t,"nodeClickDistance",7),o=y(t,"onnodeclick",7),i=y(t,"onnodecontextmenu",7),s=y(t,"onnodepointerenter",7),a=y(t,"onnodepointermove",7),l=y(t,"onnodepointerleave",7),u=y(t,"onnodedrag",7),d=y(t,"onnodedragstart",7),p=y(t,"onnodedragstop",7);const f=typeof ResizeObserver>"u"?null:new ResizeObserver(v=>{const w=new Map;v.forEach(b=>{const k=b.target.getAttribute("data-id");w.set(k,{id:k,nodeElement:b.target,force:!0})}),n().updateNodeInternals(w)});Ro(()=>{f?.disconnect()});var g={get store(){return n()},set store(v){n(v),m()},get nodeClickDistance(){return r()},set nodeClickDistance(v){r(v),m()},get onnodeclick(){return o()},set onnodeclick(v){o(v),m()},get onnodecontextmenu(){return i()},set onnodecontextmenu(v){i(v),m()},get onnodepointerenter(){return s()},set onnodepointerenter(v){s(v),m()},get onnodepointermove(){return a()},set onnodepointermove(v){a(v),m()},get onnodepointerleave(){return l()},set onnodepointerleave(v){l(v),m()},get onnodedrag(){return u()},set onnodedrag(v){u(v),m()},get onnodedragstart(){return d()},set onnodedragstart(v){d(v),m()},get onnodedragstop(){return p()},set onnodedragstop(v){p(v),m()}},h=Y0();return ft(h,21,()=>n().visible.nodes.values(),v=>v.id,(v,w)=>{Tc(v,{get node(){return c(w)},get resizeObserver(){return f},get nodeClickDistance(){return r()},get onnodeclick(){return o()},get onnodepointerenter(){return s()},get onnodepointermove(){return a()},get onnodepointerleave(){return l()},get onnodedrag(){return u()},get onnodedragstart(){return d()},get onnodedragstop(){return p()},get onnodecontextmenu(){return i()},get store(){return n()},set store(b){n(b)}})}),A(h),H(e,h),ce(g)}se(Vc,{store:{},nodeClickDistance:{},onnodeclick:{},onnodecontextmenu:{},onnodepointerenter:{},onnodepointermove:{},onnodepointerleave:{},onnodedrag:{},onnodedragstart:{},onnodedragstop:{}},[],[],!0);var j0=ge('<svg class="svelte-flow__edge-wrapper"><g><!></g></svg>');function Oc(e,t){ue(t,!0);const n=y(t,"edge",7),r=y(t,"store",15),o=y(t,"onedgeclick",7),i=y(t,"onedgecontextmenu",7),s=y(t,"onedgepointerenter",7),a=y(t,"onedgepointerleave",7);let l=z(()=>n().source),u=z(()=>n().target),d=z(()=>n().sourceX),p=z(()=>n().sourceY),f=z(()=>n().targetX),g=z(()=>n().targetY),h=z(()=>n().sourcePosition),v=z(()=>n().targetPosition),w=z(()=>gt(n().animated,!1)),b=z(()=>gt(n().selected,!1)),k=z(()=>n().label),x=z(()=>n().labelStyle),S=z(()=>gt(n().data,()=>({}),!0)),E=z(()=>n().style),L=z(()=>n().interactionWidth),D=z(()=>gt(n().type,"default")),q=z(()=>n().sourceHandle),B=z(()=>n().targetHandle),U=z(()=>n().markerStart),O=z(()=>n().markerEnd),$=z(()=>n().selectable),C=z(()=>n().focusable),_=z(()=>gt(n().deletable,!0)),P=z(()=>n().hidden),N=z(()=>n().zIndex),T=z(()=>n().class),Z=z(()=>n().ariaLabel),X=null;const{id:M}=n();mr("svelteflow__edge_id",M);let Y=z(()=>c($)??r().elementsSelectable),ee=z(()=>c(C)??r().edgesFocusable),ne=z(()=>r().edgeTypes[c(D)]??Js),R=z(()=>c(U)?`url('#${Bs(c(U),r().flowId)}')`:void 0),W=z(()=>c(O)?`url('#${Bs(c(O),r().flowId)}')`:void 0);function F(te){const de=r().edgeLookup.get(M);de&&(c(Y)&&r().handleEdgeSelection(M),o()?.({event:te,edge:de}))}function ie(te,de){const fe=r().edgeLookup.get(M);fe&&de({event:te,edge:fe})}function G(te){if(!r().disableKeyboardA11y&&Nu.includes(te.key)&&c(Y)){const{unselectNodesAndEdges:de,addSelectedEdges:fe}=r();te.key==="Escape"?(X?.blur(),de({edges:[n()]})):fe([M])}}var me={get edge(){return n()},set edge(te){n(te),m()},get store(){return r()},set store(te){r(te),m()},get onedgeclick(){return o()},set onedgeclick(te){o(te),m()},get onedgecontextmenu(){return i()},set onedgecontextmenu(te){i(te),m()},get onedgepointerenter(){return s()},set onedgepointerenter(te){s(te),m()},get onedgepointerleave(){return a()},set onedgepointerleave(te){a(te),m()}},we=Ce(),re=oe(we);{var Q=te=>{var de=j0();let fe;var le=I(de);Ue(le,ke=>({class:["svelte-flow__edge",c(T)],"data-id":M,onclick:F,oncontextmenu:i()?K=>{ie(K,i())}:void 0,onpointerenter:s()?K=>{ie(K,s())}:void 0,onpointerleave:a()?K=>{ie(K,a())}:void 0,"aria-label":c(Z)===null?void 0:c(Z)?c(Z):`Edge from ${c(l)} to ${c(u)}`,"aria-describedby":c(ee)?`${Mc}-${r().flowId}`:void 0,role:n().ariaRole??(c(ee)?"group":"img"),"aria-roledescription":"edge",onkeydown:c(ee)?G:void 0,tabindex:c(ee)?0:void 0,...n().domAttributes,[Vn]:ke}),[()=>({animated:c(w),selected:c(b),selectable:c(Y)})]);var Ne=I(le);ds(Ne,()=>c(ne),(ke,K)=>{K(ke,{get id(){return M},get source(){return c(l)},get target(){return c(u)},get sourceX(){return c(d)},get sourceY(){return c(p)},get targetX(){return c(f)},get targetY(){return c(g)},get sourcePosition(){return c(h)},get targetPosition(){return c(v)},get animated(){return c(w)},get selected(){return c(b)},get label(){return c(k)},get labelStyle(){return c(x)},get data(){return c(S)},get style(){return c(E)},get interactionWidth(){return c(L)},get selectable(){return c(Y)},get deletable(){return c(_)},get type(){return c(D)},get sourceHandleId(){return c(q)},get targetHandleId(){return c(B)},get markerStart(){return c(R)},get markerEnd(){return c(W)}})}),A(le),Et(le,ke=>X=ke,()=>X),A(de),xe(ke=>fe=rt(de,"",fe,ke),[()=>({"z-index":c(N)})]),H(te,de)};ae(re,te=>{c(P)||te(Q)})}return H(e,we),ce(me)}se(Oc,{edge:{},store:{},onedgeclick:{},onedgecontextmenu:{},onedgepointerenter:{},onedgepointerleave:{}},[],[],!0),Mf();var W0=ge("<defs></defs>");function Ac(e,t){ue(t,!1);const n=jt();Ll();var r=W0();ft(r,5,()=>n.markers,o=>o.id,(o,i)=>{Ic(o,Ae(()=>c(i)))}),A(r),H(e,r),ce()}se(Ac,{},[],[],!0);var F0=ge('<polyline class="arrow" fill="none" stroke-linecap="round" stroke-linejoin="round" points="-5,-4 0,0 -5,4"></polyline>'),G0=ge('<polyline class="arrowclosed" stroke-linecap="round" stroke-linejoin="round" points="-5,-4 0,0 -5,4 -5,-4"></polyline>'),U0=ge('<marker class="svelte-flow__arrowhead" viewBox="-10 -10 20 20" refX="0" refY="0"><!></marker>');function Ic(e,t){ue(t,!0);let n=y(t,"id",7),r=y(t,"type",7),o=y(t,"width",7,12.5),i=y(t,"height",7,12.5),s=y(t,"markerUnits",7,"strokeWidth"),a=y(t,"orient",7,"auto-start-reverse"),l=y(t,"color",7,"none"),u=y(t,"strokeWidth",7);var d={get id(){return n()},set id(v){n(v),m()},get type(){return r()},set type(v){r(v),m()},get width(){return o()},set width(v=12.5){o(v),m()},get height(){return i()},set height(v=12.5){i(v),m()},get markerUnits(){return s()},set markerUnits(v="strokeWidth"){s(v),m()},get orient(){return a()},set orient(v="auto-start-reverse"){a(v),m()},get color(){return l()},set color(v="none"){l(v),m()},get strokeWidth(){return u()},set strokeWidth(v){u(v),m()}},p=U0(),f=I(p);{var g=v=>{var w=F0();let b;xe(k=>{ye(w,"stroke-width",u()),b=rt(w,"",b,k)},[()=>({stroke:l()})]),H(v,w)},h=v=>{var w=Ce(),b=oe(w);{var k=x=>{var S=G0();let E;xe(L=>{ye(S,"stroke-width",u()),E=rt(S,"",E,L)},[()=>({stroke:l(),fill:l()})]),H(x,S)};ae(b,x=>{r()===fo.ArrowClosed&&x(k)},!0)}H(v,w)};ae(f,v=>{r()===fo.Arrow?v(g):v(h,!1)})}return A(p),xe(()=>{ye(p,"id",n()),ye(p,"markerWidth",`${o()}`),ye(p,"markerHeight",`${i()}`),ye(p,"markerUnits",s()),ye(p,"orient",a())}),H(e,p),ce(d)}se(Ic,{id:{},type:{},width:{},height:{},markerUnits:{},orient:{},color:{},strokeWidth:{}},[],[],!0);var J0=J('<div class="svelte-flow__edges"><svg class="svelte-flow__marker"><!></svg> <!></div>');function qc(e,t){ue(t,!0);let n=y(t,"store",15),r=y(t,"onedgeclick",7),o=y(t,"onedgecontextmenu",7),i=y(t,"onedgepointerenter",7),s=y(t,"onedgepointerleave",7);var a={get store(){return n()},set store(f){n(f),m()},get onedgeclick(){return r()},set onedgeclick(f){r(f),m()},get onedgecontextmenu(){return o()},set onedgecontextmenu(f){o(f),m()},get onedgepointerenter(){return i()},set onedgepointerenter(f){i(f),m()},get onedgepointerleave(){return s()},set onedgepointerleave(f){s(f),m()}},l=J0(),u=I(l),d=I(u);Ac(d,{}),A(u);var p=V(u,2);return ft(p,17,()=>n().visible.edges.values(),f=>f.id,(f,g)=>{Oc(f,{get edge(){return c(g)},get onedgeclick(){return r()},get onedgecontextmenu(){return o()},get onedgepointerenter(){return i()},get onedgepointerleave(){return s()},get store(){return n()},set store(h){n(h)}})}),A(l),H(e,l),ce(a)}se(qc,{store:{},onedgeclick:{},onedgecontextmenu:{},onedgepointerenter:{},onedgepointerleave:{}},[],[],!0);var Q0=J('<div class="svelte-flow__selection svelte-1iugwpu"></div>');const em={hash:"svelte-1iugwpu",code:".svelte-flow__selection.svelte-1iugwpu {position:absolute;top:0;left:0;}"};function Qs(e,t){ue(t,!0),He(e,em);let n=y(t,"x",7,0),r=y(t,"y",7,0),o=y(t,"width",7,0),i=y(t,"height",7,0),s=y(t,"isVisible",7,!0);var a={get x(){return n()},set x(p=0){n(p),m()},get y(){return r()},set y(p=0){r(p),m()},get width(){return o()},set width(p=0){o(p),m()},get height(){return i()},set height(p=0){i(p),m()},get isVisible(){return s()},set isVisible(p=!0){s(p),m()}},l=Ce(),u=oe(l);{var d=p=>{var f=Q0();let g;xe(h=>g=rt(f,"",g,h),[()=>({width:typeof o()=="string"?o():cn(o()),height:typeof i()=="string"?i():cn(i()),transform:`translate(${n()}px, ${r()}px)`})]),H(p,f)};ae(u,p=>{s()&&p(d)})}return H(e,l),ce(a)}se(Qs,{x:{},y:{},width:{},height:{},isVisible:{}},[],[],!0);function tm(e,t,n){const r=t().nodes.filter(o=>o.selected);n()?.({nodes:r,event:e})}function nm(e,t,n){const r=t().nodes.filter(o=>o.selected);n()?.({nodes:r,event:e})}var rm=J("<div><!></div>");const om={hash:"svelte-16qgzgd",code:".svelte-flow__selection-wrapper.svelte-16qgzgd {position:absolute;top:0;left:0;z-index:2000;pointer-events:all;}"};function Zc(e,t){ue(t,!0),He(e,om);let n=y(t,"store",15),r=y(t,"onnodedrag",7),o=y(t,"onnodedragstart",7),i=y(t,"onnodedragstop",7),s=y(t,"onselectionclick",7),a=y(t,"onselectioncontextmenu",7),l=Se(void 0);Ke(()=>{n().disableKeyboardA11y||c(l)?.focus({preventScroll:!0})});let u=z(()=>n().selectionRectMode==="nodes"?(n().nodes,go(n().nodeLookup,{filter:v=>!!v.selected})):null);function d(v){Object.prototype.hasOwnProperty.call(_i,v.key)&&(v.preventDefault(),n().moveSelectedNodes(_i[v.key],v.shiftKey?4:1))}var p={get store(){return n()},set store(v){n(v),m()},get onnodedrag(){return r()},set onnodedrag(v){r(v),m()},get onnodedragstart(){return o()},set onnodedragstart(v){o(v),m()},get onnodedragstop(){return i()},set onnodedragstop(v){i(v),m()},get onselectionclick(){return s()},set onselectionclick(v){s(v),m()},get onselectioncontextmenu(){return a()},set onselectioncontextmenu(v){a(v),m()}},f=Ce(),g=oe(f);{var h=v=>{var w=rm();w.__contextmenu=[tm,n,a],w.__click=[nm,n,s],w.__keydown=function(...x){(n().disableKeyboardA11y?void 0:d)?.apply(this,x)};let b;var k=I(w);Qs(k,{width:"100%",height:"100%",x:0,y:0}),A(w),pt(w,(x,S)=>Lc?.(x,S),()=>({disabled:!1,store:n(),onDrag:(x,S,E,L)=>{r()?.({event:x,targetNode:null,nodes:L})},onDragStart:(x,S,E,L)=>{o()?.({event:x,targetNode:null,nodes:L})},onDragStop:(x,S,E,L)=>{i()?.({event:x,targetNode:null,nodes:L})}})),Et(w,x=>j(l,x),()=>c(l)),xe(x=>{bt(w,1,bn(["svelte-flow__selection-wrapper",n().noPanClass]),"svelte-16qgzgd"),ye(w,"role",n().disableKeyboardA11y?void 0:"button"),ye(w,"tabindex",n().disableKeyboardA11y?void 0:-1),b=rt(w,"",b,x)},[()=>({width:cn(c(u).width),height:cn(c(u).height),transform:`translate(${c(u).x??""}px, ${c(u).y??""}px)`})]),H(v,w)};ae(g,v=>{n().selectionRectMode==="nodes"&&c(u)&&Cn(c(u).x)&&Cn(c(u).y)&&v(h)})}return H(e,f),ce(p)}wn(["contextmenu","click","keydown"]),se(Zc,{store:{},onnodedrag:{},onnodedragstart:{},onnodedragstop:{},onselectionclick:{},onselectioncontextmenu:{}},[],[],!0);function im(e){switch(e){case"ctrl":return 8;case"shift":return 4;case"alt":return 2;case"meta":return 1}}function dn(e,t){let{enabled:n=!0,trigger:r,type:o="keydown"}=t;function i(a){const l=Array.isArray(r)?r:[r],u=[a.metaKey,a.altKey,a.shiftKey,a.ctrlKey].reduce((d,p,f)=>p?d|1<<f:d,0);for(const d of l){const p={preventDefault:!1,enabled:!0,...d},{modifier:f,key:g,callback:h,preventDefault:v,enabled:w}=p;if(w){if(a.key!==g)continue;if(f===null||f===!1){if(u!==0)continue}else if(f!==void 0&&f?.[0]?.length>0){const k=Array.isArray(f)?f:[f];let x=!1;for(const S of k)if((Array.isArray(S)?S:[S]).reduce((E,L)=>E|im(L),0)===u){x=!0;break}if(!x)continue}v&&a.preventDefault();const b={node:e,trigger:p,originalEvent:a};e.dispatchEvent(new CustomEvent("shortcut",{detail:b})),h?.(b)}}}let s;return n&&(s=ss(e,o,i)),{update:a=>{const{enabled:l=!0,type:u="keydown"}=a;n&&(!l||o!==u)?s?.():!n&&l&&(s=ss(e,u,i)),n=l,o=u,r=a.trigger},destroy:()=>{s?.()}}}function ot(){const e=z(jt),t=i=>{const s=vc(i)?i:c(e).nodeLookup.get(i.id),a=s.parentId?D1(s.position,s.measured,s.parentId,c(e).nodeLookup,c(e).nodeOrigin):s.position,l={...s,position:a,width:s.measured?.width??s.width,height:s.measured?.height??s.height};return zr(l)};function n(i,s,a={replace:!1}){c(e).nodes=nt(()=>c(e).nodes).map(l=>{if(l.id===i){const u=typeof s=="function"?s(l):s;return a?.replace&&vc(u)?u:{...l,...u}}return l})}function r(i,s,a={replace:!1}){c(e).edges=nt(()=>c(e).edges).map(l=>{if(l.id===i){const u=typeof s=="function"?s(l):s;return a.replace&&C0(u)?u:{...l,...u}}return l})}const o=i=>c(e).nodeLookup.get(i);return{zoomIn:c(e).zoomIn,zoomOut:c(e).zoomOut,getInternalNode:o,getNode:i=>o(i)?.internals.userNode,getNodes:i=>i===void 0?c(e).nodes:Rc(c(e).nodeLookup,i),getEdge:i=>c(e).edgeLookup.get(i),getEdges:i=>i===void 0?c(e).edges:Rc(c(e).edgeLookup,i),setZoom:(i,s)=>{const a=c(e).panZoom;return a?a.scaleTo(i,{duration:s?.duration}):Promise.resolve(!1)},getZoom:()=>c(e).viewport.zoom,setViewport:async(i,s)=>{const a=c(e).viewport;return c(e).panZoom?(await c(e).panZoom.setViewport({x:i.x??a.x,y:i.y??a.y,zoom:i.zoom??a.zoom},s),Promise.resolve(!0)):Promise.resolve(!1)},getViewport:()=>Na(c(e).viewport),setCenter:async(i,s,a)=>c(e).setCenter(i,s,a),fitView:i=>c(e).fitView(i),fitBounds:async(i,s)=>{if(!c(e).panZoom)return Promise.resolve(!1);const a=qs(i,c(e).width,c(e).height,c(e).minZoom,c(e).maxZoom,s?.padding??.1);return await c(e).panZoom.setViewport(a,{duration:s?.duration,ease:s?.ease,interpolate:s?.interpolate}),Promise.resolve(!0)},getIntersectingNodes:(i,s=!0,a)=>{const l=Ou(i),u=l?i:t(i);return u?(a||c(e).nodes).filter(d=>{const p=c(e).nodeLookup.get(d.id);if(!p||!l&&d.id===i.id)return!1;const f=zr(p),g=ho(f,u);return s&&g>0||g>=f.width*f.height||g>=u.width*u.height}):[]},isNodeIntersecting:(i,s,a=!0)=>{const l=Ou(i)?i:t(i);if(!l)return!1;const u=ho(l,s);return a&&u>0||u>=l.width*l.height},deleteElements:async({nodes:i=[],edges:s=[]})=>{const{nodes:a,edges:l}=await N1({nodesToRemove:i,edgesToRemove:s,nodes:c(e).nodes,edges:c(e).edges,onBeforeDelete:c(e).onbeforedelete});return a&&(c(e).nodes=nt(()=>c(e).nodes).filter(u=>!a.some(({id:d})=>d===u.id))),l&&(c(e).edges=nt(()=>c(e).edges).filter(u=>!l.some(({id:d})=>d===u.id))),(a.length>0||l.length>0)&&c(e).ondelete?.({nodes:a,edges:l}),{deletedNodes:a,deletedEdges:l}},screenToFlowPosition:(i,s={snapToGrid:!0})=>{if(!c(e).domNode)return i;const a=s.snapToGrid?c(e).snapGrid:!1,{x:l,y:u,zoom:d}=c(e).viewport,{x:p,y:f}=c(e).domNode.getBoundingClientRect(),g={x:i.x-p,y:i.y-f};return mo(g,[l,u,d],a!==null,a||[1,1])},flowToScreenPosition:i=>{if(!c(e).domNode)return i;const{x:s,y:a,zoom:l}=c(e).viewport,{x:u,y:d}=c(e).domNode.getBoundingClientRect(),p=wi(i,[s,a,l]);return{x:p.x+u,y:p.y+d}},toObject:()=>structuredClone({nodes:[...c(e).nodes],edges:[...c(e).edges],viewport:{...c(e).viewport}}),updateNode:n,updateNodeData:(i,s,a)=>{const l=c(e).nodeLookup.get(i)?.internals.userNode;if(!l)return;const u=typeof s=="function"?s(l):s;n(i,d=>({...d,data:a?.replace?u:{...d.data,...u}}))},updateEdge:r,getNodesBounds:i=>$1(i,{nodeLookup:c(e).nodeLookup,nodeOrigin:c(e).nodeOrigin}),getHandleConnections:({type:i,id:s,nodeId:a})=>Array.from(c(e).connectionLookup.get(`${a}-${i}-${s??null}`)?.values()??[])}}function Rc(e,t){const n=[];for(const r of t){const o=e.get(r);if(o){const i="internals"in o?o.internals?.userNode:o;n.push(i)}}return n}function Bc(e,t){ue(t,!0);let n=y(t,"store",15),r=y(t,"selectionKey",7,"Shift"),o=y(t,"multiSelectionKey",23,()=>sr()?"Meta":"Control"),i=y(t,"deleteKey",7,"Backspace"),s=y(t,"panActivationKey",7," "),a=y(t,"zoomActivationKey",23,()=>sr()?"Meta":"Control"),{deleteElements:l}=ot();function u(w){return w!==null&&typeof w=="object"}function d(w){return u(w)?w.modifier||[]:[]}function p(w){return w==null?"":u(w)?w.key:w}function f(w,b){return(Array.isArray(w)?w:[w]).map(k=>{const x=p(k);return{key:x,modifier:d(k),enabled:x!==null,callback:b}})}function g(){n(n().selectionRect=null,!0),n(n().selectionKeyPressed=!1,!0),n(n().multiselectionKeyPressed=!1,!0),n(n().deleteKeyPressed=!1,!0),n(n().panActivationKeyPressed=!1,!0),n(n().zoomActivationKeyPressed=!1,!0)}function h(){const w=n().nodes.filter(k=>k.selected),b=n().edges.filter(k=>k.selected);l({nodes:w,edges:b})}var v={get store(){return n()},set store(w){n(w),m()},get selectionKey(){return r()},set selectionKey(w="Shift"){r(w),m()},get multiSelectionKey(){return o()},set multiSelectionKey(w=sr()?"Meta":"Control"){o(w),m()},get deleteKey(){return i()},set deleteKey(w="Backspace"){i(w),m()},get panActivationKey(){return s()},set panActivationKey(w=" "){s(w),m()},get zoomActivationKey(){return a()},set zoomActivationKey(w=sr()?"Meta":"Control"){a(w),m()}};return vl("blur",mt,g),vl("contextmenu",mt,g),pt(mt,(w,b)=>dn?.(w,b),()=>({trigger:f(r(),()=>n(n().selectionKeyPressed=!0,!0)),type:"keydown"})),pt(mt,(w,b)=>dn?.(w,b),()=>({trigger:f(r(),()=>n(n().selectionKeyPressed=!1,!0)),type:"keyup"})),pt(mt,(w,b)=>dn?.(w,b),()=>({trigger:f(o(),()=>{n(n().multiselectionKeyPressed=!0,!0)}),type:"keydown"})),pt(mt,(w,b)=>dn?.(w,b),()=>({trigger:f(o(),()=>n(n().multiselectionKeyPressed=!1,!0)),type:"keyup"})),pt(mt,(w,b)=>dn?.(w,b),()=>({trigger:f(i(),w=>{!(w.originalEvent.ctrlKey||w.originalEvent.metaKey||w.originalEvent.shiftKey)&&!Zu(w.originalEvent)&&(n(n().deleteKeyPressed=!0,!0),h())}),type:"keydown"})),pt(mt,(w,b)=>dn?.(w,b),()=>({trigger:f(i(),()=>n(n().deleteKeyPressed=!1,!0)),type:"keyup"})),pt(mt,(w,b)=>dn?.(w,b),()=>({trigger:f(s(),()=>n(n().panActivationKeyPressed=!0,!0)),type:"keydown"})),pt(mt,(w,b)=>dn?.(w,b),()=>({trigger:f(s(),()=>n(n().panActivationKeyPressed=!1,!0)),type:"keyup"})),pt(mt,(w,b)=>dn?.(w,b),()=>({trigger:f(a(),()=>n(n().zoomActivationKeyPressed=!0,!0)),type:"keydown"})),pt(mt,(w,b)=>dn?.(w,b),()=>({trigger:f(a(),()=>n(n().zoomActivationKeyPressed=!1,!0)),type:"keyup"})),ce(v)}se(Bc,{store:{},selectionKey:{},multiSelectionKey:{},deleteKey:{},panActivationKey:{},zoomActivationKey:{}},[],[],!0);var sm=ge('<path fill="none" class="svelte-flow__connection-path"></path>'),am=ge('<svg class="svelte-flow__connectionline"><g><!></g></svg>');function Kc(e,t){ue(t,!0);let n=y(t,"store",15),r=y(t,"type",7),o=y(t,"containerStyle",7),i=y(t,"style",7),s=y(t,"LineComponent",7),a=z(()=>{if(!n().connection.inProgress)return"";const f={sourceX:n().connection.from.x,sourceY:n().connection.from.y,sourcePosition:n().connection.fromPosition,targetX:n().connection.to.x,targetY:n().connection.to.y,targetPosition:n().connection.toPosition};switch(r()){case $n.Bezier:{const[g]=Xu(f);return g}case $n.Straight:{const[g]=ju(f);return g}case $n.Step:case $n.SmoothStep:{const[g]=Rs({...f,borderRadius:r()===$n.Step?0:void 0});return g}}});var l={get store(){return n()},set store(f){n(f),m()},get type(){return r()},set type(f){r(f),m()},get containerStyle(){return o()},set containerStyle(f){o(f),m()},get style(){return i()},set style(f){i(f),m()},get LineComponent(){return s()},set LineComponent(f){s(f),m()}},u=Ce(),d=oe(u);{var p=f=>{var g=am(),h=I(g),v=I(h);{var w=k=>{var x=Ce(),S=oe(x);ds(S,s,(E,L)=>{L(E,{})}),H(k,x)},b=k=>{var x=sm();xe(()=>{ye(x,"d",c(a)),rt(x,i())}),H(k,x)};ae(v,k=>{s()?k(w):k(b,!1)})}A(h),A(g),xe(k=>{ye(g,"width",n().width),ye(g,"height",n().height),rt(g,o()),bt(h,0,k)},[()=>bn(["svelte-flow__connection",_1(n().connection.isValid)])]),H(f,g)};ae(d,f=>{n().connection.inProgress&&f(p)})}return H(e,u),ce(l)}se(Kc,{store:{},type:{},containerStyle:{},style:{},LineComponent:{}},[],[],!0);var lm=J("<div><!></div>");function bo(e,t){ue(t,!0);let n=y(t,"position",7,"top-right"),r=y(t,"style",7),o=y(t,"class",7),i=y(t,"children",7),s=Te(t,["$$slots","$$events","$$legacy","$$host","position","style","class","children"]),a=z(()=>`${n()}`.split("-"));var l={get position(){return n()},set position(p="top-right"){n(p),m()},get style(){return r()},set style(p){r(p),m()},get class(){return o()},set class(p){o(p),m()},get children(){return i()},set children(p){i(p),m()}},u=lm();Ue(u,p=>({class:p,style:r(),...s}),[()=>["svelte-flow__panel",o(),...c(a)]]);var d=I(u);return Xe(d,()=>i()??tt),A(u),H(e,u),ce(l)}se(bo,{position:{},style:{},class:{},children:{}},[],[],!0);var um=J('<a href="https://svelteflow.dev" target="_blank" rel="noopener noreferrer" aria-label="Svelte Flow attribution">Svelte Flow</a>');function Xc(e,t){ue(t,!0);let n=y(t,"proOptions",7),r=y(t,"position",7,"bottom-right");var o={get proOptions(){return n()},set proOptions(l){n(l),m()},get position(){return r()},set position(l="bottom-right"){r(l),m()}},i=Ce(),s=oe(i);{var a=l=>{bo(l,{get position(){return r()},class:"svelte-flow__attribution","data-message":"Feel free to remove the attribution or check out how you could support us: https://svelteflow.dev/support-us",children:(u,d)=>{var p=um();H(u,p)},$$slots:{default:!0}})};ae(s,l=>{n()?.hideAttribution||l(a)})}return H(e,i),ce(o)}se(Xc,{proOptions:{},position:{}},[],[],!0);var cm=J("<div><!></div>");const dm={hash:"svelte-12wlba6",code:".svelte-flow.svelte-12wlba6 {width:100%;height:100%;overflow:hidden;position:relative;z-index:0;background-color:var(--background-color, var(--background-color-default));}:root {--background-color-default: #fff;--background-pattern-color-default: #ddd;--minimap-mask-color-default: rgb(240, 240, 240, 0.6);--minimap-mask-stroke-color-default: none;--minimap-mask-stroke-width-default: 1;--controls-button-background-color-default: #fefefe;--controls-button-background-color-hover-default: #f4f4f4;--controls-button-color-default: inherit;--controls-button-color-hover-default: inherit;--controls-button-border-color-default: #eee;}"};function Yc(e,t){ue(t,!0),He(e,dm);let n=y(t,"width",7),r=y(t,"height",7),o=y(t,"colorMode",7),i=y(t,"domNode",15),s=y(t,"clientWidth",15),a=y(t,"clientHeight",15),l=y(t,"children",7),u=y(t,"rest",7),d=z(()=>u().class),p=z(()=>sp(u(),["id","class","nodeTypes","edgeTypes","colorMode","isValidConnection","onmove","onmovestart","onmoveend","onflowerror","ondelete","onbeforedelete","onbeforeconnect","onconnect","onconnectstart","onconnectend","onbeforereconnect","onreconnect","onreconnectstart","onreconnectend","onclickconnectstart","onclickconnectend","oninit","onselectionchange","onselectiondragstart","onselectiondrag","onselectiondragstop","onselectionstart","onselectionend","clickConnect","fitView","fitViewOptions","nodeOrigin","nodeDragThreshold","connectionDragThreshold","minZoom","maxZoom","initialViewport","connectionRadius","connectionMode","selectionMode","selectNodesOnDrag","snapGrid","defaultMarkerColor","translateExtent","nodeExtent","onlyRenderVisibleElements","autoPanOnConnect","autoPanOnNodeDrag","colorModeSSR","style","defaultEdgeOptions","elevateNodesOnSelect","elevateEdgesOnSelect","nodesDraggable","autoPanOnNodeFocus","nodesConnectable","elementsSelectable","nodesFocusable","edgesFocusable","disableKeyboardA11y","noDragClass","noPanClass","noWheelClass","ariaLabelConfig"]));function f(w){w.currentTarget.scrollTo({top:0,left:0,behavior:"auto"}),u().onscroll&&u().onscroll(w)}var g={get width(){return n()},set width(w){n(w),m()},get height(){return r()},set height(w){r(w),m()},get colorMode(){return o()},set colorMode(w){o(w),m()},get domNode(){return i()},set domNode(w){i(w),m()},get clientWidth(){return s()},set clientWidth(w){s(w),m()},get clientHeight(){return a()},set clientHeight(w){a(w),m()},get children(){return l()},set children(w){l(w),m()},get rest(){return u()},set rest(w){u(w),m()}},h=cm();Ue(h,w=>({class:["svelte-flow","svelte-flow__container",c(d),o()],"data-testid":"svelte-flow__wrapper",role:"application",onscroll:f,...c(p),[nn]:w}),[()=>({width:cn(n()),height:cn(r())})],void 0,"svelte-12wlba6");var v=I(h);return Xe(v,()=>l()??tt),A(h),Et(h,w=>i(w),()=>i()),Pl(h,"clientHeight",a),Pl(h,"clientWidth",s),H(e,h),ce(g)}se(Yc,{width:{},height:{},colorMode:{},domNode:{},clientWidth:{},clientHeight:{},children:{},rest:{}},[],[],!0);var fm=J('<div class="svelte-flow__viewport-back svelte-flow__container"></div> <!> <div class="svelte-flow__edge-labels svelte-flow__container"></div> <!> <!> <!> <div class="svelte-flow__viewport-front svelte-flow__container"></div>',1),pm=J("<!> <!>",1),gm=J("<!> <!> <!> <!> <!>",1);function jc(e,t){ue(t,!0);let n=y(t,"width",7),r=y(t,"height",7),o=y(t,"proOptions",7),i=y(t,"selectionKey",7),s=y(t,"deleteKey",7),a=y(t,"panActivationKey",7),l=y(t,"multiSelectionKey",7),u=y(t,"zoomActivationKey",7),d=y(t,"paneClickDistance",7,1),p=y(t,"nodeClickDistance",7,1),f=y(t,"onmovestart",7),g=y(t,"onmoveend",7),h=y(t,"onmove",7),v=y(t,"oninit",7),w=y(t,"onnodeclick",7),b=y(t,"onnodecontextmenu",7),k=y(t,"onnodedrag",7),x=y(t,"onnodedragstart",7),S=y(t,"onnodedragstop",7),E=y(t,"onnodepointerenter",7),L=y(t,"onnodepointermove",7),D=y(t,"onnodepointerleave",7),q=y(t,"onselectionclick",7),B=y(t,"onselectioncontextmenu",7),U=y(t,"onselectionstart",7),O=y(t,"onselectionend",7),$=y(t,"onedgeclick",7),C=y(t,"onedgecontextmenu",7),_=y(t,"onedgepointerenter",7),P=y(t,"onedgepointerleave",7),N=y(t,"onpaneclick",7),T=y(t,"onpanecontextmenu",7),Z=y(t,"panOnScrollMode",23,()=>ln.Free),X=y(t,"preventScrolling",7,!0),M=y(t,"zoomOnScroll",7,!0),Y=y(t,"zoomOnDoubleClick",7,!0),ee=y(t,"zoomOnPinch",7,!0),ne=y(t,"panOnScroll",7,!1),R=y(t,"panOnDrag",7,!0),W=y(t,"selectionOnDrag",7,!0),F=y(t,"connectionLineComponent",7),ie=y(t,"connectionLineStyle",7),G=y(t,"connectionLineContainerStyle",7),me=y(t,"connectionLineType",23,()=>$n.Bezier),we=y(t,"attributionPosition",7),re=y(t,"children",7),Q=y(t,"nodes",31,()=>Mt([])),te=y(t,"edges",31,()=>Mt([])),de=y(t,"viewport",31,()=>{}),fe=Te(t,["$$slots","$$events","$$legacy","$$host","width","height","proOptions","selectionKey","deleteKey","panActivationKey","multiSelectionKey","zoomActivationKey","paneClickDistance","nodeClickDistance","onmovestart","onmoveend","onmove","oninit","onnodeclick","onnodecontextmenu","onnodedrag","onnodedragstart","onnodedragstop","onnodepointerenter","onnodepointermove","onnodepointerleave","onselectionclick","onselectioncontextmenu","onselectionstart","onselectionend","onedgeclick","onedgecontextmenu","onedgepointerenter","onedgepointerleave","onpaneclick","onpanecontextmenu","panOnScrollMode","preventScrolling","zoomOnScroll","zoomOnDoubleClick","zoomOnPinch","panOnScroll","panOnDrag","selectionOnDrag","connectionLineComponent","connectionLineStyle","connectionLineContainerStyle","connectionLineType","attributionPosition","children","nodes","edges","viewport"]),le=$c({props:fe,width:n(),height:r(),get nodes(){return Q()},set nodes(K){Q(K)},get edges(){return te()},set edges(K){te(K)},get viewport(){return de()},set viewport(K){de(K)}});const Ne=Dn(ki);Ne&&Ne.setStore&&Ne.setStore(le),mr(ki,{provider:!1,getStore(){return le}}),Ke(()=>{const K={nodes:le.selectedNodes,edges:le.selectedEdges};nt(()=>t.onselectionchange)?.(K);for(const st of le.selectionChangeHandlers.values())st(K)}),Ro(()=>{le.reset()});var ke={get width(){return n()},set width(K){n(K),m()},get height(){return r()},set height(K){r(K),m()},get proOptions(){return o()},set proOptions(K){o(K),m()},get selectionKey(){return i()},set selectionKey(K){i(K),m()},get deleteKey(){return s()},set deleteKey(K){s(K),m()},get panActivationKey(){return a()},set panActivationKey(K){a(K),m()},get multiSelectionKey(){return l()},set multiSelectionKey(K){l(K),m()},get zoomActivationKey(){return u()},set zoomActivationKey(K){u(K),m()},get paneClickDistance(){return d()},set paneClickDistance(K=1){d(K),m()},get nodeClickDistance(){return p()},set nodeClickDistance(K=1){p(K),m()},get onmovestart(){return f()},set onmovestart(K){f(K),m()},get onmoveend(){return g()},set onmoveend(K){g(K),m()},get onmove(){return h()},set onmove(K){h(K),m()},get oninit(){return v()},set oninit(K){v(K),m()},get onnodeclick(){return w()},set onnodeclick(K){w(K),m()},get onnodecontextmenu(){return b()},set onnodecontextmenu(K){b(K),m()},get onnodedrag(){return k()},set onnodedrag(K){k(K),m()},get onnodedragstart(){return x()},set onnodedragstart(K){x(K),m()},get onnodedragstop(){return S()},set onnodedragstop(K){S(K),m()},get onnodepointerenter(){return E()},set onnodepointerenter(K){E(K),m()},get onnodepointermove(){return L()},set onnodepointermove(K){L(K),m()},get onnodepointerleave(){return D()},set onnodepointerleave(K){D(K),m()},get onselectionclick(){return q()},set onselectionclick(K){q(K),m()},get onselectioncontextmenu(){return B()},set onselectioncontextmenu(K){B(K),m()},get onselectionstart(){return U()},set onselectionstart(K){U(K),m()},get onselectionend(){return O()},set onselectionend(K){O(K),m()},get onedgeclick(){return $()},set onedgeclick(K){$(K),m()},get onedgecontextmenu(){return C()},set onedgecontextmenu(K){C(K),m()},get onedgepointerenter(){return _()},set onedgepointerenter(K){_(K),m()},get onedgepointerleave(){return P()},set onedgepointerleave(K){P(K),m()},get onpaneclick(){return N()},set onpaneclick(K){N(K),m()},get onpanecontextmenu(){return T()},set onpanecontextmenu(K){T(K),m()},get panOnScrollMode(){return Z()},set panOnScrollMode(K=ln.Free){Z(K),m()},get preventScrolling(){return X()},set preventScrolling(K=!0){X(K),m()},get zoomOnScroll(){return M()},set zoomOnScroll(K=!0){M(K),m()},get zoomOnDoubleClick(){return Y()},set zoomOnDoubleClick(K=!0){Y(K),m()},get zoomOnPinch(){return ee()},set zoomOnPinch(K=!0){ee(K),m()},get panOnScroll(){return ne()},set panOnScroll(K=!1){ne(K),m()},get panOnDrag(){return R()},set panOnDrag(K=!0){R(K),m()},get selectionOnDrag(){return W()},set selectionOnDrag(K=!0){W(K),m()},get connectionLineComponent(){return F()},set connectionLineComponent(K){F(K),m()},get connectionLineStyle(){return ie()},set connectionLineStyle(K){ie(K),m()},get connectionLineContainerStyle(){return G()},set connectionLineContainerStyle(K){G(K),m()},get connectionLineType(){return me()},set connectionLineType(K=$n.Bezier){me(K),m()},get attributionPosition(){return we()},set attributionPosition(K){we(K),m()},get children(){return re()},set children(K){re(K),m()},get nodes(){return Q()},set nodes(K=[]){Q(K),m()},get edges(){return te()},set edges(K=[]){te(K),m()},get viewport(){return de()},set viewport(K=void 0){de(K),m()}};return Yc(e,{get colorMode(){return le.colorMode},get width(){return n()},get height(){return r()},get rest(){return fe},get domNode(){return le.domNode},set domNode(K){le.domNode=K},get clientWidth(){return le.width},set clientWidth(K){le.width=K},get clientHeight(){return le.height},set clientHeight(K){le.height=K},children:(K,st)=>{var De=gm(),qe=oe(De);Bc(qe,{get selectionKey(){return i()},get deleteKey(){return s()},get panActivationKey(){return a()},get multiSelectionKey(){return l()},get zoomActivationKey(){return u()},get store(){return le},set store(Ve){le=Ve}});var Me=V(qe,2);Cc(Me,{get panOnScrollMode(){return Z()},get preventScrolling(){return X()},get zoomOnScroll(){return M()},get zoomOnDoubleClick(){return Y()},get zoomOnPinch(){return ee()},get panOnScroll(){return ne()},get panOnDrag(){return R()},get paneClickDistance(){return d()},get onmovestart(){return f()},get onmove(){return h()},get onmoveend(){return g()},get oninit(){return v()},get store(){return le},set store(Ve){le=Ve},children:(Ve,Fe)=>{Pc(Ve,{get onpaneclick(){return N()},get onpanecontextmenu(){return T()},get onselectionstart(){return U()},get onselectionend(){return O()},get panOnDrag(){return R()},get selectionOnDrag(){return W()},get store(){return le},set store(pe){le=pe},children:(pe,Be)=>{var ut=pm(),et=oe(ut);zc(et,{get store(){return le},set store(je){le=je},children:(je,kt)=>{var gn=fm(),Bn=V(oe(gn),2);qc(Bn,{get onedgeclick(){return $()},get onedgecontextmenu(){return C()},get onedgepointerenter(){return _()},get onedgepointerleave(){return P()},get store(){return le},set store(cr){le=cr}});var Li=V(Bn,4);Kc(Li,{get type(){return me()},get LineComponent(){return F()},get containerStyle(){return G()},get style(){return ie()},get store(){return le},set store(cr){le=cr}});var ef=V(Li,2);Vc(ef,{get nodeClickDistance(){return p()},get onnodeclick(){return w()},get onnodecontextmenu(){return b()},get onnodepointerenter(){return E()},get onnodepointermove(){return L()},get onnodepointerleave(){return D()},get onnodedrag(){return k()},get onnodedragstart(){return x()},get onnodedragstop(){return S()},get store(){return le},set store(cr){le=cr}});var Nb=V(ef,2);Zc(Nb,{get onselectionclick(){return q()},get onselectioncontextmenu(){return B()},get onnodedrag(){return k()},get onnodedragstart(){return x()},get onnodedragstop(){return S()},get store(){return le},set store(cr){le=cr}}),he(2),H(je,gn)},$$slots:{default:!0}});var zt=V(et,2);{let je=z(()=>!!(le.selectionRect&&le.selectionRectMode==="user")),kt=z(()=>le.selectionRect?.width),gn=z(()=>le.selectionRect?.height),Bn=z(()=>le.selectionRect?.x),Li=z(()=>le.selectionRect?.y);Qs(zt,{get isVisible(){return c(je)},get width(){return c(kt)},get height(){return c(gn)},get x(){return c(Bn)},get y(){return c(Li)}})}H(pe,ut)},$$slots:{default:!0}})},$$slots:{default:!0}});var at=V(Me,2);Xc(at,{get proOptions(){return o()},get position(){return we()}});var lt=V(at,2);Dc(lt,{get store(){return le}});var At=V(lt,2);Xe(At,()=>re()??tt),H(K,De)},$$slots:{default:!0}}),ce(ke)}se(jc,{width:{},height:{},proOptions:{},selectionKey:{},deleteKey:{},panActivationKey:{},multiSelectionKey:{},zoomActivationKey:{},paneClickDistance:{},nodeClickDistance:{},onmovestart:{},onmoveend:{},onmove:{},oninit:{},onnodeclick:{},onnodecontextmenu:{},onnodedrag:{},onnodedragstart:{},onnodedragstop:{},onnodepointerenter:{},onnodepointermove:{},onnodepointerleave:{},onselectionclick:{},onselectioncontextmenu:{},onselectionstart:{},onselectionend:{},onedgeclick:{},onedgecontextmenu:{},onedgepointerenter:{},onedgepointerleave:{},onpaneclick:{},onpanecontextmenu:{},panOnScrollMode:{},preventScrolling:{},zoomOnScroll:{},zoomOnDoubleClick:{},zoomOnPinch:{},panOnScroll:{},panOnDrag:{},selectionOnDrag:{},connectionLineComponent:{},connectionLineStyle:{},connectionLineContainerStyle:{},connectionLineType:{},attributionPosition:{},children:{},nodes:{},edges:{},viewport:{}},[],[],!0);function Wc(e,t){ue(t,!0);let n=y(t,"children",7),r=Se($c({props:{},nodes:[],edges:[]}));mr(ki,{provider:!0,getStore(){return c(r)},setStore:a=>{j(r,a)}}),Ro(()=>{c(r).reset()});var o={get children(){return n()},set children(a){n(a),m()}},i=Ce(),s=oe(i);return Xe(s,()=>n()??tt),H(e,i),ce(o)}se(Wc,{children:{}},[],[],!0);var hm=J("<button><!></button>");function xo(e,t){ue(t,!0);let n=y(t,"class",7),r=y(t,"bgColor",7),o=y(t,"bgColorHover",7),i=y(t,"color",7),s=y(t,"colorHover",7),a=y(t,"borderColor",7),l=y(t,"onclick",7),u=y(t,"children",7),d=Te(t,["$$slots","$$events","$$legacy","$$host","class","bgColor","bgColorHover","color","colorHover","borderColor","onclick","children"]);var p={get class(){return n()},set class(h){n(h),m()},get bgColor(){return r()},set bgColor(h){r(h),m()},get bgColorHover(){return o()},set bgColorHover(h){o(h),m()},get color(){return i()},set color(h){i(h),m()},get colorHover(){return s()},set colorHover(h){s(h),m()},get borderColor(){return a()},set borderColor(h){a(h),m()},get onclick(){return l()},set onclick(h){l(h),m()},get children(){return u()},set children(h){u(h),m()}},f=hm();Ue(f,h=>({type:"button",onclick:l(),class:["svelte-flow__controls-button",n()],...d,[nn]:h}),[()=>({"--xy-controls-button-background-color-props":r(),"--xy-controls-button-background-color-hover-props":o(),"--xy-controls-button-color-props":i(),"--xy-controls-button-color-hover-props":s(),"--xy-controls-button-border-color-props":a()})]);var g=I(f);return Xe(g,()=>u()??tt),A(f),H(e,f),ce(p)}se(xo,{class:{},bgColor:{},bgColorHover:{},color:{},colorHover:{},borderColor:{},onclick:{},children:{}},[],[],!0);var vm=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32"><path d="M32 18.133H18.133V32h-4.266V18.133H0v-4.266h13.867V0h4.266v13.867H32z"></path></svg>');function Fc(e){var t=vm();H(e,t)}se(Fc,{},[],[],!0);var mm=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 5"><path d="M0 0h32v4.2H0z"></path></svg>');function Gc(e){var t=mm();H(e,t)}se(Gc,{},[],[],!0);var ym=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 30"><path d="M3.692 4.63c0-.53.4-.938.939-.938h5.215V0H4.708C2.13 0 0 2.054 0 4.63v5.216h3.692V4.631zM27.354 0h-5.2v3.692h5.17c.53 0 .984.4.984.939v5.215H32V4.631A4.624 4.624 0 0027.354 0zm.954 24.83c0 .532-.4.94-.939.94h-5.215v3.768h5.215c2.577 0 4.631-2.13 4.631-4.707v-5.139h-3.692v5.139zm-23.677.94c-.531 0-.939-.4-.939-.94v-5.138H0v5.139c0 2.577 2.13 4.707 4.708 4.707h5.138V25.77H4.631z"></path></svg>');function Uc(e){var t=ym();H(e,t)}se(Uc,{},[],[],!0);var wm=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 25 32"><path d="M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0 8 0 4.571 3.429 4.571 7.619v3.048H3.048A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047zm4.724-13.866H7.467V7.619c0-2.59 2.133-4.724 4.723-4.724 2.591 0 4.724 2.133 4.724 4.724v3.048z"></path></svg>');function Jc(e){var t=wm();H(e,t)}se(Jc,{},[],[],!0);var bm=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 25 32"><path d="M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0c-4.114 1.828-1.37 2.133.305 2.438 1.676.305 4.42 2.59 4.42 5.181v3.048H3.047A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047z"></path></svg>');function Qc(e){var t=bm();H(e,t)}se(Qc,{},[],[],!0);var xm=J("<!> <!>",1),_m=J("<!> <!> <!> <!> <!> <!>",1);function ed(e,t){ue(t,!0);let n=y(t,"position",7,"bottom-left"),r=y(t,"orientation",7,"vertical"),o=y(t,"showZoom",7,!0),i=y(t,"showFitView",7,!0),s=y(t,"showLock",7,!0),a=y(t,"style",7),l=y(t,"class",7),u=y(t,"buttonBgColor",7),d=y(t,"buttonBgColorHover",7),p=y(t,"buttonColor",7),f=y(t,"buttonColorHover",7),g=y(t,"buttonBorderColor",7),h=y(t,"fitViewOptions",7),v=y(t,"children",7),w=y(t,"before",7),b=y(t,"after",7),k=Te(t,["$$slots","$$events","$$legacy","$$host","position","orientation","showZoom","showFitView","showLock","style","class","buttonBgColor","buttonBgColorHover","buttonColor","buttonColorHover","buttonBorderColor","fitViewOptions","children","before","after"]),x=z(jt);const S={bgColor:u(),bgColorHover:d(),color:p(),colorHover:f(),borderColor:g()};let E=z(()=>c(x).nodesDraggable||c(x).nodesConnectable||c(x).elementsSelectable),L=z(()=>c(x).viewport.zoom<=c(x).minZoom),D=z(()=>c(x).viewport.zoom>=c(x).maxZoom),q=z(()=>c(x).ariaLabelConfig),B=z(()=>r()==="horizontal"?"horizontal":"vertical");const U=()=>{c(x).zoomIn()},O=()=>{c(x).zoomOut()},$=()=>{c(x).fitView(h())},C=()=>{let P=!c(E);c(x).nodesDraggable=P,c(x).nodesConnectable=P,c(x).elementsSelectable=P};var _={get position(){return n()},set position(P="bottom-left"){n(P),m()},get orientation(){return r()},set orientation(P="vertical"){r(P),m()},get showZoom(){return o()},set showZoom(P=!0){o(P),m()},get showFitView(){return i()},set showFitView(P=!0){i(P),m()},get showLock(){return s()},set showLock(P=!0){s(P),m()},get style(){return a()},set style(P){a(P),m()},get class(){return l()},set class(P){l(P),m()},get buttonBgColor(){return u()},set buttonBgColor(P){u(P),m()},get buttonBgColorHover(){return d()},set buttonBgColorHover(P){d(P),m()},get buttonColor(){return p()},set buttonColor(P){p(P),m()},get buttonColorHover(){return f()},set buttonColorHover(P){f(P),m()},get buttonBorderColor(){return g()},set buttonBorderColor(P){g(P),m()},get fitViewOptions(){return h()},set fitViewOptions(P){h(P),m()},get children(){return v()},set children(P){v(P),m()},get before(){return w()},set before(P){w(P),m()},get after(){return b()},set after(P){b(P),m()}};{let P=z(()=>["svelte-flow__controls",c(B),l()]);bo(e,Ae({get class(){return c(P)},get position(){return n()},"data-testid":"svelte-flow__controls",get"aria-label"(){return c(q)["controls.ariaLabel"]},get style(){return a()}},()=>k,{children:(N,T)=>{var Z=_m(),X=oe(Z);{var M=re=>{var Q=Ce(),te=oe(Q);Xe(te,w),H(re,Q)};ae(X,re=>{w()&&re(M)})}var Y=V(X,2);{var ee=re=>{var Q=xm(),te=oe(Q);xo(te,Ae({onclick:U,class:"svelte-flow__controls-zoomin",get title(){return c(q)["controls.zoomIn.ariaLabel"]},get"aria-label"(){return c(q)["controls.zoomIn.ariaLabel"]},get disabled(){return c(D)}},()=>S,{children:(fe,le)=>{Fc(fe)},$$slots:{default:!0}}));var de=V(te,2);xo(de,Ae({onclick:O,class:"svelte-flow__controls-zoomout",get title(){return c(q)["controls.zoomOut.ariaLabel"]},get"aria-label"(){return c(q)["controls.zoomOut.ariaLabel"]},get disabled(){return c(L)}},()=>S,{children:(fe,le)=>{Gc(fe)},$$slots:{default:!0}})),H(re,Q)};ae(Y,re=>{o()&&re(ee)})}var ne=V(Y,2);{var R=re=>{xo(re,Ae({class:"svelte-flow__controls-fitview",onclick:$,get title(){return c(q)["controls.fitView.ariaLabel"]},get"aria-label"(){return c(q)["controls.fitView.ariaLabel"]}},()=>S,{children:(Q,te)=>{Uc(Q)},$$slots:{default:!0}}))};ae(ne,re=>{i()&&re(R)})}var W=V(ne,2);{var F=re=>{xo(re,Ae({class:"svelte-flow__controls-interactive",onclick:C,get title(){return c(q)["controls.interactive.ariaLabel"]},get"aria-label"(){return c(q)["controls.interactive.ariaLabel"]}},()=>S,{children:(Q,te)=>{var de=Ce(),fe=oe(de);{var le=ke=>{Qc(ke)},Ne=ke=>{Jc(ke)};ae(fe,ke=>{c(E)?ke(le):ke(Ne,!1)})}H(Q,de)},$$slots:{default:!0}}))};ae(W,re=>{s()&&re(F)})}var ie=V(W,2);{var G=re=>{var Q=Ce(),te=oe(Q);Xe(te,v),H(re,Q)};ae(ie,re=>{v()&&re(G)})}var me=V(ie,2);{var we=re=>{var Q=Ce(),te=oe(Q);Xe(te,b),H(re,Q)};ae(me,re=>{b()&&re(we)})}H(N,Z)},$$slots:{default:!0}}))}return ce(_)}se(ed,{position:{},orientation:{},showZoom:{},showFitView:{},showLock:{},style:{},class:{},buttonBgColor:{},buttonBgColorHover:{},buttonColor:{},buttonColorHover:{},buttonBorderColor:{},fitViewOptions:{},children:{},before:{},after:{}},[],[],!0);var Sn;(function(e){e.Lines="lines",e.Dots="dots",e.Cross="cross"})(Sn||(Sn={}));var km=ge("<circle></circle>");function td(e,t){ue(t,!0);let n=y(t,"radius",7),r=y(t,"class",7);var o={get radius(){return n()},set radius(s){n(s),m()},get class(){return r()},set class(s){r(s),m()}},i=km();return xe(()=>{ye(i,"cx",n()),ye(i,"cy",n()),ye(i,"r",n()),bt(i,0,bn(["svelte-flow__background-pattern","dots",r()]))}),H(e,i),ce(o)}se(td,{radius:{},class:{}},[],[],!0);var $m=ge("<path></path>");function nd(e,t){ue(t,!0);let n=y(t,"lineWidth",7),r=y(t,"dimensions",7),o=y(t,"variant",7),i=y(t,"class",7);var s={get lineWidth(){return n()},set lineWidth(l){n(l),m()},get dimensions(){return r()},set dimensions(l){r(l),m()},get variant(){return o()},set variant(l){o(l),m()},get class(){return i()},set class(l){i(l),m()}},a=$m();return xe(()=>{ye(a,"stroke-width",n()),ye(a,"d",`M${r()[0]/2} 0 V${r()[1]} M0 ${r()[1]/2} H${r()[0]}`),bt(a,0,bn(["svelte-flow__background-pattern",o(),i()]))}),H(e,a),ce(s)}se(nd,{lineWidth:{},dimensions:{},variant:{},class:{}},[],[],!0);const Cm={[Sn.Dots]:1,[Sn.Lines]:1,[Sn.Cross]:6};var Sm=ge('<svg data-testid="svelte-flow__background"><pattern patternUnits="userSpaceOnUse"><!></pattern><rect x="0" y="0" width="100%" height="100%"></rect></svg>');function rd(e,t){ue(t,!0);let n=y(t,"id",7),r=y(t,"variant",23,()=>Sn.Dots),o=y(t,"gap",7,20),i=y(t,"size",7),s=y(t,"lineWidth",7,1),a=y(t,"bgColor",7),l=y(t,"patternColor",7),u=y(t,"patternClass",7),d=y(t,"class",7),p=z(jt),f=z(()=>r()===Sn.Dots),g=z(()=>r()===Sn.Cross),h=z(()=>Array.isArray(o())?o():[o(),o()]),v=z(()=>`background-pattern-${c(p).flowId}-${n()??""}`),w=z(()=>[c(h)[0]*c(p).viewport.zoom||1,c(h)[1]*c(p).viewport.zoom||1]),b=z(()=>(i()??Cm[r()])*c(p).viewport.zoom),k=z(()=>c(g)?[c(b),c(b)]:c(w)),x=z(()=>c(f)?[c(b)/2,c(b)/2]:[c(k)[0]/2,c(k)[1]/2]);var S={get id(){return n()},set id($){n($),m()},get variant(){return r()},set variant($=Sn.Dots){r($),m()},get gap(){return o()},set gap($=20){o($),m()},get size(){return i()},set size($){i($),m()},get lineWidth(){return s()},set lineWidth($=1){s($),m()},get bgColor(){return a()},set bgColor($){a($),m()},get patternColor(){return l()},set patternColor($){l($),m()},get patternClass(){return u()},set patternClass($){u($),m()},get class(){return d()},set class($){d($),m()}},E=Sm();let L;var D=I(E),q=I(D);{var B=$=>{{let C=z(()=>c(b)/2);td($,{get radius(){return c(C)},get class(){return u()}})}},U=$=>{nd($,{get dimensions(){return c(k)},get variant(){return r()},get lineWidth(){return s()},get class(){return u()}})};ae(q,$=>{c(f)?$(B):$(U,!1)})}A(D);var O=V(D);return A(E),xe($=>{bt(E,0,bn(["svelte-flow__background","svelte-flow__container",d()])),L=rt(E,"",L,$),ye(D,"id",c(v)),ye(D,"x",c(p).viewport.x%c(w)[0]),ye(D,"y",c(p).viewport.y%c(w)[1]),ye(D,"width",c(w)[0]),ye(D,"height",c(w)[1]),ye(D,"patternTransform",`translate(-${c(x)[0]},-${c(x)[1]})`),ye(O,"fill",`url(#${c(v)})`)},[()=>({"--xy-background-color-props":a(),"--xy-background-pattern-color-props":l()})]),H(e,E),ce(S)}se(rd,{id:{},variant:{},gap:{},size:{},lineWidth:{},bgColor:{},patternColor:{},patternClass:{},class:{}},[],[],!0);var Em=ge("<rect></rect>");function od(e,t){ue(t,!0);let n=y(t,"x",7),r=y(t,"y",7),o=y(t,"width",7),i=y(t,"height",7),s=y(t,"borderRadius",7,5),a=y(t,"color",7),l=y(t,"shapeRendering",7),u=y(t,"strokeColor",7),d=y(t,"strokeWidth",7,2),p=y(t,"selected",7),f=y(t,"class",7);var g={get x(){return n()},set x(b){n(b),m()},get y(){return r()},set y(b){r(b),m()},get width(){return o()},set width(b){o(b),m()},get height(){return i()},set height(b){i(b),m()},get borderRadius(){return s()},set borderRadius(b=5){s(b),m()},get color(){return a()},set color(b){a(b),m()},get shapeRendering(){return l()},set shapeRendering(b){l(b),m()},get strokeColor(){return u()},set strokeColor(b){u(b),m()},get strokeWidth(){return d()},set strokeWidth(b=2){d(b),m()},get selected(){return p()},set selected(b){p(b),m()},get class(){return f()},set class(b){f(b),m()}},h=Em();let v,w;return xe((b,k)=>{v=bt(h,0,bn(["svelte-flow__minimap-node",f()]),null,v,b),ye(h,"x",n()),ye(h,"y",r()),ye(h,"rx",s()),ye(h,"ry",s()),ye(h,"width",o()),ye(h,"height",i()),ye(h,"shape-rendering",l()),w=rt(h,"",w,k)},[()=>({selected:p()}),()=>({fill:a(),stroke:u(),"stroke-width":d()})]),H(e,h),ce(g)}se(od,{x:{},y:{},width:{},height:{},borderRadius:{},color:{},shapeRendering:{},strokeColor:{},strokeWidth:{},selected:{},class:{}},[],[],!0);function Nm(e,t){const n=d0({domNode:e,panZoom:t.panZoom,getTransform:()=>{const{viewport:o}=t.store;return[o.x,o.y,o.zoom]},getViewScale:t.getViewScale});n.update({translateExtent:t.translateExtent,width:t.width,height:t.height,inversePan:t.inversePan,zoomStep:t.zoomStep,pannable:t.pannable,zoomable:t.zoomable});function r(o){n.update({translateExtent:o.translateExtent,width:o.width,height:o.height,inversePan:o.inversePan,zoomStep:o.zoomStep,pannable:o.pannable,zoomable:o.zoomable})}return{update:r,destroy(){n.destroy()}}}const ea=e=>e instanceof Function?e:()=>e;var Pm=ge("<title> </title>"),zm=ge('<svg class="svelte-flow__minimap-svg" role="img"><!><!><path class="svelte-flow__minimap-mask" fill-rule="evenodd" pointer-events="none"></path></svg>'),Lm=J('<svelte-css-wrapper style="display: contents"><!></svelte-css-wrapper>',1);function id(e,t){ue(t,!0);let n=y(t,"position",7,"bottom-right"),r=y(t,"ariaLabel",7),o=y(t,"nodeStrokeColor",7,"transparent"),i=y(t,"nodeColor",7),s=y(t,"nodeClass",7,""),a=y(t,"nodeBorderRadius",7,5),l=y(t,"nodeStrokeWidth",7,2),u=y(t,"bgColor",7),d=y(t,"maskColor",7),p=y(t,"maskStrokeColor",7),f=y(t,"maskStrokeWidth",7),g=y(t,"width",7,200),h=y(t,"height",7,150),v=y(t,"pannable",7,!0),w=y(t,"zoomable",7,!0),b=y(t,"inversePan",7),k=y(t,"zoomStep",7),x=y(t,"class",7),S=Te(t,["$$slots","$$events","$$legacy","$$host","position","ariaLabel","nodeStrokeColor","nodeColor","nodeClass","nodeBorderRadius","nodeStrokeWidth","bgColor","maskColor","maskStrokeColor","maskStrokeWidth","width","height","pannable","zoomable","inversePan","zoomStep","class"]),E=z(jt),L=z(()=>c(E).ariaLabelConfig);const D=i()===void 0?void 0:ea(i()),q=ea(o()),B=ea(s()),U=typeof window>"u"||window.chrome?"crispEdges":"geometricPrecision";let O=z(()=>`svelte-flow__minimap-desc-${c(E).flowId}`),$=z(()=>({x:-c(E).viewport.x/c(E).viewport.zoom,y:-c(E).viewport.y/c(E).viewport.zoom,width:c(E).width/c(E).viewport.zoom,height:c(E).height/c(E).viewport.zoom})),C=z(()=>c(E).nodeLookup.size>0?Vu(go(c(E).nodeLookup,{filter:G=>!G.hidden}),c($)):c($)),_=z(()=>c(C).width/g()),P=z(()=>c(C).height/h()),N=z(()=>Math.max(c(_),c(P))),T=z(()=>c(N)*g()),Z=z(()=>c(N)*h()),X=z(()=>5*c(N)),M=z(()=>c(C).x-(c(T)-c(C).width)/2-c(X)),Y=z(()=>c(C).y-(c(Z)-c(C).height)/2-c(X)),ee=z(()=>c(T)+c(X)*2),ne=z(()=>c(Z)+c(X)*2);const R=()=>c(N);var W={get position(){return n()},set position(G="bottom-right"){n(G),m()},get ariaLabel(){return r()},set ariaLabel(G){r(G),m()},get nodeStrokeColor(){return o()},set nodeStrokeColor(G="transparent"){o(G),m()},get nodeColor(){return i()},set nodeColor(G){i(G),m()},get nodeClass(){return s()},set nodeClass(G=""){s(G),m()},get nodeBorderRadius(){return a()},set nodeBorderRadius(G=5){a(G),m()},get nodeStrokeWidth(){return l()},set nodeStrokeWidth(G=2){l(G),m()},get bgColor(){return u()},set bgColor(G){u(G),m()},get maskColor(){return d()},set maskColor(G){d(G),m()},get maskStrokeColor(){return p()},set maskStrokeColor(G){p(G),m()},get maskStrokeWidth(){return f()},set maskStrokeWidth(G){f(G),m()},get width(){return g()},set width(G=200){g(G),m()},get height(){return h()},set height(G=150){h(G),m()},get pannable(){return v()},set pannable(G=!0){v(G),m()},get zoomable(){return w()},set zoomable(G=!0){w(G),m()},get inversePan(){return b()},set inversePan(G){b(G),m()},get zoomStep(){return k()},set zoomStep(G){k(G),m()},get class(){return x()},set class(G){x(G),m()}},F=Lm(),ie=oe(F);{let G=z(()=>["svelte-flow__minimap",x()]);bp(ie,()=>({"--xy-minimap-background-color-props":u()})),bo(ie.lastChild,Ae({get position(){return n()},get class(){return c(G)},"data-testid":"svelte-flow__minimap"},()=>S,{children:(me,we)=>{var re=Ce(),Q=oe(re);{var te=de=>{var fe=zm();let le;var Ne=I(fe);{var ke=De=>{var qe=Pm(),Me=I(qe,!0);A(qe),xe(()=>{ye(qe,"id",c(O)),Oe(Me,r()??c(L)["minimap.ariaLabel"])}),H(De,qe)};ae(Ne,De=>{(r()??c(L)["minimap.ariaLabel"])&&De(ke)})}var K=V(Ne);ft(K,17,()=>c(E).nodes,De=>De.id,(De,qe)=>{const Me=z(()=>c(E).nodeLookup.get(c(qe).id));var at=Ce(),lt=oe(at);{var At=Ve=>{const Fe=z(()=>On(c(Me)));{let pe=z(()=>D?.(c(Me))),Be=z(()=>q(c(Me))),ut=z(()=>B(c(Me)));od(Ve,Ae({get x(){return c(Me).internals.positionAbsolute.x},get y(){return c(Me).internals.positionAbsolute.y}},()=>c(Fe),{get selected(){return c(Me).selected},get color(){return c(pe)},get borderRadius(){return a()},get strokeColor(){return c(Be)},get strokeWidth(){return l()},get shapeRendering(){return U},get class(){return c(ut)}}))}};ae(lt,Ve=>{c(Me)&&Au(c(Me))&&Ve(At)})}H(De,at)});var st=V(K);A(fe),pt(fe,(De,qe)=>Nm?.(De,qe),()=>({store:c(E),panZoom:c(E).panZoom,getViewScale:R,translateExtent:c(E).translateExtent,width:c(E).width,height:c(E).height,inversePan:b(),zoomStep:k(),pannable:v(),zoomable:w()})),xe(De=>{ye(fe,"width",g()),ye(fe,"height",h()),ye(fe,"viewBox",`${c(M)??""} ${c(Y)??""} ${c(ee)??""} ${c(ne)??""}`),ye(fe,"aria-labelledby",c(O)),le=rt(fe,"",le,De),ye(st,"d",`M${c(M)-c(X)},${c(Y)-c(X)}h${c(ee)+c(X)*2}v${c(ne)+c(X)*2}h${-c(ee)-c(X)*2}z
      M${c($).x??""},${c($).y??""}h${c($).width??""}v${c($).height??""}h${-c($).width}z`)},[()=>({"--xy-minimap-mask-background-color-props":d(),"--xy-minimap-mask-stroke-color-props":p(),"--xy-minimap-mask-stroke-width-props":f()?f()*c(N):void 0})]),H(de,fe)};ae(Q,de=>{c(E).panZoom&&de(te)})}H(me,re)},$$slots:{default:!0}})),A(ie)}return H(e,F),ce(W)}se(id,{position:{},ariaLabel:{},nodeStrokeColor:{},nodeColor:{},nodeClass:{},nodeBorderRadius:{},nodeStrokeWidth:{},bgColor:{},maskColor:{},maskStrokeColor:{},maskStrokeWidth:{},width:{},height:{},pannable:{},zoomable:{},inversePan:{},zoomStep:{},class:{}},[],[],!0);var Dm=J("<div><!></div>");function sd(e,t){ue(t,!0);let n=y(t,"nodeId",7),r=y(t,"position",23,()=>ve.Top),o=y(t,"align",7,"center"),i=y(t,"offset",7,10),s=y(t,"isVisible",7),a=y(t,"children",7),l=Te(t,["$$slots","$$events","$$legacy","$$host","nodeId","position","align","offset","isVisible","children"]);const u=jt(),{getNodesBounds:d}=ot(),p=Dn("svelteflow__node_id");let f=z(()=>(u.nodes,(Array.isArray(n())?n():[n()??p]).reduce((E,L)=>{const D=u.nodeLookup.get(L);return D&&E.push(D),E},[]))),g=z(()=>{const E=d(c(f));return E?Y1(E,u.viewport,r(),i(),o()):""}),h=z(()=>c(f).length===0?1:Math.max(...c(f).map(E=>(E.internals.z||5)+1))),v=z(()=>u.nodes.filter(E=>E.selected).length),w=z(()=>typeof s()=="boolean"?s():c(f).length===1&&c(f)[0].selected&&c(v)===1);var b={get nodeId(){return n()},set nodeId(E){n(E),m()},get position(){return r()},set position(E=ve.Top){r(E),m()},get align(){return o()},set align(E="center"){o(E),m()},get offset(){return i()},set offset(E=10){i(E),m()},get isVisible(){return s()},set isVisible(E){s(E),m()},get children(){return a()},set children(E){a(E),m()}},k=Ce(),x=oe(k);{var S=E=>{var L=Dm();Ue(L,(q,B)=>({class:"svelte-flow__node-toolbar","data-id":q,...l,[nn]:B}),[()=>c(f).reduce((q,B)=>`${q}${B.id} `,"").trim(),()=>({display:hc().value?"none":void 0,position:"absolute",transform:c(g),"z-index":c(h)})]);var D=I(L);Xe(D,()=>a()??tt),A(L),pt(L,(q,B)=>gc?.(q,B),()=>"root"),H(E,L)};ae(x,E=>{u.domNode&&c(w)&&c(f)&&E(S)})}return H(e,k),ce(b)}se(sd,{nodeId:{},position:{},align:{},offset:{},isVisible:{},children:{}},[],[],!0);function En(e){const t=z(jt),n=z(()=>c(t).nodes),r=z(()=>c(t).nodeLookup);let o=[],i=!0;const s=z(()=>{c(n);const a=[],l=Array.isArray(e),u=l?e:[e];for(const d of u){const p=c(r).get(d)?.internals.userNode;p&&a.push({id:p.id,type:p.type,data:p.data})}return(!n0(a,o)||i)&&(o=a,i=!1),l?o:o[0]??null});return{get current(){return c(s)}}}const ad="tinyflow-component",Hm=[{value:"String",label:"String"},{value:"Number",label:"Number"},{value:"Boolean",label:"Boolean"},{value:"File",label:"File"},{value:"Object",label:"Object"},{value:"Array",label:"Array"}],Mm=[{value:"ref",label:"引用"},{value:"fixed",label:"固定值"}],ta=[{label:"文字",value:"text"},{label:"图片",value:"image"},{label:"视频",value:"video"},{label:"音频",value:"audio"},{label:"文件",value:"file"},{label:"其他",value:"other"}],Tm=[{label:"当行输入框",value:"input"},{label:"多行输入框",value:"textarea"},{label:"下拉菜单",value:"select"},{label:"单选",value:"radio"},{label:"多选",value:"checkbox"}],Vm=[{label:"单选",value:"radio"},{label:"多选",value:"checkbox"}];class Om{options;rootEl;svelteFlowInstance;constructor(t){if(typeof t.element!="string"&&!(t.element instanceof Element))throw new Error("element must be a string or Element");this._setOptions(t),this._init()}_init(){if(typeof this.options.element=="string"){if(this.rootEl=document.querySelector(this.options.element),!this.rootEl)throw new Error(`element not found by document.querySelector('${this.options.element}')`)}else if(this.options.element instanceof Element)this.rootEl=this.options.element;else throw new Error("element must be a string or Element");const t=document.createElement(ad);t.style.display="block",t.style.width="100%",t.style.height="100%",t.classList.add("tf-theme-light"),t.options=this.options,t.onInit=n=>{this.svelteFlowInstance=n},this.rootEl.appendChild(t)}_setOptions(t){this.options={...t}}getOptions(){return this.options}getData(){return this.svelteFlowInstance.toObject()}setData(t){this.options.data=t;const n=document.createElement(ad);n.style.display="block",n.style.width="100%",n.style.height="100%",n.classList.add("tf-theme-light"),n.options=this.options,n.onInit=r=>{this.svelteFlowInstance=r},this.destroy(),this.rootEl.appendChild(n)}destroy(){for(;this.rootEl.firstChild;)this.rootEl.removeChild(this.rootEl.firstChild)}}const Am=()=>{let e=Se([]),t=Se([]),n=Se({x:250,y:100,zoom:1});return{init:(r,o)=>{j(e,r),j(t,o)},getNodes:()=>c(e),setNodes:r=>{j(e,r)},getEdges:()=>c(t),setEdges:r=>{j(t,r)},getViewport:()=>c(n),setViewport:r=>{j(n,r)},getNode:r=>c(e).find(o=>o.id===r),addNode:r=>{j(e,[...c(e),r])},removeNode:r=>{j(e,c(e).filter(o=>o.id!==r))},updateNode:(r,o)=>{j(e,c(e).map(i=>i.id===r?{...i,...o}:i))},updateNodes:r=>{j(e,r(c(e)))},updateNodeData:(r,o)=>{j(e,c(e).map(i=>i.id===r?{...i,data:{...i.data,...o}}:i))},selectNodeOnly:r=>{j(e,c(e).map(o=>o.id===r?{...o,selected:!0}:{...o,selected:!1}))},getEdge:r=>c(t).find(o=>o.id===r),addEdge:r=>{j(t,[...c(t),r])},removeEdge:r=>{j(t,c(t).filter(o=>o.id!==r))},updateEdge:(r,o)=>{j(t,c(t).map(i=>i.id===r?{...i,...o}:i))},updateEdges:r=>{j(t,r(c(t)))},updateEdgeData:(r,o)=>{j(t,c(t).map(i=>i.id===r?{...i,data:{...i.data,...o}}:i))}}},Re=Am();var Im=J("<button><!></button>");function Pe(e,t){ue(t,!0);const n=y(t,"children",7),r=y(t,"primary",7),o=Te(t,["$$slots","$$events","$$legacy","$$host","children","primary"]);var i={get children(){return n()},set children(l){n(l),m()},get primary(){return r()},set primary(l){r(l),m()}},s=Im();Ue(s,()=>({type:"button",...o,class:`tf-btn ${r()?"tf-btn-primary":""} nopan nodrag ${t.class??""}`}));var a=I(s);return Xe(a,()=>n()??tt),A(s),H(e,s),ce(i)}se(Pe,{children:{},primary:{}},[],[],!0);var qm=J("<input/>");function ld(e,t){ue(t,!0);const n=Te(t,["$$slots","$$events","$$legacy","$$host"]);var r=qm();rn(r),Ue(r,()=>({type:"checkbox",...n,class:`tf-checkbox nopan nodrag ${t.class??""}`})),H(e,r),ce()}se(ld,{},[],[],!0);var Zm=J('<div><input type="hidden"/> <!> <!></div>');const Rm={hash:"svelte-1swt2gg",code:".tf-chosen.svelte-1swt2gg {display:flex;flex-direction:row;align-items:center;justify-content:space-between;gap:5px;}"};function ud(e,t){ue(t,!0),He(e,Rm);const n=y(t,"placeholder",7),r=y(t,"label",7),o=y(t,"value",7),i=y(t,"buttonText",7,"选择..."),s=y(t,"onChosen",7),a=Te(t,["$$slots","$$events","$$legacy","$$host","placeholder","label","value","buttonText","onChosen"]);var l={get placeholder(){return n()},set placeholder(g){n(g),m()},get label(){return r()},set label(g){r(g),m()},get value(){return o()},set value(g){o(g),m()},get buttonText(){return i()},set buttonText(g="选择..."){i(g),m()},get onChosen(){return s()},set onChosen(g){s(g),m()}},u=Zm();Ue(u,()=>({...a,class:`tf-chosen nopan nodrag ${t.class??""}`}),void 0,void 0,"svelte-1swt2gg");var d=I(u);rn(d);var p=V(d,2);We(p,{get value(){return r()},get placeholder(){return n()},style:"flex-grow: 1;",disabled:!0});var f=V(p,2);return Pe(f,{onclick:g=>{s()?.(o(),r(),g)},style:"padding: 3px",children:(g,h)=>{he();var v=_e();xe(()=>Oe(v,i())),H(g,v)},$$slots:{default:!0}}),A(u),xe(()=>Bo(d,o())),H(e,u),ce(l)}se(ud,{placeholder:{},label:{},value:{},buttonText:{},onChosen:{}},[],[],!0);var Bm=J("<input/>");function We(e,t){ue(t,!0);const n=Te(t,["$$slots","$$events","$$legacy","$$host"]);var r=Bm();rn(r),Ue(r,()=>({type:"text",spellcheck:"false",...n,class:`tf-input  nopan nodrag ${t.class??""}`})),H(e,r),ce()}se(We,{},[],[],!0);var Km=J("<textarea></textarea>");function Ie(e,t){ue(t,!0);const n=y(t,"value",7),r=Te(t,["$$slots","$$events","$$legacy","$$host","value"]);var o={get value(){return n()},set value(s){n(s),m()}},i=Km();return Gf(i),Ue(i,()=>({spellcheck:"false",...r,class:`tf-textarea nodrag nowheel ${t.class??""}`,value:n()||""})),H(e,i),ce(o)}se(Ie,{value:{}},[],[],!0);var Xm=J('<div role="button"><!></div>'),Ym=J("<div></div>");function cd(e,t){const n=Hl(t,["children","$$slots","$$events","$$legacy","$$host"]),r=Hl(n,["items","onChange","activeIndex"]);ue(t,!1);let o=y(t,"items",28,()=>[]),i=y(t,"onChange",12,()=>{}),s=y(t,"activeIndex",12,0);function a(d,p){s(p),i()?.(d,p)}var l={get items(){return o()},set items(d){o(d),m()},get onChange(){return i()},set onChange(d){i(d),m()},get activeIndex(){return s()},set activeIndex(d){s(d),m()}};Ll();var u=Ym();return Ue(u,()=>({...r,class:`tf-tabs ${ns(r),nt(()=>r.class)??""}`})),ft(u,5,o,kr,(d,p,f)=>{var g=Xm();ye(g,"tabindex",f),g.__click=()=>a(c(p),f),g.__keydown=b=>{(b.key==="Enter"||b.key===" ")&&(b.preventDefault(),a(c(p),f))};var h=I(g);{var v=b=>{var k=_e();xe(()=>Oe(k,(c(p),nt(()=>c(p).label)))),H(b,k)},w=b=>{var k=Ce(),x=oe(k);Xe(x,()=>(c(p),nt(()=>c(p).label)??tt)),H(b,k)};ae(h,b=>{c(p),nt(()=>typeof c(p).label=="string")?b(v):b(w,!1)})}A(g),xe(()=>bt(g,1,`tf-tabs-item ${f===s()?"active":""}`)),H(d,g)}),A(u),H(e,u),ce(l)}wn(["click","keydown"]),se(cd,{items:{},onChange:{},activeIndex:{}},[],[],!0);var jm=(e,t,n)=>t(c(n)),Wm=(e,t,n)=>{(e.key==="Enter"||e.key===" ")&&(e.preventDefault(),t(c(n)))},Fm=J('<span class="tf-collapse-item-title-icon"><!></span>'),Gm=J('<div class="tf-collapse-item-description"><!></div>'),Um=J('<div class="tf-collapse-item-content"><!></div>'),Jm=J('<div class="tf-collapse-item"><div class="tf-collapse-item-title" role="button"><!> <!> <span><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M13.1717 12.0007L8.22192 7.05093L9.63614 5.63672L16.0001 12.0007L9.63614 18.3646L8.22192 16.9504L13.1717 12.0007Z"></path></svg></span></div> <!> <!></div>'),Qm=J("<div></div>");const e2={hash:"svelte-1jfktzw",code:`
    /* 定义旋转的 CSS 类 */.rotate-90.svelte-1jfktzw {transform:rotate(90deg);transition:transform 0.3s ease;}`};function dd(e,t){ue(t,!0),He(e,e2);let n=y(t,"items",7),r=y(t,"onChange",7),o=y(t,"activeKeys",31,()=>Mt([]));function i(l){o().includes(l.key)?o(o().filter(u=>u!==l.key)):(o().push(l.key),o(o())),r()?.(l,o())}var s={get items(){return n()},set items(l){n(l),m()},get onChange(){return r()},set onChange(l){r(l),m()},get activeKeys(){return o()},set activeKeys(l=[]){o(l),m()}},a=Qm();return ft(a,21,n,kr,(l,u,d)=>{var p=Jm(),f=I(p);ye(f,"tabindex",d),f.__click=[jm,i,u],f.__keydown=[Wm,i,u];var g=I(f);{var h=E=>{var L=Fm(),D=I(L);In(D,{get target(){return c(u).icon}}),A(L),H(E,L)};ae(g,E=>{c(u).icon&&E(h)})}var v=V(g,2);In(v,{get target(){return c(u).title}});var w=V(v,2);A(f);var b=V(f,2);{var k=E=>{var L=Gm(),D=I(L);In(D,{get target(){return c(u).description}}),A(L),H(E,L)};ae(b,E=>{c(u).description&&E(k)})}var x=V(b,2);{var S=E=>{var L=Um(),D=I(L);In(D,{get target(){return c(u).content}}),A(L),H(E,L)};ae(x,E=>{o().includes(c(u).key)&&E(S)})}A(p),xe(E=>bt(w,1,`tf-collapse-item-title-arrow ${E??""}`,"svelte-1jfktzw"),[()=>o().includes(c(u).key)?"rotate-90":""]),H(l,p)}),A(a),xe(()=>{rt(a,t.style),bt(a,1,`tf-collapse ${t.class??""}`,"svelte-1jfktzw")}),H(e,a),ce(s)}wn(["click","keydown"]),se(dd,{items:{},onChange:{},activeKeys:{}},[],[],!0);function In(e,t){ue(t,!0);let n=y(t,"target",7);typeof n()>"u"&&n("undefined");var r={get target(){return n()},set target(l){n(l),m()}},o=Ce(),i=oe(o);{var s=l=>{var u=Ce(),d=oe(u);Xe(d,()=>n()??tt),H(l,u)},a=l=>{var u=Ce(),d=oe(u);cs(d,n),H(l,u)};ae(i,l=>{typeof n()=="function"?l(s):l(a,!1)})}return H(e,o),ce(r)}se(In,{target:{}},[],[],!0);var t2=(e,t,n)=>t(c(n)),n2=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M12 14L8 10H16L12 14Z"></path></svg>'),r2=J('<div class="tf-select-content-children"><!></div>'),o2=J('<button class="tf-select-content-item"><span><!></span> <!></button> <!>',1),i2=J('<div class="tf-select-content nopan nodrag"><!></div>'),s2=J("<!> <!>",1),a2=J('<div class="tf-select-input-placeholder"> </div>'),l2=J('<button><div class="tf-select-input-value"></div> <div class="tf-select-input-arrow"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11.9999 13.1714L16.9497 8.22168L18.3639 9.63589L11.9999 15.9999L5.63599 9.63589L7.0502 8.22168L11.9999 13.1714Z"></path></svg></div></button>'),u2=J("<div><!></div>");function it(e,t){ue(t,!0);const n=(k,x=tt)=>{var S=Ce(),E=oe(S);ft(E,19,x,(L,D)=>`${D}_${L.value}`,(L,D)=>{var q=o2(),B=oe(q);B.__click=[t2,h,D];var U=I(B),O=I(U);{var $=N=>{var T=n2();H(N,T)};ae(O,N=>{c(D).children&&c(D).children.length>0&&N($)})}A(U);var C=V(U,2);In(C,{get target(){return c(D).label}}),A(B);var _=V(B,2);{var P=N=>{var T=r2(),Z=I(T);n(Z,()=>c(D).children),A(T),H(N,T)};ae(_,N=>{c(D).children&&c(D).children.length>0&&(a()||u().includes(c(D).value))&&N(P)})}H(L,q)}),H(k,S)};let r=y(t,"items",7),o=y(t,"onSelect",7),i=y(t,"value",23,()=>[]),s=y(t,"defaultValue",23,()=>[]),a=y(t,"expandAll",7,!0),l=y(t,"multiple",7,!1),u=y(t,"expandValue",23,()=>[]),d=y(t,"placeholder",7),p=Te(t,["$$slots","$$events","$$legacy","$$host","items","onSelect","value","defaultValue","expandAll","multiple","expandValue","placeholder"]),f=z(()=>{const k=[],x=S=>{for(let E of S)i().length>0?i().includes(E.value)&&k.push(E):s().includes(E.value)&&k.push(E),E.children&&E.children.length>0&&x(E.children)};return x(r()),k}),g;function h(k){g?.hide(),o()?.(k)}var v={get items(){return r()},set items(k){r(k),m()},get onSelect(){return o()},set onSelect(k){o(k),m()},get value(){return i()},set value(k=[]){i(k),m()},get defaultValue(){return s()},set defaultValue(k=[]){s(k),m()},get expandAll(){return a()},set expandAll(k=!0){a(k),m()},get multiple(){return l()},set multiple(k=!1){l(k),m()},get expandValue(){return u()},set expandValue(k=[]){u(k),m()},get placeholder(){return d()},set placeholder(k){d(k),m()}},w=u2();Ue(w,()=>({...p,class:`tf-select ${p.class??""}`}));var b=I(w);return Et(lr(b,{floating:k=>{var x=i2(),S=I(x);n(S,r),A(x),H(k,x)},children:(k,x)=>{var S=l2();Ue(S,()=>({class:"tf-select-input nopan nodrag",...p}));var E=I(S);ft(E,23,()=>c(f),(L,D)=>`${D}_${L.value}`,(L,D,q)=>{var B=Ce(),U=oe(B);{var O=C=>{var _=Ce(),P=oe(_);{var N=T=>{In(T,{get target(){return c(D).label}})};ae(P,T=>{c(q)===0&&T(N)})}H(C,_)},$=C=>{var _=s2(),P=oe(_);In(P,{get target(){return c(D).label}});var N=V(P,2);{var T=Z=>{var X=_e(",");H(Z,X)};ae(N,Z=>{c(q)<c(f).length-1&&Z(T)})}H(C,_)};ae(U,C=>{l()?C($,!1):C(O)})}H(L,B)},L=>{var D=a2(),q=I(D,!0);A(D),xe(()=>Oe(q,d())),H(L,D)}),A(E),he(2),A(S),H(k,S)},$$slots:{floating:!0,default:!0}}),k=>g=k,()=>g),A(w),H(e,w),ce(v)}wn(["click"]),se(it,{items:{},onSelect:{},value:{},defaultValue:{},expandAll:{},multiple:{},expandValue:{},placeholder:{}},[],[],!0);const _o=Math.min,Mr=Math.max,$i=Math.round,fn=e=>({x:e,y:e}),c2={left:"right",right:"left",bottom:"top",top:"bottom"},d2={start:"end",end:"start"};function na(e,t,n){return Mr(e,_o(t,n))}function ko(e,t){return typeof e=="function"?e(t):e}function ar(e){return e.split("-")[0]}function $o(e){return e.split("-")[1]}function fd(e){return e==="x"?"y":"x"}function ra(e){return e==="y"?"height":"width"}const f2=new Set(["top","bottom"]);function qn(e){return f2.has(ar(e))?"y":"x"}function oa(e){return fd(qn(e))}function p2(e,t,n){n===void 0&&(n=!1);const r=$o(e),o=oa(e),i=ra(o);let s=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[i]>t.floating[i]&&(s=Ci(s)),[s,Ci(s)]}function g2(e){const t=Ci(e);return[ia(e),t,ia(t)]}function ia(e){return e.replace(/start|end/g,t=>d2[t])}const pd=["left","right"],gd=["right","left"],h2=["top","bottom"],v2=["bottom","top"];function m2(e,t,n){switch(e){case"top":case"bottom":return n?t?gd:pd:t?pd:gd;case"left":case"right":return t?h2:v2;default:return[]}}function y2(e,t,n,r){const o=$o(e);let i=m2(ar(e),n==="start",r);return o&&(i=i.map(s=>s+"-"+o),t&&(i=i.concat(i.map(ia)))),i}function Ci(e){return e.replace(/left|right|bottom|top/g,t=>c2[t])}function w2(e){return{top:0,right:0,bottom:0,left:0,...e}}function hd(e){return typeof e!="number"?w2(e):{top:e,right:e,bottom:e,left:e}}function Si(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function vd(e,t,n){let{reference:r,floating:o}=e;const i=qn(t),s=oa(t),a=ra(s),l=ar(t),u=i==="y",d=r.x+r.width/2-o.width/2,p=r.y+r.height/2-o.height/2,f=r[a]/2-o[a]/2;let g;switch(l){case"top":g={x:d,y:r.y-o.height};break;case"bottom":g={x:d,y:r.y+r.height};break;case"right":g={x:r.x+r.width,y:p};break;case"left":g={x:r.x-o.width,y:p};break;default:g={x:r.x,y:r.y}}switch($o(t)){case"start":g[s]-=f*(n&&u?-1:1);break;case"end":g[s]+=f*(n&&u?-1:1);break}return g}const b2=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:s}=n,a=i.filter(Boolean),l=await(s.isRTL==null?void 0:s.isRTL(t));let u=await s.getElementRects({reference:e,floating:t,strategy:o}),{x:d,y:p}=vd(u,r,l),f=r,g={},h=0;for(let v=0;v<a.length;v++){const{name:w,fn:b}=a[v],{x:k,y:x,data:S,reset:E}=await b({x:d,y:p,initialPlacement:r,placement:f,strategy:o,middlewareData:g,rects:u,platform:s,elements:{reference:e,floating:t}});d=k??d,p=x??p,g={...g,[w]:{...g[w],...S}},E&&h<=50&&(h++,typeof E=="object"&&(E.placement&&(f=E.placement),E.rects&&(u=E.rects===!0?await s.getElementRects({reference:e,floating:t,strategy:o}):E.rects),{x:d,y:p}=vd(u,f,l)),v=-1)}return{x:d,y:p,placement:f,strategy:o,middlewareData:g}};async function md(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:i,rects:s,elements:a,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:d="viewport",elementContext:p="floating",altBoundary:f=!1,padding:g=0}=ko(t,e),h=hd(g),v=a[f?p==="floating"?"reference":"floating":p],w=Si(await i.getClippingRect({element:(n=await(i.isElement==null?void 0:i.isElement(v)))==null||n?v:v.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(a.floating)),boundary:u,rootBoundary:d,strategy:l})),b=p==="floating"?{x:r,y:o,width:s.floating.width,height:s.floating.height}:s.reference,k=await(i.getOffsetParent==null?void 0:i.getOffsetParent(a.floating)),x=await(i.isElement==null?void 0:i.isElement(k))?await(i.getScale==null?void 0:i.getScale(k))||{x:1,y:1}:{x:1,y:1},S=Si(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:b,offsetParent:k,strategy:l}):b);return{top:(w.top-S.top+h.top)/x.y,bottom:(S.bottom-w.bottom+h.bottom)/x.y,left:(w.left-S.left+h.left)/x.x,right:(S.right-w.right+h.right)/x.x}}const x2=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:i,platform:s,elements:a,middlewareData:l}=t,{element:u,padding:d=0}=ko(e,t)||{};if(u==null)return{};const p=hd(d),f={x:n,y:r},g=oa(o),h=ra(g),v=await s.getDimensions(u),w=g==="y",b=w?"top":"left",k=w?"bottom":"right",x=w?"clientHeight":"clientWidth",S=i.reference[h]+i.reference[g]-f[g]-i.floating[h],E=f[g]-i.reference[g],L=await(s.getOffsetParent==null?void 0:s.getOffsetParent(u));let D=L?L[x]:0;(!D||!await(s.isElement==null?void 0:s.isElement(L)))&&(D=a.floating[x]||i.floating[h]);const q=S/2-E/2,B=D/2-v[h]/2-1,U=_o(p[b],B),O=_o(p[k],B),$=U,C=D-v[h]-O,_=D/2-v[h]/2+q,P=na($,_,C),N=!l.arrow&&$o(o)!=null&&_!==P&&i.reference[h]/2-(_<$?U:O)-v[h]/2<0,T=N?_<$?_-$:_-C:0;return{[g]:f[g]+T,data:{[g]:P,centerOffset:_-P-T,...N&&{alignmentOffset:T}},reset:N}}}),_2=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:i,rects:s,initialPlacement:a,platform:l,elements:u}=t,{mainAxis:d=!0,crossAxis:p=!0,fallbackPlacements:f,fallbackStrategy:g="bestFit",fallbackAxisSideDirection:h="none",flipAlignment:v=!0,...w}=ko(e,t);if((n=i.arrow)!=null&&n.alignmentOffset)return{};const b=ar(o),k=qn(a),x=ar(a)===a,S=await(l.isRTL==null?void 0:l.isRTL(u.floating)),E=f||(x||!v?[Ci(a)]:g2(a)),L=h!=="none";!f&&L&&E.push(...y2(a,v,h,S));const D=[a,...E],q=await md(t,w),B=[];let U=((r=i.flip)==null?void 0:r.overflows)||[];if(d&&B.push(q[b]),p){const _=p2(o,s,S);B.push(q[_[0]],q[_[1]])}if(U=[...U,{placement:o,overflows:B}],!B.every(_=>_<=0)){var O,$;const _=(((O=i.flip)==null?void 0:O.index)||0)+1,P=D[_];if(P&&(!(p==="alignment"&&k!==qn(P))||U.every(T=>qn(T.placement)===k?T.overflows[0]>0:!0)))return{data:{index:_,overflows:U},reset:{placement:P}};let N=($=U.filter(T=>T.overflows[0]<=0).sort((T,Z)=>T.overflows[1]-Z.overflows[1])[0])==null?void 0:$.placement;if(!N)switch(g){case"bestFit":{var C;const T=(C=U.filter(Z=>{if(L){const X=qn(Z.placement);return X===k||X==="y"}return!0}).map(Z=>[Z.placement,Z.overflows.filter(X=>X>0).reduce((X,M)=>X+M,0)]).sort((Z,X)=>Z[1]-X[1])[0])==null?void 0:C[0];T&&(N=T);break}case"initialPlacement":N=a;break}if(o!==N)return{reset:{placement:N}}}return{}}}},k2=new Set(["left","top"]);async function $2(e,t){const{placement:n,platform:r,elements:o}=e,i=await(r.isRTL==null?void 0:r.isRTL(o.floating)),s=ar(n),a=$o(n),l=qn(n)==="y",u=k2.has(s)?-1:1,d=i&&l?-1:1,p=ko(t,e);let{mainAxis:f,crossAxis:g,alignmentAxis:h}=typeof p=="number"?{mainAxis:p,crossAxis:0,alignmentAxis:null}:{mainAxis:p.mainAxis||0,crossAxis:p.crossAxis||0,alignmentAxis:p.alignmentAxis};return a&&typeof h=="number"&&(g=a==="end"?h*-1:h),l?{x:g*d,y:f*u}:{x:f*u,y:g*d}}const C2=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:i,placement:s,middlewareData:a}=t,l=await $2(t,e);return s===((n=a.offset)==null?void 0:n.placement)&&(r=a.arrow)!=null&&r.alignmentOffset?{}:{x:o+l.x,y:i+l.y,data:{...l,placement:s}}}}},S2=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:s=!1,limiter:a={fn:w=>{let{x:b,y:k}=w;return{x:b,y:k}}},...l}=ko(e,t),u={x:n,y:r},d=await md(t,l),p=qn(ar(o)),f=fd(p);let g=u[f],h=u[p];if(i){const w=f==="y"?"top":"left",b=f==="y"?"bottom":"right",k=g+d[w],x=g-d[b];g=na(k,g,x)}if(s){const w=p==="y"?"top":"left",b=p==="y"?"bottom":"right",k=h+d[w],x=h-d[b];h=na(k,h,x)}const v=a.fn({...t,[f]:g,[p]:h});return{...v,data:{x:v.x-n,y:v.y-r,enabled:{[f]:i,[p]:s}}}}}};function Ei(){return typeof window<"u"}function Tr(e){return yd(e)?(e.nodeName||"").toLowerCase():"#document"}function Pt(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Nn(e){var t;return(t=(yd(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function yd(e){return Ei()?e instanceof Node||e instanceof Pt(e).Node:!1}function Wt(e){return Ei()?e instanceof Element||e instanceof Pt(e).Element:!1}function pn(e){return Ei()?e instanceof HTMLElement||e instanceof Pt(e).HTMLElement:!1}function wd(e){return!Ei()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof Pt(e).ShadowRoot}const E2=new Set(["inline","contents"]);function Co(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=Ft(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!E2.has(o)}const N2=new Set(["table","td","th"]);function P2(e){return N2.has(Tr(e))}const z2=[":popover-open",":modal"];function Ni(e){return z2.some(t=>{try{return e.matches(t)}catch{return!1}})}const L2=["transform","translate","scale","rotate","perspective"],D2=["transform","translate","scale","rotate","perspective","filter"],H2=["paint","layout","strict","content"];function sa(e){const t=aa(),n=Wt(e)?Ft(e):e;return L2.some(r=>n[r]?n[r]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||D2.some(r=>(n.willChange||"").includes(r))||H2.some(r=>(n.contain||"").includes(r))}function M2(e){let t=Zn(e);for(;pn(t)&&!Vr(t);){if(sa(t))return t;if(Ni(t))return null;t=Zn(t)}return null}function aa(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const T2=new Set(["html","body","#document"]);function Vr(e){return T2.has(Tr(e))}function Ft(e){return Pt(e).getComputedStyle(e)}function Pi(e){return Wt(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Zn(e){if(Tr(e)==="html")return e;const t=e.assignedSlot||e.parentNode||wd(e)&&e.host||Nn(e);return wd(t)?t.host:t}function bd(e){const t=Zn(e);return Vr(t)?e.ownerDocument?e.ownerDocument.body:e.body:pn(t)&&Co(t)?t:bd(t)}function xd(e,t,n){var r;t===void 0&&(t=[]);const o=bd(e),i=o===((r=e.ownerDocument)==null?void 0:r.body),s=Pt(o);return i?(la(s),t.concat(s,s.visualViewport||[],Co(o)?o:[],[])):t.concat(o,xd(o,[]))}function la(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function _d(e){const t=Ft(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=pn(e),i=o?e.offsetWidth:n,s=o?e.offsetHeight:r,a=$i(n)!==i||$i(r)!==s;return a&&(n=i,r=s),{width:n,height:r,$:a}}function kd(e){return Wt(e)?e:e.contextElement}function Or(e){const t=kd(e);if(!pn(t))return fn(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:i}=_d(t);let s=(i?$i(n.width):n.width)/r,a=(i?$i(n.height):n.height)/o;return(!s||!Number.isFinite(s))&&(s=1),(!a||!Number.isFinite(a))&&(a=1),{x:s,y:a}}const V2=fn(0);function $d(e){const t=Pt(e);return!aa()||!t.visualViewport?V2:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function O2(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==Pt(e)?!1:t}function So(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),i=kd(e);let s=fn(1);t&&(r?Wt(r)&&(s=Or(r)):s=Or(e));const a=O2(i,n,r)?$d(i):fn(0);let l=(o.left+a.x)/s.x,u=(o.top+a.y)/s.y,d=o.width/s.x,p=o.height/s.y;if(i){const f=Pt(i),g=r&&Wt(r)?Pt(r):r;let h=f,v=la(h);for(;v&&r&&g!==h;){const w=Or(v),b=v.getBoundingClientRect(),k=Ft(v),x=b.left+(v.clientLeft+parseFloat(k.paddingLeft))*w.x,S=b.top+(v.clientTop+parseFloat(k.paddingTop))*w.y;l*=w.x,u*=w.y,d*=w.x,p*=w.y,l+=x,u+=S,h=Pt(v),v=la(h)}}return Si({width:d,height:p,x:l,y:u})}function zi(e,t){const n=Pi(e).scrollLeft;return t?t.left+n:So(Nn(e)).left+n}function Cd(e,t){const n=e.getBoundingClientRect(),r=n.left+t.scrollLeft-zi(e,n),o=n.top+t.scrollTop;return{x:r,y:o}}function A2(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const i=o==="fixed",s=Nn(r),a=t?Ni(t.floating):!1;if(r===s||a&&i)return n;let l={scrollLeft:0,scrollTop:0},u=fn(1);const d=fn(0),p=pn(r);if((p||!p&&!i)&&((Tr(r)!=="body"||Co(s))&&(l=Pi(r)),pn(r))){const g=So(r);u=Or(r),d.x=g.x+r.clientLeft,d.y=g.y+r.clientTop}const f=s&&!p&&!i?Cd(s,l):fn(0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-l.scrollLeft*u.x+d.x+f.x,y:n.y*u.y-l.scrollTop*u.y+d.y+f.y}}function I2(e){return Array.from(e.getClientRects())}function q2(e){const t=Nn(e),n=Pi(e),r=e.ownerDocument.body,o=Mr(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=Mr(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let s=-n.scrollLeft+zi(e);const a=-n.scrollTop;return Ft(r).direction==="rtl"&&(s+=Mr(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:s,y:a}}const Sd=25;function Z2(e,t){const n=Pt(e),r=Nn(e),o=n.visualViewport;let i=r.clientWidth,s=r.clientHeight,a=0,l=0;if(o){i=o.width,s=o.height;const d=aa();(!d||d&&t==="fixed")&&(a=o.offsetLeft,l=o.offsetTop)}const u=zi(r);if(u<=0){const d=r.ownerDocument,p=d.body,f=getComputedStyle(p),g=d.compatMode==="CSS1Compat"&&parseFloat(f.marginLeft)+parseFloat(f.marginRight)||0,h=Math.abs(r.clientWidth-p.clientWidth-g);h<=Sd&&(i-=h)}else u<=Sd&&(i+=u);return{width:i,height:s,x:a,y:l}}const R2=new Set(["absolute","fixed"]);function B2(e,t){const n=So(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=pn(e)?Or(e):fn(1),s=e.clientWidth*i.x,a=e.clientHeight*i.y,l=o*i.x,u=r*i.y;return{width:s,height:a,x:l,y:u}}function Ed(e,t,n){let r;if(t==="viewport")r=Z2(e,n);else if(t==="document")r=q2(Nn(e));else if(Wt(t))r=B2(t,n);else{const o=$d(e);r={x:t.x-o.x,y:t.y-o.y,width:t.width,height:t.height}}return Si(r)}function Nd(e,t){const n=Zn(e);return n===t||!Wt(n)||Vr(n)?!1:Ft(n).position==="fixed"||Nd(n,t)}function K2(e,t){const n=t.get(e);if(n)return n;let r=xd(e,[]).filter(a=>Wt(a)&&Tr(a)!=="body"),o=null;const i=Ft(e).position==="fixed";let s=i?Zn(e):e;for(;Wt(s)&&!Vr(s);){const a=Ft(s),l=sa(s);!l&&a.position==="fixed"&&(o=null),(i?!l&&!o:!l&&a.position==="static"&&o&&R2.has(o.position)||Co(s)&&!l&&Nd(e,s))?r=r.filter(u=>u!==s):o=a,s=Zn(s)}return t.set(e,r),r}function X2(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const i=[...n==="clippingAncestors"?Ni(t)?[]:K2(t,this._c):[].concat(n),r],s=i[0],a=i.reduce((l,u)=>{const d=Ed(t,u,o);return l.top=Mr(d.top,l.top),l.right=_o(d.right,l.right),l.bottom=_o(d.bottom,l.bottom),l.left=Mr(d.left,l.left),l},Ed(t,s,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}}function Y2(e){const{width:t,height:n}=_d(e);return{width:t,height:n}}function j2(e,t,n){const r=pn(t),o=Nn(t),i=n==="fixed",s=So(e,!0,i,t);let a={scrollLeft:0,scrollTop:0};const l=fn(0);function u(){l.x=zi(o)}if(r||!r&&!i)if((Tr(t)!=="body"||Co(o))&&(a=Pi(t)),r){const g=So(t,!0,i,t);l.x=g.x+t.clientLeft,l.y=g.y+t.clientTop}else o&&u();i&&!r&&o&&u();const d=o&&!r&&!i?Cd(o,a):fn(0),p=s.left+a.scrollLeft-l.x-d.x,f=s.top+a.scrollTop-l.y-d.y;return{x:p,y:f,width:s.width,height:s.height}}function ua(e){return Ft(e).position==="static"}function Pd(e,t){if(!pn(e)||Ft(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return Nn(e)===n&&(n=n.ownerDocument.body),n}function zd(e,t){const n=Pt(e);if(Ni(e))return n;if(!pn(e)){let o=Zn(e);for(;o&&!Vr(o);){if(Wt(o)&&!ua(o))return o;o=Zn(o)}return n}let r=Pd(e,t);for(;r&&P2(r)&&ua(r);)r=Pd(r,t);return r&&Vr(r)&&ua(r)&&!sa(r)?n:r||M2(e)||n}const W2=async function(e){const t=this.getOffsetParent||zd,n=this.getDimensions,r=await n(e.floating);return{reference:j2(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function F2(e){return Ft(e).direction==="rtl"}const G2={convertOffsetParentRelativeRectToViewportRelativeRect:A2,getDocumentElement:Nn,getClippingRect:X2,getOffsetParent:zd,getElementRects:W2,getClientRects:I2,getDimensions:Y2,getScale:Or,isElement:Wt,isRTL:F2},U2=C2,J2=S2,Q2=_2,ey=x2,ty=(e,t,n)=>{const r=new Map,o={platform:G2,...n},i={...o.platform,_c:r};return b2(e,t,{...o,platform:i})},ny=({trigger:e,triggerEvent:t,floatContent:n,placement:r="bottom",offsetOptions:o,flipOptions:i,shiftOptions:s,interactive:a,showArrow:l})=>{if(typeof e=="string"){const b=document.querySelector(e);if(b)e=b;else throw new Error("element not found by document.querySelector('"+e+"')")}let u;if(typeof n=="string"){const b=document.querySelector(n);if(b)u=b;else throw new Error("element not found by document.querySelector('"+n+"')")}else u=n;let d;l&&(d=document.createElement("div"),d.style.position="absolute",d.style.backgroundColor="#222",d.style.width="8px",d.style.height="8px",d.style.transform="rotate(45deg)",d.style.display="none",u.firstElementChild.before(d));function p(){ty(e,u,{placement:r,middleware:[U2(o),Q2(i),J2(s),...l?[ey({element:d})]:[]]}).then(({x:b,y:k,placement:x,middlewareData:S})=>{if(Object.assign(u.style,{left:`${b}px`,top:`${k}px`}),l){const{x:E,y:L}=S.arrow,D={top:"bottom",right:"left",bottom:"top",left:"right"}[x.split("-")[0]];Object.assign(d.style,{zIndex:-1,left:E!=null?`${E}px`:"",top:L!=null?`${L}px`:"",right:"",bottom:"",[D]:"2px"})}})}let f=!1;function g(){u.style.display="block",u.style.visibility="block",u.style.position="absolute",l&&(d.style.display="block"),f=!0,p()}function h(){u.style.display="none",l&&(d.style.display="none"),f=!1}function v(b){b.stopPropagation(),f?h():g()}function w(b){u.contains(b.target)||h()}return(!t||t.length==0)&&(t=["click"]),t.forEach(b=>{e.addEventListener(b,v)}),document.addEventListener("click",w),{destroy(){t.forEach(b=>{e.removeEventListener(b,v)}),document.removeEventListener("click",w)},hide(){h()},isVisible(){return f}}};var ry=J('<div style="position: relative"><div><!></div> <div style="display: none; width: 100%;z-index: 9999"><!></div></div>');function lr(e,t){ue(t,!0);const n=y(t,"children",7),r=y(t,"floating",7),o=y(t,"placement",7,"bottom");let i,s,a;Tn(()=>(a=ny({trigger:i,floatContent:s,interactive:!0,placement:o()}),()=>{a.destroy()}));function l(){a.hide()}var u={hide:l,get children(){return n()},set children(v){n(v),m()},get floating(){return r()},set floating(v){r(v),m()},get placement(){return o()},set placement(v="bottom"){o(v),m()}},d=ry(),p=I(d),f=I(p);Xe(f,n),A(p),Et(p,v=>i=v,()=>i);var g=V(p,2),h=I(g);return Xe(h,r),A(g),Et(g,v=>s=v,()=>s),A(d),H(e,d),ce(u)}se(lr,{children:{},floating:{},placement:{}},[],["hide"],!0);function Le(e,t){ue(t,!0);const n=y(t,"children",7),r=y(t,"level",7,1),o=y(t,"mt",7),i=y(t,"mb",7);var s={get children(){return n()},set children(u){n(u),m()},get level(){return r()},set level(u=1){r(u),m()},get mt(){return o()},set mt(u){o(u),m()},get mb(){return i()},set mb(u){i(u),m()}},a=Ce(),l=oe(a);return $p(l,()=>`h${r()}`,!1,(u,d)=>{Ue(u,()=>({class:"tf-heading",style:`margin-top:${o()||"0"};margin-bottom:${i()||"0"}`}));var p=Ce(),f=oe(p);Xe(f,()=>n()??tt),H(d,p)}),H(e,a),ce(s)}se(Le,{children:{},level:{},mt:{},mb:{}},[],[],!0);var oy=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="svelte-1rvn4a8"><path d="M4.5 10.5C3.675 10.5 3 11.175 3 12C3 12.825 3.675 13.5 4.5 13.5C5.325 13.5 6 12.825 6 12C6 11.175 5.325 10.5 4.5 10.5ZM19.5 10.5C18.675 10.5 18 11.175 18 12C18 12.825 18.675 13.5 19.5 13.5C20.325 13.5 21 12.825 21 12C21 11.175 20.325 10.5 19.5 10.5ZM12 10.5C11.175 10.5 10.5 11.175 10.5 12C10.5 12.825 11.175 13.5 12 13.5C12.825 13.5 13.5 12.825 13.5 12C13.5 11.175 12.825 10.5 12 10.5Z" class="svelte-1rvn4a8"></path></svg>');const iy={hash:"svelte-1rvn4a8",code:".input-btn-more {border:1px solid transparent;padding:3px;&:hover {background:#eee;border:1px solid transparent;}}"};function Eo(e,t){ue(t,!0),He(e,iy);const n=Te(t,["$$slots","$$events","$$legacy","$$host"]);Pe(e,Ae(()=>n,{get class(){return`input-btn-more ${t.class??""}`},children:(r,o)=>{var i=oy();H(r,i)},$$slots:{default:!0}})),ce()}se(Eo,{},[],[],!0);const sy=()=>({deleteNode:e=>{Re.removeNode(e),Re.updateEdges(t=>t.filter(n=>n.source!==e&&n.target!==e))}}),ur=(e=16)=>{const t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",n=new Uint8Array(e);return crypto.getRandomValues(n),Array.from(n,r=>t[r%t.length]).join("")},ay=()=>({copyNode:e=>{const t=Re.getNode(e);if(t){const n=ur(),r={...t,id:n,position:{x:t.position.x+50,y:t.position.y+50}};Re.updateNodes(o=>[...o.map(i=>({...i,selected:!1})),r])}}}),Ye=()=>Dn("svelteflow__node_id"),Rn=()=>Dn("tinyflow_options");var ly=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M17 6H22V8H20V21C20 21.5523 19.5523 22 19 22H5C4.44772 22 4 21.5523 4 21V8H2V6H7V3C7 2.44772 7.44772 2 8 2H16C16.5523 2 17 2.44772 17 3V6ZM18 8H6V20H18V8ZM9 11H11V17H9V11ZM13 11H15V17H13V11ZM9 4V6H15V4H9Z"></path></svg>'),uy=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M6.9998 6V3C6.9998 2.44772 7.44752 2 7.9998 2H19.9998C20.5521 2 20.9998 2.44772 20.9998 3V17C20.9998 17.5523 20.5521 18 19.9998 18H16.9998V20.9991C16.9998 21.5519 16.5499 22 15.993 22H4.00666C3.45059 22 3 21.5554 3 20.9991L3.0026 7.00087C3.0027 6.44811 3.45264 6 4.00942 6H6.9998ZM5.00242 8L5.00019 20H14.9998V8H5.00242ZM8.9998 6H16.9998V16H18.9998V4H8.9998V6Z"></path></svg>'),cy=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M8 18.3915V5.60846L18.2264 12L8 18.3915ZM6 3.80421V20.1957C6 20.9812 6.86395 21.46 7.53 21.0437L20.6432 12.848C21.2699 12.4563 21.2699 11.5436 20.6432 11.152L7.53 2.95621C6.86395 2.53993 6 3.01878 6 3.80421Z"></path></svg>'),dy=J('<div class="input-item svelte-1jesvb7">执行条件： <!></div>'),fy=(e,t,n)=>{const r=e.target.checked;t(n,{async:r})},py=(e,t,n)=>{const r=e.target.checked;t(n,{loopEnable:r})},gy=J('<div class="input-item svelte-1jesvb7">循环间隔时间（单位：毫秒）： <!></div> <div class="input-item svelte-1jesvb7">最大循环次数（0 表示不限制）： <!></div> <div class="input-item svelte-1jesvb7">退出条件： <!></div>',1),hy=J('<div class="settings svelte-1jesvb7"><div class="input-item svelte-1jesvb7">节点名称： <!></div> <div class="input-item svelte-1jesvb7">参数描述： <!></div> <!> <label class="input-item-inline svelte-1jesvb7"><span>异步执行：</span> <input type="checkbox"/></label> <label class="input-item-inline svelte-1jesvb7"><span>循环执行：</span> <input type="checkbox"/></label> <!></div>'),vy=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M3.33946 17.0002C2.90721 16.2515 2.58277 15.4702 2.36133 14.6741C3.3338 14.1779 3.99972 13.1668 3.99972 12.0002C3.99972 10.8345 3.3348 9.824 2.36353 9.32741C2.81025 7.71651 3.65857 6.21627 4.86474 4.99001C5.7807 5.58416 6.98935 5.65534 7.99972 5.072C9.01009 4.48866 9.55277 3.40635 9.4962 2.31604C11.1613 1.8846 12.8847 1.90004 14.5031 2.31862C14.4475 3.40806 14.9901 4.48912 15.9997 5.072C17.0101 5.65532 18.2187 5.58416 19.1346 4.99007C19.7133 5.57986 20.2277 6.25151 20.66 7.00021C21.0922 7.7489 21.4167 8.53025 21.6381 9.32628C20.6656 9.82247 19.9997 10.8336 19.9997 12.0002C19.9997 13.166 20.6646 14.1764 21.6359 14.673C21.1892 16.2839 20.3409 17.7841 19.1347 19.0104C18.2187 18.4163 17.0101 18.3451 15.9997 18.9284C14.9893 19.5117 14.4467 20.5941 14.5032 21.6844C12.8382 22.1158 11.1148 22.1004 9.49633 21.6818C9.55191 20.5923 9.00929 19.5113 7.99972 18.9284C6.98938 18.3451 5.78079 18.4162 4.86484 19.0103C4.28617 18.4205 3.77172 17.7489 3.33946 17.0002ZM8.99972 17.1964C10.0911 17.8265 10.8749 18.8227 11.2503 19.9659C11.7486 20.0133 12.2502 20.014 12.7486 19.9675C13.1238 18.8237 13.9078 17.8268 14.9997 17.1964C16.0916 16.5659 17.347 16.3855 18.5252 16.6324C18.8146 16.224 19.0648 15.7892 19.2729 15.334C18.4706 14.4373 17.9997 13.2604 17.9997 12.0002C17.9997 10.74 18.4706 9.5632 19.2729 8.6665C19.1688 8.4405 19.0538 8.21822 18.9279 8.00021C18.802 7.78219 18.667 7.57148 18.5233 7.36842C17.3457 7.61476 16.0911 7.43414 14.9997 6.80405C13.9083 6.17395 13.1246 5.17768 12.7491 4.03455C12.2509 3.98714 11.7492 3.98646 11.2509 4.03292C10.8756 5.17671 10.0916 6.17364 8.99972 6.80405C7.9078 7.43447 6.65245 7.61494 5.47428 7.36803C5.18485 7.77641 4.93463 8.21117 4.72656 8.66637C5.52881 9.56311 5.99972 10.74 5.99972 12.0002C5.99972 13.2604 5.52883 14.4372 4.72656 15.3339C4.83067 15.5599 4.94564 15.7822 5.07152 16.0002C5.19739 16.2182 5.3324 16.4289 5.47612 16.632C6.65377 16.3857 7.90838 16.5663 8.99972 17.1964ZM11.9997 15.0002C10.3429 15.0002 8.99972 13.6571 8.99972 12.0002C8.99972 10.3434 10.3429 9.00021 11.9997 9.00021C13.6566 9.00021 14.9997 10.3434 14.9997 12.0002C14.9997 13.6571 13.6566 15.0002 11.9997 15.0002ZM11.9997 13.0002C12.552 13.0002 12.9997 12.5525 12.9997 12.0002C12.9997 11.4479 12.552 11.0002 11.9997 11.0002C11.4474 11.0002 10.9997 11.4479 10.9997 12.0002C10.9997 12.5525 11.4474 13.0002 11.9997 13.0002Z"></path></svg>'),my=J('<div class="tf-node-toolbar svelte-1jesvb7"><!> <!> <!> <!></div>'),yy=J('<!> <div class="tf-node-wrapper"><div class="tf-node-wrapper-title">TinyFlow.ai</div> <div class="tf-node-wrapper-body"><!></div></div> <!> <!> <!>',1);const wy={hash:"svelte-1jesvb7",code:".tf-node-toolbar.svelte-1jesvb7 {display:flex;gap:5px;padding:5px;border-radius:5px;background:#fff;border:1px solid #eee;box-shadow:0 0 5px rgba(0, 0, 0, 0.1);}.tf-node-toolbar-item {border:1px solid transparent;}.settings.svelte-1jesvb7 {display:flex;flex-direction:column;gap:10px;padding:10px;background:#fff;border:1px solid #ddd;border-radius:5px;width:200px;box-shadow:0 0 10px 2px rgba(0, 0, 0, 0.1);}.settings.svelte-1jesvb7 .input-item:where(.svelte-1jesvb7) {display:flex;flex-direction:column;gap:3px;font-size:12px;color:#666;}.settings.svelte-1jesvb7 .input-item-inline:where(.svelte-1jesvb7) {display:flex;align-items:center;font-size:12px;color:#666;}"};function Ot(e,t){ue(t,!0),He(e,wy);const n=y(t,"data",7),r=y(t,"id",7,""),o=y(t,"icon",7),i=y(t,"handle",7),s=y(t,"children",7),a=y(t,"allowExecute",7,!0),l=y(t,"allowCopy",7,!0),u=y(t,"allowDelete",7,!0),d=y(t,"allowSetting",7,!0),p=y(t,"allowSettingOfCondition",7,!0),f=y(t,"showSourceHandle",7,!0),g=y(t,"showTargetHandle",7,!0),h=y(t,"onCollapse",7);let v=n().expand?["key"]:[];const{updateNodeData:w,getNode:b}=ot(),k=z(()=>[{key:"key",icon:o(),title:n().title,description:n().description,content:s()}]),{deleteNode:x}=sy(),{copyNode:S}=ay(),E=Rn(),L=()=>{E.onNodeExecute?.(b(r()))};let D=Ye();var q={get data(){return n()},set data(M){n(M),m()},get id(){return r()},set id(M=""){r(M),m()},get icon(){return o()},set icon(M){o(M),m()},get handle(){return i()},set handle(M){i(M),m()},get children(){return s()},set children(M){s(M),m()},get allowExecute(){return a()},set allowExecute(M=!0){a(M),m()},get allowCopy(){return l()},set allowCopy(M=!0){l(M),m()},get allowDelete(){return u()},set allowDelete(M=!0){u(M),m()},get allowSetting(){return d()},set allowSetting(M=!0){d(M),m()},get allowSettingOfCondition(){return p()},set allowSettingOfCondition(M=!0){p(M),m()},get showSourceHandle(){return f()},set showSourceHandle(M=!0){f(M),m()},get showTargetHandle(){return g()},set showTargetHandle(M=!0){g(M),m()},get onCollapse(){return h()},set onCollapse(M){h(M),m()}},B=yy(),U=oe(B);{var O=M=>{sd(M,{get position(){return ve.Top},align:"start",children:(Y,ee)=>{var ne=my(),R=I(ne);{var W=Q=>{Pe(Q,{class:"tf-node-toolbar-item",onclick:()=>{x(r())},children:(te,de)=>{var fe=ly();H(te,fe)},$$slots:{default:!0}})};ae(R,Q=>{u()&&Q(W)})}var F=V(R,2);{var ie=Q=>{Pe(Q,{class:"tf-node-toolbar-item",onclick:()=>{S(r())},children:(te,de)=>{var fe=uy();H(te,fe)},$$slots:{default:!0}})};ae(F,Q=>{l()&&Q(ie)})}var G=V(F,2);{var me=Q=>{Pe(Q,{class:"tf-node-toolbar-item",onclick:L,children:(te,de)=>{var fe=cy();H(te,fe)},$$slots:{default:!0}})};ae(G,Q=>{a()&&Q(me)})}var we=V(G,2);{var re=Q=>{lr(Q,{placement:"bottom",floating:te=>{var de=hy(),fe=I(de),le=V(I(fe));We(le,{style:"width: 100%;",onchange:Ve=>{const Fe=Ve.target.value;w(D,{title:Fe})},get value(){return n().title}}),A(fe);var Ne=V(fe,2),ke=V(I(Ne));Ie(ke,{rows:3,style:"width: 100%;",onchange:Ve=>{const Fe=Ve.target.value;w(D,{description:Fe})},get value(){return n().description}}),A(Ne);var K=V(Ne,2);{var st=Ve=>{var Fe=dy(),pe=V(I(Fe));Ie(pe,{rows:2,style:"width: 100%;",onchange:Be=>{const ut=Be.target.value;w(D,{condition:ut})},get value(){return n().condition}}),A(Fe),H(Ve,Fe)};ae(K,Ve=>{p()&&Ve(st)})}var De=V(K,2),qe=V(I(De),2);rn(qe),qe.__change=[fy,w,D],A(De);var Me=V(De,2),at=V(I(Me),2);rn(at),at.__change=[py,w,D],A(Me);var lt=V(Me,2);{var At=Ve=>{var Fe=gy(),pe=oe(Fe),Be=V(I(pe));{let kt=z(()=>n().loopIntervalMs||"1000");Ie(Be,{rows:1,style:"width: 100%;",onchange:gn=>{const Bn=gn.target.value;w(D,{loopIntervalMs:Bn})},get value(){return c(kt)}})}A(pe);var ut=V(pe,2),et=V(I(ut));{let kt=z(()=>n().maxLoopCount||"0");Ie(et,{rows:1,style:"width: 100%;",onchange:gn=>{const Bn=gn.target.value;w(D,{maxLoopCount:Bn})},get value(){return c(kt)}})}A(ut);var zt=V(ut,2),je=V(I(zt));Ie(je,{rows:2,style:"width: 100%;",onchange:kt=>{const gn=kt.target.value;w(D,{loopBreakCondition:gn})},get value(){return n().loopBreakCondition}}),A(zt),H(Ve,Fe)};ae(lt,Ve=>{n().loopEnable&&Ve(At)})}A(de),xe(()=>{hs(qe,!!n().async),hs(at,!!n().loopEnable)}),H(te,de)},children:(te,de)=>{Pe(te,{class:"tf-node-toolbar-item",children:(fe,le)=>{var Ne=vy();H(fe,Ne)},$$slots:{default:!0}})},$$slots:{floating:!0,default:!0}})};ae(we,Q=>{d()&&Q(re)})}A(ne),H(Y,ne)},$$slots:{default:!0}})};ae(U,M=>{(a()||l()||u())&&M(O)})}var $=V(U,2),C=V(I($),2),_=I(C);dd(_,{get items(){return c(k)},get activeKeys(){return v},onChange:(M,Y)=>{w(r(),{expand:Y?.includes("key")}),h()?.(Y?.includes("key")?"key":"")}}),A(C),A($);var P=V($,2);{var N=M=>{An(M,{type:"target",get position(){return ve.Left},style:" left: -12px;top: 20px"})};ae(P,M=>{g()&&M(N)})}var T=V(P,2);{var Z=M=>{An(M,{type:"source",get position(){return ve.Right},style:"right: -12px;top: 20px"})};ae(T,M=>{f()&&M(Z)})}var X=V(T,2);return Xe(X,()=>i()??tt),H(e,B),ce(q)}wn(["change"]),se(Ot,{data:{},id:{},icon:{},handle:{},children:{},allowExecute:{},allowCopy:{},allowDelete:{},allowSetting:{},allowSettingOfCondition:{},showSourceHandle:{},showTargetHandle:{},onCollapse:{}},[],[],!0);var by=J('<div class="input-more-item svelte-2f9bnc">数据选项： <!></div>'),xy=J('<div class="input-more-setting svelte-2f9bnc"><div class="input-more-item svelte-2f9bnc">数据内容： <!></div> <div class="input-more-item svelte-2f9bnc">输入方式： <!></div> <!> <div class="input-more-item svelte-2f9bnc">数据标题： <!></div> <div class="input-more-item svelte-2f9bnc">数据描述： <!></div> <div class="input-more-item svelte-2f9bnc">占位符： <!></div> <div class="input-more-item svelte-2f9bnc"><!></div></div>'),_y=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M4.5 10.5C3.675 10.5 3 11.175 3 12C3 12.825 3.675 13.5 4.5 13.5C5.325 13.5 6 12.825 6 12C6 11.175 5.325 10.5 4.5 10.5ZM19.5 10.5C18.675 10.5 18 11.175 18 12C18 12.825 18.675 13.5 19.5 13.5C20.325 13.5 21 12.825 21 12C21 11.175 20.325 10.5 19.5 10.5ZM12 10.5C11.175 10.5 10.5 11.175 10.5 12C10.5 12.825 11.175 13.5 12 13.5C12.825 13.5 13.5 12.825 13.5 12C13.5 11.175 12.825 10.5 12 10.5Z"></path></svg>'),ky=J('<div class="input-item svelte-2f9bnc"><!></div> <div class="input-item svelte-2f9bnc"><!></div> <div class="input-item svelte-2f9bnc"><!></div>',1);const $y={hash:"svelte-2f9bnc",code:".input-item.svelte-2f9bnc {display:flex;align-items:center;}.input-more-setting.svelte-2f9bnc {display:flex;flex-direction:column;gap:10px;padding:10px;background:#fff;border:1px solid #ddd;border-radius:5px;width:200px;box-shadow:0 0 10px 2px rgba(0, 0, 0, 0.1);}.input-more-setting.svelte-2f9bnc .input-more-item:where(.svelte-2f9bnc) {display:flex;flex-direction:column;gap:3px;font-size:12px;color:#666;}"};function Ld(e,t){ue(t,!0),He(e,$y);const n=y(t,"parameter",7),r=y(t,"index",7);let o=Ye(),i=En(o),s=z(()=>({...n(),...i?.current?.data?.parameters[r()]}));const{updateNodeData:a}=ot(),l=(q,B)=>{a(o,U=>{let O=U.data.parameters;return O[r()][q]=B,{parameters:O}})},u=(q,B)=>{const U=B.target.value;l(q,U)},d=q=>{const B=q.target.value;l("name",B)},p=q=>{const B=q.target.checked;l("required",B)},f=q=>{const B=q.value;l("formType",B)},g=q=>{const B=q.value;l("contentType",B)};let h;const v=()=>{a(o,q=>{let B=q.data.parameters;return B.splice(r(),1),{parameters:[...B]}}),h?.hide()};var w={get parameter(){return n()},set parameter(q){n(q),m()},get index(){return r()},set index(q){r(q),m()}},b=ky(),k=oe(b),x=I(k);We(x,{style:"width: 100%;",get value(){return c(s).name},placeholder:"请输入参数名称",oninput:d}),A(k);var S=V(k,2),E=I(S);ld(E,{get checked(){return c(s).required},onchange:p}),A(S);var L=V(S,2),D=I(L);return Et(lr(D,{placement:"bottom",floating:q=>{var B=xy(),U=I(B),O=V(I(U));{let R=z(()=>c(s).contentType?[c(s).contentType]:[]);it(O,{get items(){return ta},style:"width: 100%",defaultValue:["text"],get value(){return c(R)},onSelect:g})}A(U);var $=V(U,2),C=V(I($));{let R=z(()=>c(s).formType?[c(s).formType]:[]);it(C,{get items(){return Tm},style:"width: 100%",defaultValue:["input"],get value(){return c(R)},onSelect:f})}A($);var _=V($,2);{var P=R=>{var W=by(),F=V(I(W));{let ie=z(()=>c(s).enums?.join(`
`));Ie(F,{rows:3,style:"width: 100%;",onchange:G=>{l("enums",G.target?.value.trim().split(`
`))},get value(){return c(ie)},placeholder:"一行一个选项"})}A(W),H(R,W)};ae(_,R=>{(c(s).formType==="radio"||c(s).formType==="checkbox"||c(s).formType==="select")&&R(P)})}var N=V(_,2),T=V(I(N));Ie(T,{rows:1,style:"width: 100%;",onchange:R=>{u("formLabel",R)},get value(){return c(s).formLabel}}),A(N);var Z=V(N,2),X=V(I(Z));Ie(X,{rows:2,style:"width: 100%;",onchange:R=>{u("formDescription",R)},get value(){return c(s).formDescription}}),A(Z);var M=V(Z,2),Y=V(I(M));Ie(Y,{rows:2,style:"width: 100%;",onchange:R=>{u("formPlaceholder",R)},get value(){return c(s).formPlaceholder}}),A(M);var ee=V(M,2),ne=I(ee);Pe(ne,{onclick:v,children:(R,W)=>{he();var F=_e("删除");H(R,F)},$$slots:{default:!0}}),A(ee),A(B),H(q,B)},children:(q,B)=>{Pe(q,{class:"input-btn-more",children:(U,O)=>{var $=_y();H(U,$)},$$slots:{default:!0}})},$$slots:{floating:!0,default:!0}}),q=>h=q,()=>h),A(L),H(e,b),ce(w)}se(Ld,{parameter:{},index:{}},[],[],!0);var Cy=J('<div class="input-header svelte-3n0wca">参数名称</div> <div class="input-header svelte-3n0wca">必填</div> <div class="input-header svelte-3n0wca"></div>',1),Sy=J('<div class="none-params svelte-3n0wca">无输入参数</div>'),Ey=J('<div class="input-container svelte-3n0wca"><!> <!></div>');const Ny={hash:"svelte-3n0wca",code:`.input-container.svelte-3n0wca {display:grid;grid-template-columns:80% 10% 10%;row-gap:5px;column-gap:3px;}.input-container.svelte-3n0wca .none-params:where(.svelte-3n0wca) {font-size:12px;background:#f8f8f8;height:40px;display:flex;justify-content:center;align-items:center;border-radius:5px;width:calc(100% - 5px);grid-column:1 / -1;
  /* 从第一列开始到最后一列结束 */}.input-container.svelte-3n0wca .input-header:where(.svelte-3n0wca) {font-size:12px;color:#666;}`};function Dd(e,t){ue(t,!0),He(e,Ny);let n=Ye(),r=En(n),o=z(()=>[...r?.current?.data?.parameters||[]]);var i=Ey(),s=I(i);{var a=u=>{var d=Cy();he(4),H(u,d)};ae(s,u=>{c(o).length!==0&&u(a)})}var l=V(s,2);ft(l,19,()=>c(o),u=>u.id,(u,d,p)=>{Ld(u,{get parameter(){return c(d)},get index(){return c(p)}})},u=>{var d=Sy();H(u,d)}),A(i),H(e,i),ce()}se(Dd,{},[],[],!0);const No=e=>(!e||e.length==0||e.forEach(t=>{t.id||(t.id=ur()),No(t.children)}),e),Gt=()=>{const{updateNodeData:e}=ot();return{addParameter:(t,n="parameters",r)=>{Array.isArray(r)?r.forEach(s=>No(s?.children)):No(r?.children);function o(s){return{name:"",dataType:"String",refType:"ref",...s,id:ur()}}const i=[];Array.isArray(r)?i.push(...r.map(o)):i.push(o(r)),e(t,s=>{let a=s.data[n];return a?a.push(...i):a=[...i],{[n]:[...a]}})}}};var Py=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22ZM12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4C7.58172 4 4 7.58172 4 12C4 16.4183 7.58172 20 12 20ZM12 15C10.3431 15 9 13.6569 9 12C9 10.3431 10.3431 9 12 9C13.6569 9 15 10.3431 15 12C15 13.6569 13.6569 15 12 15Z"></path></svg>'),zy=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),Ly=J('<div class="heading svelte-r5g35l"><!> <!></div> <!>',1);const Dy={hash:"svelte-r5g35l",code:".heading.svelte-r5g35l {display:flex;margin-bottom:10px;}.input-btn-more {border:1px solid transparent;padding:3px;}.input-btn-more:hover {background:#eee;border:1px solid transparent;}"};function Hd(e,t){ue(t,!0),He(e,Dy);const n=y(t,"data",7),r=Te(t,["$$slots","$$events","$$legacy","$$host","data"]),o=Ye(),{addParameter:i}=Gt();var s={get data(){return n()},set data(a){n(a),m()}};return Ot(e,Ae(()=>r,{get data(){return n()},allowExecute:!1,showTargetHandle:!1,allowSettingOfCondition:!1,icon:a=>{var l=Py();H(a,l)},children:(a,l)=>{var u=Ly(),d=oe(u),p=I(d);Le(p,{level:3,children:(h,v)=>{he();var w=_e("输入参数");H(h,w)},$$slots:{default:!0}});var f=V(p,2);Pe(f,{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{i(o,"parameters",{refType:"input"})},children:(h,v)=>{var w=zy();H(h,w)},$$slots:{default:!0}}),A(d);var g=V(d,2);Dd(g,{}),H(a,u)},$$slots:{icon:!0,default:!0}})),ce(s)}se(Hd,{data:{}},[],[],!0);const Md=(e,t,n)=>{for(const r of n)r.target===t&&r.source&&(e.push(r.source),Md(e,r.source,n))},Td=(e,t,n)=>!e||e.length===0?[]:e.map(r=>({label:r.name+(n?` (Array<${r.dataType||"String"}>)`:` (${r.dataType||"String"})`),value:t+"."+r.name,children:Td(r.children,t+"."+r.name,n)})),Vd=(e,t,n)=>{if(e.type==="startNode"){const r=e.data.parameters,o=[];if(r)for(const i of r)o.push({label:i.name+(t?` (Array<${i.dataType||"String"}>)`:` (${i.dataType||"String"})`),value:e.id+"."+i.name});return{label:e.data.title,value:e.id,children:o}}else{if(e.type==="loopNode"&&n.parentId)return{label:e.data.title,value:e.id,children:[{label:"loopItem",value:e.id+".loopItem"},{label:"index (Number)",value:e.id+".index"}]};{const r=e.data.outputDefs;if(r)return{label:e.data.title,value:e.id,children:Td(r,e.id,t)}}}},Od=(e=!1)=>{const t=Ye(),n=En(t),r=z(jt),o=z(()=>c(r).nodes),i=z(()=>c(r).edges),s=z(()=>c(r).nodeLookup);let a=z(()=>{const l=[];if(!n.current)return[];const u=c(s).get(t);if(e)for(const d of c(o)){const p=d.parentId===n.current.id;if(p){const f=Vd(d,p,u);f&&l.push(f)}}else{const d=[];Md(d,t,c(i));for(const p of c(o))if(d.includes(p.id)){const f=p.parentId===n.current.id,g=Vd(p,f,u);g&&l.push(g)}}return l});return{get current(){return c(a)}}};var Hy=J('<div class="input-more-item svelte-laou7w">数据内容： <!></div>'),My=J('<div class="input-more-setting svelte-laou7w"><div class="input-more-item svelte-laou7w">数据来源： <!></div> <!> <div class="input-more-item svelte-laou7w">默认值： <!></div> <div class="input-more-item svelte-laou7w">参数描述： <!></div> <div class="input-more-item svelte-laou7w"><!></div></div>'),Ty=J('<div class="input-item svelte-laou7w"><!></div> <div class="input-item svelte-laou7w"><!></div> <div class="input-item svelte-laou7w"><!></div>',1);const Vy={hash:"svelte-laou7w",code:".input-item.svelte-laou7w {display:flex;align-items:center;}.input-more-setting.svelte-laou7w {display:flex;flex-direction:column;gap:10px;padding:10px;background:#fff;border:1px solid #ddd;border-radius:5px;width:200px;box-shadow:0 0 10px 2px rgba(0, 0, 0, 0.1);}.input-more-setting.svelte-laou7w .input-more-item:where(.svelte-laou7w) {display:flex;flex-direction:column;gap:3px;font-size:12px;color:#666;}"};function Ad(e,t){ue(t,!0),He(e,Vy),Tn(()=>{c(u).refType||h({value:"ref"})});const n=y(t,"parameter",7),r=y(t,"index",7),o=y(t,"dataKeyName",7),i=y(t,"useChildrenOnly",7),s=y(t,"showContentType",7,!1);let a=Ye(),l=En(a),u=z(()=>({...n(),...l?.current?.data?.[o()][r()]}));const{updateNodeData:d}=ot(),p=(C,_)=>{d(a,P=>{let N=P.data?.[o()];return N[r()]={...N[r()],[C]:_},{[o()]:N}})},f=(C,_)=>{const P=_.target.value;p(C,P)},g=C=>{const _=C.value;p("ref",_)},h=C=>{const _=C.value;p("refType",_)},v=C=>{const _=C.value;p("contentType",_)};let w;const b=()=>{d(a,C=>{let _=C.data?.[o()];return _.splice(r(),1),{[o()]:[..._]}}),w?.hide()};let k=Od(i());var x={get parameter(){return n()},set parameter(C){n(C),m()},get index(){return r()},set index(C){r(C),m()},get dataKeyName(){return o()},set dataKeyName(C){o(C),m()},get useChildrenOnly(){return i()},set useChildrenOnly(C){i(C),m()},get showContentType(){return s()},set showContentType(C=!1){s(C),m()}},S=Ty(),E=oe(S),L=I(E);{let C=z(()=>c(u).nameDisabled===!0);We(L,{style:"width: 100%;",get value(){return c(u).name},placeholder:"请输入参数名称",get disabled(){return c(C)},oninput:_=>f("name",_)})}A(E);var D=V(E,2),q=I(D);{var B=C=>{We(C,{get value(){return c(u).value},placeholder:"请输入参数值",oninput:_=>f("value",_)})},U=C=>{var _=Ce(),P=oe(_);{var N=T=>{{let Z=z(()=>[c(u).ref]);it(T,{get items(){return k.current},style:"width: 100%",defaultValue:["ref"],get value(){return c(Z)},expandAll:!0,onSelect:g})}};ae(P,T=>{c(u).refType!=="input"&&T(N)},!0)}H(C,_)};ae(q,C=>{c(u).refType==="fixed"?C(B):C(U,!1)})}A(D);var O=V(D,2),$=I(O);return Et(lr($,{placement:"bottom",floating:C=>{var _=My(),P=I(_),N=V(I(P));{let W=z(()=>c(u).refType?[c(u).refType]:[]);it(N,{get items(){return Mm},style:"width: 100%",defaultValue:["ref"],get value(){return c(W)},onSelect:h})}A(P);var T=V(P,2);{var Z=W=>{var F=Hy(),ie=V(I(F));{let G=z(()=>c(u).contentType?[c(u).contentType]:[]);it(ie,{get items(){return ta},style:"width: 100%",defaultValue:["text"],get value(){return c(G)},onSelect:v})}A(F),H(W,F)};ae(T,W=>{s()&&W(Z)})}var X=V(T,2),M=V(I(X));Ie(M,{rows:1,style:"width: 100%;",onchange:W=>{f("defaultValue",W)},get value(){return c(u).defaultValue}}),A(X);var Y=V(X,2),ee=V(I(Y));Ie(ee,{rows:3,style:"width: 100%;",onchange:W=>{f("description",W)},get value(){return c(u).description}}),A(Y);var ne=V(Y,2),R=I(ne);Pe(R,{onclick:b,children:(W,F)=>{he();var ie=_e("删除");H(W,ie)},$$slots:{default:!0}}),A(ne),A(_),H(C,_)},children:(C,_)=>{Eo(C,{})},$$slots:{floating:!0,default:!0}}),C=>w=C,()=>w),A(O),H(e,S),ce(x)}se(Ad,{parameter:{},index:{},dataKeyName:{},useChildrenOnly:{},showContentType:{}},[],[],!0);var Oy=J('<div class="input-header svelte-1sm1mgi">参数名称</div> <div class="input-header svelte-1sm1mgi">参数值</div> <div class="input-header svelte-1sm1mgi"></div>',1),Ay=J('<div class="none-params svelte-1sm1mgi"> </div>'),Iy=J('<div class="input-container svelte-1sm1mgi"><!> <!></div>');const qy={hash:"svelte-1sm1mgi",code:`.input-container.svelte-1sm1mgi {display:grid;grid-template-columns:40% 50% 10%;row-gap:5px;column-gap:3px;}.input-container.svelte-1sm1mgi .none-params:where(.svelte-1sm1mgi) {font-size:12px;background:#f8f8f8;height:40px;display:flex;justify-content:center;align-items:center;border-radius:5px;width:calc(100% - 5px);grid-column:1 / -1;
  /* 从第一列开始到最后一列结束 */}.input-container.svelte-1sm1mgi .input-header:where(.svelte-1sm1mgi) {font-size:12px;color:#666;}`};function _t(e,t){ue(t,!0),He(e,qy);const n=y(t,"noneParameterText",7,"无输入参数"),r=y(t,"dataKeyName",7,"parameters"),o=y(t,"useChildrenOnly",7),i=y(t,"showContentType",7,!1);let s=Ye(),a=En(s),l=z(()=>[...a?.current?.data?.[r()]||[]]);var u={get noneParameterText(){return n()},set noneParameterText(h="无输入参数"){n(h),m()},get dataKeyName(){return r()},set dataKeyName(h="parameters"){r(h),m()},get useChildrenOnly(){return o()},set useChildrenOnly(h){o(h),m()},get showContentType(){return i()},set showContentType(h=!1){i(h),m()}},d=Iy(),p=I(d);{var f=h=>{var v=Oy();he(4),H(h,v)};ae(p,h=>{c(l).length!==0&&h(f)})}var g=V(p,2);return ft(g,19,()=>c(l),h=>h.id,(h,v,w)=>{Ad(h,{get parameter(){return c(v)},get index(){return c(w)},get dataKeyName(){return r()},get useChildrenOnly(){return o()},get showContentType(){return i()}})},h=>{var v=Ay(),w=I(v,!0);A(v),xe(()=>Oe(w,n())),H(h,v)}),A(d),H(e,d),ce(u)}se(_t,{noneParameterText:{},dataKeyName:{},useChildrenOnly:{},showContentType:{}},[],[],!0);var Zy=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M6 5.1438V16.0002H18.3391L6 5.1438ZM4 2.932C4 2.07155 5.01456 1.61285 5.66056 2.18123L21.6501 16.2494C22.3423 16.8584 21.9116 18.0002 20.9896 18.0002H6V22H4V2.932Z"></path></svg>'),Ry=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),By=J('<div class="heading svelte-11h445j"><!> <!></div> <!>',1);const Ky={hash:"svelte-11h445j",code:".heading.svelte-11h445j {display:flex;margin-bottom:10px;}"};function Id(e,t){ue(t,!0),He(e,Ky);const n=y(t,"data",7),r=Te(t,["$$slots","$$events","$$legacy","$$host","data"]),o=Ye(),{addParameter:i}=Gt();var s={get data(){return n()},set data(a){n(a),m()}};return Ot(e,Ae({get data(){return n()}},()=>r,{allowExecute:!1,showSourceHandle:!1,icon:a=>{var l=Zy();H(a,l)},children:(a,l)=>{var u=By(),d=oe(u),p=I(d);Le(p,{level:3,children:(h,v)=>{he();var w=_e("输出参数");H(h,w)},$$slots:{default:!0}});var f=V(p,2);Pe(f,{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{i(o,"outputDefs")},children:(h,v)=>{var w=Ry();H(h,w)},$$slots:{default:!0}}),A(d);var g=V(d,2);_t(g,{noneParameterText:"无输出参数",dataKeyName:"outputDefs",showContentType:!0}),H(a,u)},$$slots:{icon:!0,default:!0}})),ce(s)}se(Id,{data:{}},[],[],!0);const ca=e=>JSON.parse(JSON.stringify(e));var Xy=ge('<svg style="transform: scaleY(-1)" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M13 8V16C13 17.6569 11.6569 19 10 19H7.82929C7.41746 20.1652 6.30622 21 5 21C3.34315 21 2 19.6569 2 18C2 16.3431 3.34315 15 5 15C6.30622 15 7.41746 15.8348 7.82929 17H10C10.5523 17 11 16.5523 11 16V8C11 6.34315 12.3431 5 14 5H17V2L22 6L17 10V7H14C13.4477 7 13 7.44772 13 8ZM5 19C5.55228 19 6 18.5523 6 18C6 17.4477 5.55228 17 5 17C4.44772 17 4 17.4477 4 18C4 18.5523 4.44772 19 5 19Z"></path></svg>'),Yy=J('<div class="input-more-item svelte-1cfeest"><!></div>'),jy=J('<div class="input-more-setting svelte-1cfeest"><div class="input-more-item svelte-1cfeest">默认值： <!></div> <div class="input-more-item svelte-1cfeest">参数描述： <!></div> <!></div>'),Wy=J('<div class="input-item svelte-1cfeest"><!> <!></div> <div class="input-item svelte-1cfeest"><!> <!></div> <div class="input-item svelte-1cfeest"><!></div>',1);const Fy={hash:"svelte-1cfeest",code:".input-item.svelte-1cfeest {display:flex;align-items:center;gap:2px;}.input-more-setting.svelte-1cfeest {display:flex;flex-direction:column;gap:10px;padding:10px;background:#fff;border:1px solid #ddd;border-radius:5px;width:200px;box-shadow:0 0 10px 2px rgba(0, 0, 0, 0.1);}.input-more-setting.svelte-1cfeest .input-more-item:where(.svelte-1cfeest) {display:flex;flex-direction:column;gap:3px;font-size:12px;color:#666;}"};function qd(e,t){ue(t,!0),He(e,Fy);const n=y(t,"parameter",7),r=y(t,"position",7),o=y(t,"dataKeyName",7),i=y(t,"placeholder",7,"请输入参数值");let s=Ye(),a=En(s),l=z(()=>{let $=a?.current?.data?.[o()],C;if($&&r().length>0){let _=$;for(let P=0;P<r().length;P++){const N=r()[P];P==r().length-1?C=_[N]:_=_[N].children}}return{...n(),...C}});const{updateNodeData:u}=ot(),d=($,C)=>{u(s,_=>{const P=_.data?.[o()];if(P&&r().length>0){let N=P;for(let T=0;T<r().length;T++){const Z=r()[T];T==r().length-1?N[Z]={...N[Z],[$]:C}:N=N[Z].children}}return{[o()]:[...ca(P)]}})},p=($,C)=>{const _=C.target.value;d($,_)},f=$=>{const C=$.value;d("dataType",C)};let g;const h=()=>{u(s,$=>{let C=$.data?.[o()];if(C&&r().length>0){let _=C;for(let P=0;P<r().length;P++){const N=r()[P];P==r().length-1?_.splice(N,1):_=_[N].children}}return{[o()]:[...ca(C)]}}),g?.hide()},v=()=>{u(s,$=>{let C=$.data?.[o()];if(C&&r().length>0){let _=C;for(let P=0;P<r().length;P++){const N=r()[P];P==r().length-1?_[N].children?_[N].children.push({id:ur(),name:"newParam",dataType:"String"}):_[N].children=[{id:ur(),name:"newParam",dataType:"String"}]:_=_[N].children}}return{[o()]:[...ca(C)]}})};var w={get parameter(){return n()},set parameter($){n($),m()},get position(){return r()},set position($){r($),m()},get dataKeyName(){return o()},set dataKeyName($){o($),m()},get placeholder(){return i()},set placeholder($="请输入参数值"){i($),m()}},b=Wy(),k=oe(b),x=I(k);{var S=$=>{var C=Ce(),_=oe(C);ft(_,17,r,kr,(P,N)=>{he();var T=_e(" ");H(P,T)}),H($,C)};ae(x,$=>{r().length>1&&$(S)})}var E=V(x,2);{let $=z(()=>c(l).nameDisabled===!0);We(E,{style:"width: 100%;",get value(){return c(l).name},get placeholder(){return i()},oninput:C=>{p("name",C)},get disabled(){return c($)}})}A(k);var L=V(k,2),D=I(L);{let $=z(()=>c(l).dataType?[c(l).dataType]:[]),C=z(()=>c(l).dataTypeDisabled===!0);it(D,{get items(){return Hm},style:"width: 100%",defaultValue:["String"],get value(){return c($)},get disabled(){return c(C)},onSelect:f})}var q=V(D,2);{var B=$=>{Pe($,{class:"input-btn-more",style:"margin-left: auto",onclick:v,children:(C,_)=>{var P=Xy();H(C,P)},$$slots:{default:!0}})};ae(q,$=>{(c(l).dataType==="Object"||c(l).dataType==="Array")&&c(l).addChildDisabled!==!0&&$(B)})}A(L);var U=V(L,2),O=I(U);return Et(lr(O,{placement:"bottom",floating:$=>{var C=jy(),_=I(C),P=V(I(_));{let M=z(()=>c(l).defaultValue||"");Ie(P,{rows:1,style:"width: 100%;",get value(){return c(M)},onchange:Y=>{p("defaultValue",Y)}})}A(_);var N=V(_,2),T=V(I(N));{let M=z(()=>c(l).description||"");Ie(T,{rows:3,style:"width: 100%;",get value(){return c(M)},onchange:Y=>{p("description",Y)}})}A(N);var Z=V(N,2);{var X=M=>{var Y=Yy(),ee=I(Y);Pe(ee,{onclick:h,children:(ne,R)=>{he();var W=_e("删除");H(ne,W)},$$slots:{default:!0}}),A(Y),H(M,Y)};ae(Z,M=>{c(l).deleteDisabled!==!0&&M(X)})}A(C),H($,C)},children:($,C)=>{Eo($,{})},$$slots:{floating:!0,default:!0}}),$=>g=$,()=>g),A(U),H(e,b),ce(w)}se(qd,{parameter:{},position:{},dataKeyName:{},placeholder:{}},[],[],!0);var Gy=J("<!> <!>",1),Uy=J('<div class="none-params svelte-1sm1mgi"> </div>'),Jy=J('<div class="input-header svelte-1sm1mgi">参数名称</div> <div class="input-header svelte-1sm1mgi">参数类型</div> <div class="input-header svelte-1sm1mgi"></div>',1),Qy=J('<div class="input-container svelte-1sm1mgi"><!> <!></div>');const ew={hash:"svelte-1sm1mgi",code:`.input-container.svelte-1sm1mgi {display:grid;grid-template-columns:40% 50% 10%;row-gap:5px;column-gap:3px;}.input-container.svelte-1sm1mgi .none-params:where(.svelte-1sm1mgi) {font-size:12px;background:#f8f8f8;height:40px;display:flex;justify-content:center;align-items:center;border-radius:5px;width:calc(100% - 5px);grid-column:1 / -1;
  /* 从第一列开始到最后一列结束 */}.input-container.svelte-1sm1mgi .input-header:where(.svelte-1sm1mgi) {font-size:12px;color:#666;}`};function Pn(e,t){ue(t,!0),He(e,ew);const n=(h,v=tt,w=tt)=>{var b=Ce(),k=oe(b);ft(k,19,v,x=>`${x.id}_${x.children?x.children.length:0}`,(x,S,E)=>{var L=Gy(),D=oe(L);{let U=z(()=>[...w(),c(E)]);qd(D,{get parameter(){return c(S)},get position(){return c(U)},get dataKeyName(){return o()},get placeholder(){return i()}})}var q=V(D,2);{var B=U=>{{let O=z(()=>[...w(),c(E)]);n(U,()=>c(S).children,()=>c(O))}};ae(q,U=>{c(S).children&&U(B)})}H(x,L)},x=>{var S=Ce(),E=oe(S);{var L=D=>{var q=Uy(),B=I(q,!0);A(q),xe(()=>Oe(B,r())),H(D,q)};ae(E,D=>{w().length===0&&D(L)})}H(x,S)}),H(h,b)},r=y(t,"noneParameterText",7,"无输出参数"),o=y(t,"dataKeyName",7,"outputDefs"),i=y(t,"placeholder",7,"请输入参数名称");let s=Ye(),a=En(s),l=z(()=>[...a?.current?.data?.[o()]||[]]);var u={get noneParameterText(){return r()},set noneParameterText(h="无输出参数"){r(h),m()},get dataKeyName(){return o()},set dataKeyName(h="outputDefs"){o(h),m()},get placeholder(){return i()},set placeholder(h="请输入参数名称"){i(h),m()}},d=Qy(),p=I(d);{var f=h=>{var v=Jy();he(4),H(h,v)};ae(p,h=>{c(l).length!==0&&h(f)})}var g=V(p,2);return n(g,()=>c(l)||[],()=>[]),A(d),H(e,d),ce(u)}se(Pn,{noneParameterText:{},dataKeyName:{},placeholder:{}},[],[],!0);var tw=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M20.7134 7.12811L20.4668 7.69379C20.2864 8.10792 19.7136 8.10792 19.5331 7.69379L19.2866 7.12811C18.8471 6.11947 18.0555 5.31641 17.0677 4.87708L16.308 4.53922C15.8973 4.35653 15.8973 3.75881 16.308 3.57612L17.0252 3.25714C18.0384 2.80651 18.8442 1.97373 19.2761 0.930828L19.5293 0.319534C19.7058 -0.106511 20.2942 -0.106511 20.4706 0.319534L20.7238 0.930828C21.1558 1.97373 21.9616 2.80651 22.9748 3.25714L23.6919 3.57612C24.1027 3.75881 24.1027 4.35653 23.6919 4.53922L22.9323 4.87708C21.9445 5.31641 21.1529 6.11947 20.7134 7.12811ZM9 2C13.0675 2 16.426 5.03562 16.9337 8.96494L19.1842 12.5037C19.3324 12.7367 19.3025 13.0847 18.9593 13.2317L17 14.071V17C17 18.1046 16.1046 19 15 19H13.001L13 22H4L4.00025 18.3061C4.00033 17.1252 3.56351 16.0087 2.7555 15.0011C1.65707 13.6313 1 11.8924 1 10C1 5.58172 4.58172 2 9 2ZM9 4C5.68629 4 3 6.68629 3 10C3 11.3849 3.46818 12.6929 4.31578 13.7499C5.40965 15.114 6.00036 16.6672 6.00025 18.3063L6.00013 20H11.0007L11.0017 17H15V12.7519L16.5497 12.0881L15.0072 9.66262L14.9501 9.22118C14.5665 6.25141 12.0243 4 9 4ZM19.4893 16.9929L21.1535 18.1024C22.32 16.3562 23 14.2576 23 12.0001C23 11.317 22.9378 10.6486 22.8186 10L20.8756 10.5C20.9574 10.9878 21 11.489 21 12.0001C21 13.8471 20.4436 15.5642 19.4893 16.9929Z"></path></svg>'),nw=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),rw=(e,t,n)=>t(n,{temperature:parseFloat(e.target.value)}),ow=(e,t,n)=>t(n,{topP:parseFloat(e.target.value)}),iw=(e,t,n)=>t(n,{topK:parseInt(e.target.value)}),sw=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),aw=J('<div class="heading svelte-q0cqsa"><!> <!></div> <!> <!> <div class="setting-title svelte-q0cqsa">模型</div> <div class="setting-item svelte-q0cqsa"><!> <!></div> <div class="setting-title svelte-q0cqsa">采样参数</div> <div class="setting-item svelte-q0cqsa"><div class="slider-container svelte-q0cqsa"><span class="svelte-q0cqsa"> </span> <input class="nodrag svelte-q0cqsa" type="range" min="0" max="1" step="0.1"/></div></div> <div class="setting-item svelte-q0cqsa"><div class="slider-container svelte-q0cqsa"><span class="svelte-q0cqsa"> </span> <input class="nodrag svelte-q0cqsa" type="range" min="0" max="1" step="0.1"/></div></div> <div class="setting-item svelte-q0cqsa"><div class="slider-container svelte-q0cqsa"><span class="svelte-q0cqsa"> </span> <input class="nodrag svelte-q0cqsa" type="range" min="0" max="100" step="1"/></div></div> <div class="setting-title svelte-q0cqsa">系统提示词</div> <div class="setting-item svelte-q0cqsa"><!></div> <div class="setting-title svelte-q0cqsa">用户提示词</div> <div class="setting-item svelte-q0cqsa"><!></div> <div class="heading svelte-q0cqsa"><!> <!> <!></div> <!>',1);const lw={hash:"svelte-q0cqsa",code:`.heading.svelte-q0cqsa {display:flex;align-items:center;margin-bottom:10px;}.setting-title.svelte-q0cqsa {font-size:12px;color:#999;margin-bottom:4px;margin-top:10px;}.setting-item.svelte-q0cqsa {display:flex;align-items:center;justify-content:space-between;margin-bottom:10px;gap:10px;}

    /* 新增样式 */.slider-container.svelte-q0cqsa {width:100%;display:flex;flex-direction:column;gap:4px;}.slider-container.svelte-q0cqsa span:where(.svelte-q0cqsa) {font-size:12px;color:#666;display:flex;justify-content:space-between;align-items:center;}input[type="range"].svelte-q0cqsa {width:100%;height:4px;background:#ddd;border-radius:2px;outline:none;-webkit-appearance:none;}input[type="range"].svelte-q0cqsa::-webkit-slider-thumb {-webkit-appearance:none;width:14px;height:14px;background:#007bff;border-radius:50%;cursor:pointer;}`};function Zd(e,t){ue(t,!0),He(e,lw);const n=y(t,"data",7),r=Te(t,["$$slots","$$events","$$legacy","$$host","data"]),o=Ye(),{addParameter:i}=Gt(),s=Rn();let a=Se(Mt([]));Tn(async()=>{const p=await s.provider?.llm?.();c(a).push(...p||[])});const{updateNodeData:l}=ot(),u=p=>{l(o,()=>({outType:p})),p==="text"?l(o,{outputDefs:[{name:"output",dataType:"String",dataTypeDisabled:!0,deleteDisabled:!0}]}):l(o,{outputDefs:[]})};Ke(()=>{n().outType||u("text")});var d={get data(){return n()},set data(p){n(p),m()}};return Ot(e,Ae({get data(){return n()}},()=>r,{icon:p=>{var f=tw();H(p,f)},children:(p,f)=>{var g=aw(),h=oe(g),v=I(h);Le(v,{level:3,children:(re,Q)=>{he();var te=_e("输入参数");H(re,te)},$$slots:{default:!0}});var w=V(v,2);Pe(w,{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{i(o)},children:(re,Q)=>{var te=nw();H(re,te)},$$slots:{default:!0}}),A(h);var b=V(h,2);_t(b,{});var k=V(b,2);Le(k,{level:3,mt:"10px",children:(re,Q)=>{he();var te=_e("模型设置");H(re,te)},$$slots:{default:!0}});var x=V(k,4),S=I(x);{let re=z(()=>n().llmId?[n().llmId]:[]);it(S,{get items(){return c(a)},style:"width: 100%",placeholder:"请选择模型",onSelect:Q=>{const te=Q.value;l(o,()=>({llmId:te}))},get value(){return c(re)}})}var E=V(S,2);Eo(E,{}),A(x);var L=V(x,4),D=I(L),q=I(D),B=I(q);A(q);var U=V(q,2);rn(U),U.__input=[rw,l,o],A(D),A(L);var O=V(L,2),$=I(O),C=I($),_=I(C);A(C);var P=V(C,2);rn(P),P.__input=[ow,l,o],A($),A(O);var N=V(O,2),T=I(N),Z=I(T),X=I(Z);A(Z);var M=V(Z,2);rn(M),M.__input=[iw,l,o],A(T),A(N);var Y=V(N,4),ee=I(Y);{let re=z(()=>n().systemPrompt||"");Ie(ee,{rows:5,placeholder:"请输入系统提示词",style:"width: 100%",get value(){return c(re)},oninput:Q=>{l(o,{systemPrompt:Q.target.value})}})}A(Y);var ne=V(Y,4),R=I(ne);{let re=z(()=>n().userPrompt||"");Ie(R,{rows:5,placeholder:"请输入用户提示词",style:"width: 100%",get value(){return c(re)},oninput:Q=>{l(o,{userPrompt:Q.target.value})}})}A(ne);var W=V(ne,2),F=I(W);Le(F,{level:3,children:(re,Q)=>{he();var te=_e("输出参数");H(re,te)},$$slots:{default:!0}});var ie=V(F,2);{let re=z(()=>n().outType?[n().outType]:[]);it(ie,{items:[{label:"文本",value:"text"},{label:"JSON",value:"json"}],style:"width: 100px;margin-left: auto",defaultValue:"text",onSelect:Q=>{u(Q.value)},get value(){return c(re)}})}var G=V(ie,2);{var me=re=>{Pe(re,{class:"input-btn-more",style:"margin-left: 10px",onclick:()=>{i(o,"outputDefs")},children:(Q,te)=>{var de=sw();H(Q,de)},$$slots:{default:!0}})};ae(G,re=>{n().outType==="json"&&re(me)})}A(W);var we=V(W,2);Pn(we,{}),xe(()=>{Oe(B,`Temperature: ${n().temperature??.5??""}`),Bo(U,n().temperature??.5),Oe(_,`Top P: ${n().topP??.9??""}`),Bo(P,n().topP??.9),Oe(X,`Top K: ${n().topK??50??""}`),Bo(M,n().topK??50)}),H(p,g)},$$slots:{icon:!0,default:!0}})),ce(d)}wn(["input"]),se(Zd,{data:{}},[],[],!0);var uw=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M23 12L15.9289 19.0711L14.5147 17.6569L20.1716 12L14.5147 6.34317L15.9289 4.92896L23 12ZM3.82843 12L9.48528 17.6569L8.07107 19.0711L1 12L8.07107 4.92896L9.48528 6.34317L3.82843 12Z"></path></svg>'),cw=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),dw=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),fw=J('<div class="heading svelte-15t2v24"><!> <!></div> <!> <!> <div class="setting-title svelte-15t2v24">执行引擎</div> <div class="setting-item svelte-15t2v24"><!></div> <div class="setting-title svelte-15t2v24">执行代码</div> <div class="setting-item svelte-15t2v24"><!></div> <div class="heading svelte-15t2v24"><!> <!></div> <!>',1);const pw={hash:"svelte-15t2v24",code:".heading.svelte-15t2v24 {display:flex;margin-bottom:10px;}.setting-title.svelte-15t2v24 {font-size:12px;color:#999;margin-bottom:4px;margin-top:10px;}.setting-item.svelte-15t2v24 {display:flex;align-items:center;justify-content:space-between;margin-bottom:10px;gap:10px;}"};function Rd(e,t){ue(t,!0),He(e,pw);const n=y(t,"data",7),r=Te(t,["$$slots","$$events","$$legacy","$$host","data"]);Tn(()=>{n().engine||s(o,()=>({engine:"qlexpress"}))});const o=Ye(),{addParameter:i}=Gt(),{updateNodeData:s}=ot(),a=[{label:"QLExpress",value:"qlexpress"},{label:"Groovy",value:"groovy"},{label:"JavaScript",value:"js"}];var l={get data(){return n()},set data(u){n(u),m()}};return Ot(e,Ae({get data(){return n()}},()=>r,{icon:u=>{var d=uw();H(u,d)},children:(u,d)=>{var p=fw(),f=oe(p),g=I(f);Le(g,{level:3,children:(B,U)=>{he();var O=_e("输入参数");H(B,O)},$$slots:{default:!0}});var h=V(g,2);Pe(h,{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{i(o)},children:(B,U)=>{var O=cw();H(B,O)},$$slots:{default:!0}}),A(f);var v=V(f,2);_t(v,{});var w=V(v,2);Le(w,{level:3,mt:"10px",children:(B,U)=>{he();var O=_e("代码");H(B,O)},$$slots:{default:!0}});var b=V(w,4),k=I(b);{let B=z(()=>n().engine?[n().engine]:["qlexpress"]);it(k,{get items(){return a},style:"width: 100%",placeholder:"请选择执行引擎",onSelect:U=>{const O=U.value;s(o,()=>({engine:O}))},get value(){return c(B)}})}A(b);var x=V(b,4),S=I(x);{let B=z(()=>n().code||"");Ie(S,{rows:10,placeholder:"请输入执行代码，注：输出内容需添加到_result中，如：_result.put(key, value)",style:"width: 100%",onchange:U=>{s(o,()=>({code:U.target.value}))},get value(){return c(B)}})}A(x);var E=V(x,2),L=I(E);Le(L,{level:3,mt:"10px",children:(B,U)=>{he();var O=_e("输出参数");H(B,O)},$$slots:{default:!0}});var D=V(L,2);Pe(D,{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{i(o,"outputDefs")},children:(B,U)=>{var O=dw();H(B,O)},$$slots:{default:!0}}),A(E);var q=V(E,2);Pn(q,{}),H(u,p)},$$slots:{icon:!0,default:!0}})),ce(l)}se(Rd,{data:{}},[],[],!0);var gw=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M2 4C2 3.44772 2.44772 3 3 3H21C21.5523 3 22 3.44772 22 4V20C22 20.5523 21.5523 21 21 21H3C2.44772 21 2 20.5523 2 20V4ZM4 5V19H20V5H4ZM7 8H17V11H15V10H13V14H14.5V16H9.5V14H11V10H9V11H7V8Z"></path></svg>'),hw=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),vw=J('<div class="heading svelte-15t2v24"><!> <!></div> <!> <!> <div class="setting-item svelte-15t2v24"><!></div> <div class="heading svelte-15t2v24"><!></div> <!>',1);const mw={hash:"svelte-15t2v24",code:".heading.svelte-15t2v24 {display:flex;margin-bottom:10px;}.setting-item.svelte-15t2v24 {display:flex;align-items:center;justify-content:space-between;margin-bottom:10px;gap:10px;}"};function Bd(e,t){ue(t,!0),He(e,mw);const n=y(t,"data",7),r=Te(t,["$$slots","$$events","$$legacy","$$host","data"]),o=Ye(),{addParameter:i}=Gt(),{updateNodeData:s}=ot();Ke(()=>{(!n().outputDefs||n().outputDefs.length===0)&&i(o,"outputDefs",{name:"output",dataType:"String",dataTypeDisabled:!0,deleteDisabled:!0})});var a={get data(){return n()},set data(l){n(l),m()}};return Ot(e,Ae({get data(){return n()}},()=>r,{icon:l=>{var u=gw();H(l,u)},children:(l,u)=>{var d=vw(),p=oe(d),f=I(p);Le(f,{level:3,children:(E,L)=>{he();var D=_e("输入参数");H(E,D)},$$slots:{default:!0}});var g=V(f,2);Pe(g,{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{i(o)},children:(E,L)=>{var D=hw();H(E,D)},$$slots:{default:!0}}),A(p);var h=V(p,2);_t(h,{});var v=V(h,2);Le(v,{level:3,mt:"10px",mb:"10px",children:(E,L)=>{he();var D=_e("模板内容");H(E,D)},$$slots:{default:!0}});var w=V(v,2),b=I(w);{let E=z(()=>n().template||"");Ie(b,{rows:10,placeholder:"请输入模板内容",style:"width: 100%",onchange:L=>{s(o,()=>({template:L.target.value}))},get value(){return c(E)}})}A(w);var k=V(w,2),x=I(k);Le(x,{level:3,mt:"10px",children:(E,L)=>{he();var D=_e("输出参数");H(E,D)},$$slots:{default:!0}}),A(k);var S=V(k,2);Pn(S,{}),H(l,d)},$$slots:{icon:!0,default:!0}})),ce(a)}se(Bd,{data:{}},[],[],!0);var yw=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M6.23509 6.45329C4.85101 7.89148 4 9.84636 4 12C4 16.4183 7.58172 20 12 20C13.0808 20 14.1116 19.7857 15.0521 19.3972C15.1671 18.6467 14.9148 17.9266 14.8116 17.6746C14.582 17.115 13.8241 16.1582 12.5589 14.8308C12.2212 14.4758 12.2429 14.2035 12.3636 13.3943L12.3775 13.3029C12.4595 12.7486 12.5971 12.4209 14.4622 12.1248C15.4097 11.9746 15.6589 12.3533 16.0043 12.8777C16.0425 12.9358 16.0807 12.9928 16.1198 13.0499C16.4479 13.5297 16.691 13.6394 17.0582 13.8064C17.2227 13.881 17.428 13.9751 17.7031 14.1314C18.3551 14.504 18.3551 14.9247 18.3551 15.8472V15.9518C18.3551 16.3434 18.3168 16.6872 18.2566 16.9859C19.3478 15.6185 20 13.8854 20 12C20 8.70089 18.003 5.8682 15.1519 4.64482C14.5987 5.01813 13.8398 5.54726 13.575 5.91C13.4396 6.09538 13.2482 7.04166 12.6257 7.11976C12.4626 7.14023 12.2438 7.12589 12.012 7.11097C11.3905 7.07058 10.5402 7.01606 10.268 7.75495C10.0952 8.2232 10.0648 9.49445 10.6239 10.1543C10.7134 10.2597 10.7307 10.4547 10.6699 10.6735C10.59 10.9608 10.4286 11.1356 10.3783 11.1717C10.2819 11.1163 10.0896 10.8931 9.95938 10.7412C9.64554 10.3765 9.25405 9.92233 8.74797 9.78176C8.56395 9.73083 8.36166 9.68867 8.16548 9.64736C7.6164 9.53227 6.99443 9.40134 6.84992 9.09302C6.74442 8.8672 6.74488 8.55621 6.74529 8.22764C6.74529 7.8112 6.74529 7.34029 6.54129 6.88256C6.46246 6.70541 6.35689 6.56446 6.23509 6.45329ZM12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22Z"></path></svg>'),ww=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),bw=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),xw=J('<!> <div class="radio-group svelte-1dxz5e"><label class="svelte-1dxz5e"><!>none</label> <label class="svelte-1dxz5e"><!>form-data</label> <label class="svelte-1dxz5e"><!>x-www-form-urlencoded</label> <label class="svelte-1dxz5e"><!>json</label> <label class="svelte-1dxz5e"><!>raw</label></div>',1),_w=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),kw=J('<div class="heading svelte-1dxz5e" style="padding-top: 10px"><!> <!></div> <!>',1),$w=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),Cw=J('<div class="heading svelte-1dxz5e" style="padding-top: 10px"><!> <!></div> <!>',1),Sw=J('<div style="width: 100%"><!></div>'),Ew=J('<div style="width: 100%"><!></div>'),Nw=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),Pw=J('<div class="heading svelte-1dxz5e"><!> <!></div> <!> <!> <div style="display: flex;gap: 2px;width: 100%;padding: 10px 0"><div><!></div> <div style="width: 100%"><!></div></div> <div class="heading svelte-1dxz5e"><!> <!></div> <!> <!> <!> <!> <!> <!> <div class="heading svelte-1dxz5e"><!> <!></div> <!>',1);const zw={hash:"svelte-1dxz5e",code:".heading.svelte-1dxz5e {display:flex;margin-bottom:10px;}.radio-group.svelte-1dxz5e {display:flex;margin:10px 0;flex-wrap:wrap;}.radio-group.svelte-1dxz5e label:where(.svelte-1dxz5e) {display:flex;font-size:14px;box-sizing:border-box;}"};function Kd(e,t){ue(t,!0),He(e,zw);const n=y(t,"data",7),r=Te(t,["$$slots","$$events","$$legacy","$$host","data"]);Tn(()=>{n().method||a(i,()=>({method:"get"})),n().outputDefs||s(i,"outputDefs",[{name:"headers",nameDisabled:!0,dataType:"Object",dataTypeDisabled:!0,deleteDisabled:!0},{name:"body",nameDisabled:!0,dataType:"String",deleteDisabled:!0},{name:"statusCode",nameDisabled:!0,dataType:"Number",dataTypeDisabled:!0,deleteDisabled:!0}])});const o=[{value:"get",label:"GET"},{value:"post",label:"POST"},{value:"put",label:"PUT"},{value:"delete",label:"DELETE"},{value:"head",label:"HEAD"},{value:"patch",label:"PATCH"}],i=Ye(),{addParameter:s}=Gt(),{updateNodeData:a}=ot();var l={get data(){return n()},set data(u){n(u),m()}};return Ot(e,Ae({get data(){return n()}},()=>r,{icon:u=>{var d=yw();H(u,d)},children:(u,d)=>{var p=Pw(),f=oe(p),g=I(f);Le(g,{level:3,children:(R,W)=>{he();var F=_e("输入参数");H(R,F)},$$slots:{default:!0}});var h=V(g,2);Pe(h,{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{s(i)},children:(R,W)=>{var F=ww();H(R,F)},$$slots:{default:!0}}),A(f);var v=V(f,2);_t(v,{});var w=V(v,2);Le(w,{level:3,mt:"10px",children:(R,W)=>{he();var F=_e("URL 地址");H(R,F)},$$slots:{default:!0}});var b=V(w,2),k=I(b),x=I(k);{let R=z(()=>n().method?[n().method]:["get"]);it(x,{get items(){return o},style:"width: 100%",placeholder:"请选择请求方式",onSelect:W=>{const F=W.value;a(i,()=>({method:F}))},get value(){return c(R)}})}A(k);var S=V(k,2),E=I(S);{let R=z(()=>n().url||"");We(E,{placeholder:"请输入url",style:"width: 100%",onchange:W=>{a(i,()=>({url:W.target.value}))},get value(){return c(R)}})}A(S),A(b);var L=V(b,2),D=I(L);Le(D,{level:3,children:(R,W)=>{he();var F=_e("Http 头信息");H(R,F)},$$slots:{default:!0}});var q=V(D,2);Pe(q,{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{s(i,"headers")},children:(R,W)=>{var F=bw();H(R,F)},$$slots:{default:!0}}),A(L);var B=V(L,2);_t(B,{dataKeyName:"headers"});var U=V(B,2);{var O=R=>{var W=xw(),F=oe(W);Le(F,{level:3,mt:"10px",children:(ke,K)=>{he();var st=_e("Body");H(ke,st)},$$slots:{default:!0}});var ie=V(F,2),G=I(ie),me=I(G);{let ke=z(()=>!n().bodyType||n().bodyType==="");We(me,{type:"radio",name:"bodyType",value:"",get checked(){return c(ke)},onchange:K=>{K.target?.checked&&a(i,{bodyType:""})}})}he(),A(G);var we=V(G,2),re=I(we);{let ke=z(()=>n().bodyType==="form-data");We(re,{type:"radio",name:"bodyType",value:"form-data",get checked(){return c(ke)},onchange:K=>{K.target?.checked&&a(i,{bodyType:"form-data"})}})}he(),A(we);var Q=V(we,2),te=I(Q);{let ke=z(()=>n().bodyType==="x-www-form-urlencoded");We(te,{type:"radio",name:"bodyType",value:"x-www-form-urlencoded",get checked(){return c(ke)},onchange:K=>{K.target?.checked&&a(i,{bodyType:"x-www-form-urlencoded"})}})}he(),A(Q);var de=V(Q,2),fe=I(de);{let ke=z(()=>n().bodyType==="json");We(fe,{type:"radio",name:"bodyType",value:"json",get checked(){return c(ke)},onchange:K=>{K.target?.checked&&a(i,{bodyType:"json"})}})}he(),A(de);var le=V(de,2),Ne=I(le);{let ke=z(()=>n().bodyType==="raw");We(Ne,{type:"radio",name:"bodyType",value:"raw",get checked(){return c(ke)},onchange:K=>{K.target?.checked&&a(i,{bodyType:"raw"})}})}he(),A(le),A(ie),H(R,W)};ae(U,R=>{(n().method==="post"||n().method==="put"||n().method==="delete"||n().method==="patch")&&R(O)})}var $=V(U,2);{var C=R=>{var W=kw(),F=oe(W),ie=I(F);Le(ie,{level:3,children:(we,re)=>{he();var Q=_e("参数");H(we,Q)},$$slots:{default:!0}});var G=V(ie,2);Pe(G,{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{s(i,"formData")},children:(we,re)=>{var Q=_w();H(we,Q)},$$slots:{default:!0}}),A(F);var me=V(F,2);_t(me,{dataKeyName:"formData"}),H(R,W)};ae($,R=>{n().bodyType==="form-data"&&R(C)})}var _=V($,2);{var P=R=>{var W=Cw(),F=oe(W),ie=I(F);Le(ie,{level:3,children:(we,re)=>{he();var Q=_e("Body 参数");H(we,Q)},$$slots:{default:!0}});var G=V(ie,2);Pe(G,{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{s(i,"formUrlencoded")},children:(we,re)=>{var Q=$w();H(we,Q)},$$slots:{default:!0}}),A(F);var me=V(F,2);_t(me,{dataKeyName:"formUrlencoded"}),H(R,W)};ae(_,R=>{n().bodyType==="x-www-form-urlencoded"&&R(P)})}var N=V(_,2);{var T=R=>{var W=Sw(),F=I(W);Ie(F,{rows:5,style:"width: 100%",placeholder:"请输入 json 信息",get value(){return n().bodyJson},oninput:ie=>{a(i,{bodyJson:ie.target.value})}}),A(W),H(R,W)};ae(N,R=>{n().bodyType==="json"&&R(T)})}var Z=V(N,2);{var X=R=>{var W=Ew(),F=I(W);Ie(F,{rows:5,style:"width: 100%",placeholder:"请输入请求信息",get value(){return n().bodyRaw},oninput:ie=>{a(i,{bodyRaw:ie.target.value})}}),A(W),H(R,W)};ae(Z,R=>{n().bodyType==="raw"&&R(X)})}var M=V(Z,2),Y=I(M);Le(Y,{level:3,mt:"10px",children:(R,W)=>{he();var F=_e("输出参数");H(R,F)},$$slots:{default:!0}});var ee=V(Y,2);Pe(ee,{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{s(i,"outputDefs")},children:(R,W)=>{var F=Nw();H(R,F)},$$slots:{default:!0}}),A(M);var ne=V(M,2);Pn(ne,{}),H(u,p)},$$slots:{icon:!0,default:!0}})),ce(l)}se(Kd,{data:{}},[],[],!0);var Lw=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M15.5 5C13.567 5 12 6.567 12 8.5C12 10.433 13.567 12 15.5 12C17.433 12 19 10.433 19 8.5C19 6.567 17.433 5 15.5 5ZM10 8.5C10 5.46243 12.4624 3 15.5 3C18.5376 3 21 5.46243 21 8.5C21 9.6575 20.6424 10.7315 20.0317 11.6175L22.7071 14.2929L21.2929 15.7071L18.6175 13.0317C17.7315 13.6424 16.6575 14 15.5 14C12.4624 14 10 11.5376 10 8.5ZM3 4H8V6H3V4ZM3 11H8V13H3V11ZM21 18V20H3V18H21Z"></path></svg>'),Dw=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),Hw=J('<div class="heading svelte-15t2v24"><!> <!></div> <!> <!> <div class="setting-title svelte-15t2v24">知识库</div> <div class="setting-item svelte-15t2v24"><!></div> <div class="setting-title svelte-15t2v24">关键字</div> <div class="setting-item svelte-15t2v24"><!></div> <div class="setting-title svelte-15t2v24">获取数据量</div> <div class="setting-item svelte-15t2v24"><!></div> <div class="heading svelte-15t2v24"><!></div> <!>',1);const Mw={hash:"svelte-15t2v24",code:".heading.svelte-15t2v24 {display:flex;margin-bottom:10px;}.setting-title.svelte-15t2v24 {font-size:12px;color:#999;margin-bottom:4px;margin-top:10px;}.setting-item.svelte-15t2v24 {display:flex;align-items:center;justify-content:space-between;margin-bottom:10px;gap:10px;}"};function Xd(e,t){ue(t,!0),He(e,Mw);const n=y(t,"data",7),r=Te(t,["$$slots","$$events","$$legacy","$$host","data"]),o=Ye(),{addParameter:i}=Gt(),s=Rn();let a=Se(Mt([]));Tn(async()=>{const d=await s.provider?.knowledge?.();c(a).push(...d||[])});const{updateNodeData:l}=ot();Ke(()=>{(!n().outputDefs||n().outputDefs.length===0)&&i(o,"outputDefs",{name:"documents",dataType:"Array",nameDisabled:!0,dataTypeDisabled:!0,addChildDisabled:!0,deleteDisabled:!0,children:[{name:"title",dataType:"String",nameDisabled:!0,dataTypeDisabled:!0,deleteDisabled:!0},{name:"content",dataType:"String",nameDisabled:!0,dataTypeDisabled:!0,deleteDisabled:!0},{name:"documentId",dataType:"Number",nameDisabled:!0,dataTypeDisabled:!0,deleteDisabled:!0},{name:"knowledgeId",dataType:"Number",nameDisabled:!0,dataTypeDisabled:!0,deleteDisabled:!0}]})});var u={get data(){return n()},set data(d){n(d),m()}};return Ot(e,Ae({get data(){return n()}},()=>r,{icon:d=>{var p=Lw();H(d,p)},children:(d,p)=>{var f=Hw(),g=oe(f),h=I(g);Le(h,{level:3,children:(O,$)=>{he();var C=_e("输入参数");H(O,C)},$$slots:{default:!0}});var v=V(h,2);Pe(v,{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{i(o)},children:(O,$)=>{var C=Dw();H(O,C)},$$slots:{default:!0}}),A(g);var w=V(g,2);_t(w,{});var b=V(w,2);Le(b,{level:3,mt:"10px",children:(O,$)=>{he();var C=_e("知识库设置");H(O,C)},$$slots:{default:!0}});var k=V(b,4),x=I(k);{let O=z(()=>n().knowledgeId?[n().knowledgeId]:[]);it(x,{get items(){return c(a)},style:"width: 100%",placeholder:"请选择知识库",onSelect:$=>{const C=$.value;l(o,()=>({knowledgeId:C}))},get value(){return c(O)}})}A(k);var S=V(k,4),E=I(S);We(E,{placeholder:"请输入关键字",style:"width: 100%",get value(){return n().keyword},onchange:O=>{const $=O.target.value;l(o,()=>({keyword:$}))}}),A(S);var L=V(S,4),D=I(L);{let O=z(()=>n().limit||"");We(D,{placeholder:"搜索的数据条数",style:"width: 100%",onchange:$=>{const C=$.target.value;l(o,()=>({limit:C}))},get value(){return c(O)}})}A(L);var q=V(L,2),B=I(q);Le(B,{level:3,mt:"10px",children:(O,$)=>{he();var C=_e("输出参数");H(O,C)},$$slots:{default:!0}}),A(q);var U=V(q,2);Pn(U,{}),H(d,f)},$$slots:{icon:!0,default:!0}})),ce(u)}se(Xd,{data:{}},[],[],!0);var Tw=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M18.031 16.6168L22.3137 20.8995L20.8995 22.3137L16.6168 18.031C15.0769 19.263 13.124 20 11 20C6.032 20 2 15.968 2 11C2 6.032 6.032 2 11 2C15.968 2 20 6.032 20 11C20 13.124 19.263 15.0769 18.031 16.6168ZM16.0247 15.8748C17.2475 14.6146 18 12.8956 18 11C18 7.1325 14.8675 4 11 4C7.1325 4 4 7.1325 4 11C4 14.8675 7.1325 18 11 18C12.8956 18 14.6146 17.2475 15.8748 16.0247L16.0247 15.8748Z"></path></svg>'),Vw=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),Ow=J('<div class="heading svelte-15t2v24"><!> <!></div> <!> <!> <div class="setting-title svelte-15t2v24">搜索引擎</div> <div class="setting-item svelte-15t2v24"><!></div> <div class="setting-title svelte-15t2v24">关键字</div> <div class="setting-item svelte-15t2v24"><!></div> <div class="setting-title svelte-15t2v24">搜索数据量</div> <div class="setting-item svelte-15t2v24"><!></div> <div class="heading svelte-15t2v24"><!></div> <!>',1);const Aw={hash:"svelte-15t2v24",code:".heading.svelte-15t2v24 {display:flex;margin-bottom:10px;}.setting-title.svelte-15t2v24 {font-size:12px;color:#999;margin-bottom:4px;margin-top:10px;}.setting-item.svelte-15t2v24 {display:flex;align-items:center;justify-content:space-between;margin-bottom:10px;gap:10px;}"};function Yd(e,t){ue(t,!0),He(e,Aw);const n=y(t,"data",7),r=Te(t,["$$slots","$$events","$$legacy","$$host","data"]),o=Ye(),{addParameter:i}=Gt(),s=Rn();let a=Se(Mt([]));Tn(async()=>{const d=await s.provider?.searchEngine?.();c(a).push(...d||[])});const{updateNodeData:l}=ot();Ke(()=>{(!n().outputDefs||n().outputDefs.length===0)&&i(o,"outputDefs",{name:"documents",dataType:"Array",nameDisabled:!0,dataTypeDisabled:!0,addChildDisabled:!0,deleteDisabled:!0,children:[{name:"title",dataType:"String",nameDisabled:!0,dataTypeDisabled:!0,deleteDisabled:!0},{name:"content",dataType:"String",nameDisabled:!0,dataTypeDisabled:!0,deleteDisabled:!0}]})});var u={get data(){return n()},set data(d){n(d),m()}};return Ot(e,Ae({get data(){return n()}},()=>r,{icon:d=>{var p=Tw();H(d,p)},children:(d,p)=>{var f=Ow(),g=oe(f),h=I(g);Le(h,{level:3,children:(O,$)=>{he();var C=_e("输入参数");H(O,C)},$$slots:{default:!0}});var v=V(h,2);Pe(v,{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{i(o)},children:(O,$)=>{var C=Vw();H(O,C)},$$slots:{default:!0}}),A(g);var w=V(g,2);_t(w,{});var b=V(w,2);Le(b,{level:3,mt:"10px",children:(O,$)=>{he();var C=_e("搜索引擎设置");H(O,C)},$$slots:{default:!0}});var k=V(b,4),x=I(k);{let O=z(()=>n().engine?[n().engine]:[]);it(x,{get items(){return c(a)},style:"width: 100%",placeholder:"请选择搜索引擎",onSelect:$=>{const C=$.value;l(o,()=>({engine:C}))},get value(){return c(O)}})}A(k);var S=V(k,4),E=I(S);We(E,{placeholder:"请输入关键字",style:"width: 100%",get value(){return n().keyword},onchange:O=>{const $=O.target.value;l(o,()=>({keyword:$}))}}),A(S);var L=V(S,4),D=I(L);We(D,{placeholder:"搜索的数据条数",style:"width: 100%",get value(){return n().limit},onchange:O=>{const $=O.target.value;l(o,()=>({limit:$}))}}),A(L);var q=V(L,2),B=I(q);Le(B,{level:3,mt:"10px",children:(O,$)=>{he();var C=_e("输出参数");H(O,C)},$$slots:{default:!0}}),A(q);var U=V(q,2);Pn(U,{}),H(d,f)},$$slots:{icon:!0,default:!0}})),ce(u)}se(Yd,{data:{}},[],[],!0);var Iw=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M5.46257 4.43262C7.21556 2.91688 9.5007 2 12 2C17.5228 2 22 6.47715 22 12C22 14.1361 21.3302 16.1158 20.1892 17.7406L17 12H20C20 7.58172 16.4183 4 12 4C9.84982 4 7.89777 4.84827 6.46023 6.22842L5.46257 4.43262ZM18.5374 19.5674C16.7844 21.0831 14.4993 22 12 22C6.47715 22 2 17.5228 2 12C2 9.86386 2.66979 7.88416 3.8108 6.25944L7 12H4C4 16.4183 7.58172 20 12 20C14.1502 20 16.1022 19.1517 17.5398 17.7716L18.5374 19.5674Z"></path></svg>'),qw=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),Zw=J('<div class="heading svelte-1eqcy61"><!></div> <!> <div class="heading svelte-1eqcy61"><!> <!></div> <!>',1);const Rw={hash:"svelte-1eqcy61",code:".heading.svelte-1eqcy61 {display:flex;margin:10px 0;align-items:center;}.loop_handle_wrapper ::after {content:'循环体';width:100px;height:20px;background:#000;color:#fff;display:flex;justify-content:center;align-items:center;}"};function jd(e,t){ue(t,!0),He(e,Rw);const n=y(t,"data",7),r=Te(t,["$$slots","$$events","$$legacy","$$host","data"]),o=Ye(),{addParameter:i}=Gt();Ke(()=>{(!n().loopVars||n().loopVars.length===0)&&i(o,"loopVars",{name:"loopVar",nameDisabled:!0,deleteDisabled:!0})});var s={get data(){return n()},set data(a){n(a),m()}};return Ot(e,Ae({get data(){return n()}},()=>r,{icon:a=>{var l=Iw();H(a,l)},handle:a=>{An(a,{type:"source",get position(){return ve.Bottom},id:"loop_handle",style:"bottom: -12px;width: 100px",class:"loop_handle_wrapper"})},children:(a,l)=>{var u=Zw(),d=oe(u),p=I(d);Le(p,{level:3,children:(b,k)=>{he();var x=_e("循环变量");H(b,x)},$$slots:{default:!0}}),A(d);var f=V(d,2);_t(f,{dataKeyName:"loopVars"});var g=V(f,2),h=I(g);Le(h,{level:3,children:(b,k)=>{he();var x=_e("输出参数");H(b,x)},$$slots:{default:!0}});var v=V(h,2);Pe(v,{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{i(o,"outputDefs")},children:(b,k)=>{var x=qw();H(b,x)},$$slots:{default:!0}}),A(g);var w=V(g,2);_t(w,{noneParameterText:"无输出参数",dataKeyName:"outputDefs",useChildrenOnly:!0}),H(a,u)},$$slots:{icon:!0,handle:!0,default:!0}})),ce(s)}se(jd,{data:{}},[],[],!0);const Bw=(e,t)=>{const n=e.target.checked;t("required",n)};var Kw=J('<div class="input-more-setting svelte-2f9bnc"><div class="input-more-item svelte-2f9bnc">数据内容： <!></div> <div class="input-more-item svelte-2f9bnc">确认方式： <!></div> <div class="input-more-item svelte-2f9bnc">数据标题： <!></div> <div class="input-more-item svelte-2f9bnc">数据描述： <!></div> <label class="input-item-inline svelte-2f9bnc"><span>是否必填：</span> <input type="checkbox"/></label> <div class="input-more-item svelte-2f9bnc"><!></div></div>'),Xw=J('<div class="input-item svelte-2f9bnc"><!></div> <div class="input-item svelte-2f9bnc"><!></div> <div class="input-item svelte-2f9bnc"><!></div>',1);const Yw={hash:"svelte-2f9bnc",code:".input-item.svelte-2f9bnc {display:flex;align-items:center;}.input-item-inline.svelte-2f9bnc {display:flex;align-items:center;font-size:12px;color:#666;}.input-more-setting.svelte-2f9bnc {display:flex;flex-direction:column;gap:10px;padding:10px;background:#fff;border:1px solid #ddd;border-radius:5px;width:200px;box-shadow:0 0 10px 2px rgba(0, 0, 0, 0.1);}.input-more-setting.svelte-2f9bnc .input-more-item:where(.svelte-2f9bnc) {display:flex;flex-direction:column;gap:3px;font-size:12px;color:#666;}"};function Wd(e,t){ue(t,!0),He(e,Yw);const n=y(t,"parameter",7),r=y(t,"index",7),o=y(t,"dataKeyName",7),i=y(t,"useChildrenOnly",7);let s=Ye(),a=En(s),l=z(()=>({...n(),...a?.current?.data?.[o()][r()]}));const{updateNodeData:u}=ot(),d=($,C)=>{u(s,_=>{let P=_.data?.[o()];return P[r()]={...P[r()],[$]:C},{[o()]:P}})},p=($,C)=>{const _=C.target.value;d($,_)},f=$=>{const C=$.value;d("ref",C)},g=$=>{const C=$.value;d("formType",C)},h=$=>{const C=$.value;d("contentType",C)};let v;const w=()=>{u(s,$=>{let C=$.data?.[o()];return C.splice(r(),1),{[o()]:[...C]}}),v?.hide()};let b=Od(i());var k={get parameter(){return n()},set parameter($){n($),m()},get index(){return r()},set index($){r($),m()},get dataKeyName(){return o()},set dataKeyName($){o($),m()},get useChildrenOnly(){return i()},set useChildrenOnly($){i($),m()}},x=Xw(),S=oe(x),E=I(S);{let $=z(()=>c(l).nameDisabled===!0);We(E,{style:"width: 100%;",get value(){return c(l).name},placeholder:"请输入参数名称",get disabled(){return c($)},oninput:C=>p("name",C)})}A(S);var L=V(S,2),D=I(L);{var q=$=>{We($,{get value(){return c(l).value},placeholder:"请输入参数值",oninput:C=>p("value",C)})},B=$=>{var C=Ce(),_=oe(C);{var P=N=>{{let T=z(()=>[c(l).ref]);it(N,{get items(){return b.current},style:"width: 100%",defaultValue:["ref"],get value(){return c(T)},expandAll:!0,onSelect:f})}};ae(_,N=>{c(l).refType!=="input"&&N(P)},!0)}H($,C)};ae(D,$=>{c(l).refType==="fixed"?$(q):$(B,!1)})}A(L);var U=V(L,2),O=I(U);return Et(lr(O,{placement:"bottom",floating:$=>{var C=Kw(),_=I(C),P=V(I(_));{let F=z(()=>c(l).contentType?[c(l).contentType]:[]);it(P,{get items(){return ta},style:"width: 100%",defaultValue:["text"],get value(){return c(F)},onSelect:h})}A(_);var N=V(_,2),T=V(I(N));{let F=z(()=>c(l).formType?[c(l).formType]:[]);it(T,{get items(){return Vm},style:"width: 100%",defaultValue:["single"],get value(){return c(F)},onSelect:g})}A(N);var Z=V(N,2),X=V(I(Z));Ie(X,{rows:1,style:"width: 100%;",onchange:F=>{p("formLabel",F)},get value(){return c(l).formLabel}}),A(Z);var M=V(Z,2),Y=V(I(M));Ie(Y,{rows:2,style:"width: 100%;",onchange:F=>{p("formDescription",F)},get value(){return c(l).formDescription}}),A(M);var ee=V(M,2),ne=V(I(ee),2);rn(ne),hs(ne,!1),ne.__change=[Bw,d],A(ee);var R=V(ee,2),W=I(R);Pe(W,{onclick:w,children:(F,ie)=>{he();var G=_e("删除");H(F,G)},$$slots:{default:!0}}),A(R),A(C),H($,C)},children:($,C)=>{Eo($,{})},$$slots:{floating:!0,default:!0}}),$=>v=$,()=>v),A(U),H(e,x),ce(k)}wn(["change"]),se(Wd,{parameter:{},index:{},dataKeyName:{},useChildrenOnly:{}},[],[],!0);var jw=J('<div class="input-header svelte-1sm1mgi">参数名称</div> <div class="input-header svelte-1sm1mgi">参数值</div> <div class="input-header svelte-1sm1mgi"></div>',1),Ww=J('<div class="none-params svelte-1sm1mgi"> </div>'),Fw=J('<div class="input-container svelte-1sm1mgi"><!> <!></div>');const Gw={hash:"svelte-1sm1mgi",code:`.input-container.svelte-1sm1mgi {display:grid;grid-template-columns:40% 50% 10%;row-gap:5px;column-gap:3px;}.input-container.svelte-1sm1mgi .none-params:where(.svelte-1sm1mgi) {font-size:12px;background:#f8f8f8;height:40px;display:flex;justify-content:center;align-items:center;border-radius:5px;width:calc(100% - 5px);grid-column:1 / -1;
  /* 从第一列开始到最后一列结束 */}.input-container.svelte-1sm1mgi .input-header:where(.svelte-1sm1mgi) {font-size:12px;color:#666;}`};function Fd(e,t){ue(t,!0),He(e,Gw);const n=y(t,"noneParameterText",7,"无确认数据"),r=y(t,"dataKeyName",7,"parameters"),o=y(t,"useChildrenOnly",7);let i=Ye(),s=En(i),a=z(()=>[...s?.current?.data?.[r()]||[]]);var l={get noneParameterText(){return n()},set noneParameterText(g="无确认数据"){n(g),m()},get dataKeyName(){return r()},set dataKeyName(g="parameters"){r(g),m()},get useChildrenOnly(){return o()},set useChildrenOnly(g){o(g),m()}},u=Fw(),d=I(u);{var p=g=>{var h=jw();he(4),H(g,h)};ae(d,g=>{c(a).length!==0&&g(p)})}var f=V(d,2);return ft(f,19,()=>c(a),g=>g.id,(g,h,v)=>{Wd(g,{get parameter(){return c(h)},get index(){return c(v)},get dataKeyName(){return r()},get useChildrenOnly(){return o()}})},g=>{var h=Ww(),v=I(h,!0);A(h),xe(()=>Oe(v,n())),H(g,h)}),A(u),H(e,u),ce(l)}se(Fd,{noneParameterText:{},dataKeyName:{},useChildrenOnly:{}},[],[],!0);const da=(e,t)=>{if(e===t)return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;const n=Array.isArray(e),r=Array.isArray(t);if(n!==r)return!1;if(n&&r){if(e.length!==t.length)return!1;for(let o=0;o<e.length;o++)if(!da(e[o],t[o]))return!1;return!0}else{const o=Object.keys(e),i=Object.keys(t);if(o.length!==i.length)return!1;for(const s of o)if(!(s in t)||!da(e[s],t[s]))return!1;return!0}};var Uw=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M23 12L15.9289 19.0711L14.5147 17.6569L20.1716 12L14.5147 6.34317L15.9289 4.92896L23 12ZM3.82843 12L9.48528 17.6569L8.07107 19.0711L1 12L8.07107 4.92896L9.48528 6.34317L3.82843 12Z"></path></svg>'),Jw=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),Qw=J('<div class="heading svelte-15t2v24"><!> <!></div> <!> <!> <div class="setting-title svelte-15t2v24">消息内容</div> <div class="setting-item svelte-15t2v24"><!></div> <div class="heading svelte-15t2v24"><!></div> <!>',1);const eb={hash:"svelte-15t2v24",code:".heading.svelte-15t2v24 {display:flex;margin-bottom:10px;}.setting-title.svelte-15t2v24 {font-size:12px;color:#999;margin-bottom:4px;margin-top:10px;}.setting-item.svelte-15t2v24 {display:flex;align-items:center;justify-content:space-between;margin-bottom:10px;gap:10px;}"};function Gd(e,t){ue(t,!0),He(e,eb);const n=y(t,"data",7),r=Te(t,["$$slots","$$events","$$legacy","$$host","data"]),o=Ye(),{addParameter:i}=Gt(),{updateNodeData:s}=ot();Ke(()=>{if(n().confirms){const l=n().confirms.map(u=>({...u,nameDisabled:!0,dataTypeDisabled:!0,dataType:u.formType==="checkbox"||u.formType==="select"?"Array":"String",addChildDisabled:!0}));da(l,n().outputDefs)||s(o,()=>({outputDefs:l}))}});var a={get data(){return n()},set data(l){n(l),m()}};return Ot(e,Ae({get data(){return n()}},()=>r,{icon:l=>{var u=Uw();H(l,u)},children:(l,u)=>{var d=Qw(),p=oe(d),f=I(p);Le(f,{level:3,children:(E,L)=>{he();var D=_e("确认数据");H(E,D)},$$slots:{default:!0}});var g=V(f,2);Pe(g,{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{i(o,"confirms")},children:(E,L)=>{var D=Jw();H(E,D)},$$slots:{default:!0}}),A(p);var h=V(p,2);Fd(h,{dataKeyName:"confirms",noneParameterText:"无确认数据"});var v=V(h,2);Le(v,{level:3,mt:"10px",children:(E,L)=>{he();var D=_e("确认消息");H(E,D)},$$slots:{default:!0}});var w=V(v,4),b=I(w);{let E=z(()=>n().message||"");Ie(b,{rows:5,placeholder:"请输入用户需要确认的消息内容",style:"width: 100%",onchange:L=>{s(o,()=>({message:L.target.value}))},get value(){return c(E)}})}A(w);var k=V(w,2),x=I(k);Le(x,{level:3,mt:"10px",children:(E,L)=>{he();var D=_e("输出参数");H(E,D)},$$slots:{default:!0}}),A(k);var S=V(k,2);Pn(S,{placeholder:""}),H(l,d)},$$slots:{icon:!0,default:!0}})),ce(a)}se(Gd,{data:{}},[],[],!0);const tb={startNode:Hd,codeNode:Rd,confirmNode:Gd,llmNode:Zd,templateNode:Bd,httpNode:Kd,knowledgeNode:Xd,searchEngineNode:Yd,loopNode:jd,endNode:Id};var nb=J("<!> ",1);function fa(e,t){ue(t,!0);const n=y(t,"icon",7),r=y(t,"title",7),o=y(t,"type",7),i=y(t,"description",7),s=y(t,"extra",7),a=u=>{if(!u.dataTransfer)return null;const d={type:o(),data:{title:r(),description:i(),...s()}};u.dataTransfer.setData("application/tinyflow",JSON.stringify(d)),u.dataTransfer.effectAllowed="move"};var l={get icon(){return n()},set icon(u){n(u),m()},get title(){return r()},set title(u){r(u),m()},get type(){return o()},set type(u){o(u),m()},get description(){return i()},set description(u){i(u),m()},get extra(){return s()},set extra(u){s(u),m()}};return Pe(e,{draggable:!0,ondragstart:a,get"data-node-type"(){return o()},children:(u,d)=>{var p=nb(),f=oe(p);cs(f,n);var g=V(f);xe(()=>Oe(g,` ${r()??""}`)),H(u,p)},$$slots:{default:!0}}),ce(l)}se(fa,{icon:{},title:{},type:{},description:{},extra:{}},[],[],!0);var rb=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M4.83582 12L11.0429 18.2071L12.4571 16.7929L7.66424 12L12.4571 7.20712L11.0429 5.79291L4.83582 12ZM10.4857 12L16.6928 18.2071L18.107 16.7929L13.3141 12L18.107 7.20712L16.6928 5.79291L10.4857 12Z"></path></svg>'),ob=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M19.1642 12L12.9571 5.79291L11.5429 7.20712L16.3358 12L11.5429 16.7929L12.9571 18.2071L19.1642 12ZM13.5143 12L7.30722 5.79291L5.89301 7.20712L10.6859 12L5.89301 16.7929L7.30722 18.2071L13.5143 12Z"></path></svg>'),ib=J('<div><div class="tf-toolbar-container "><div class="tf-toolbar-container-header"><!></div> <div class="tf-toolbar-container-body"><div class="tf-toolbar-container-base"></div> <div class="tf-toolbar-container-tools"></div></div></div> <!></div>');function Ud(e,t){ue(t,!0);let n=Se("base"),r=Se("show");const o=[{icon:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22ZM12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4C7.58172 4 4 7.58172 4 12C4 16.4183 7.58172 20 12 20ZM12 15C10.3431 15 9 13.6569 9 12C9 10.3431 10.3431 9 12 9C13.6569 9 15 10.3431 15 12C15 13.6569 13.6569 15 12 15Z"></path></svg>',title:"开始节点",type:"startNode",sortNo:100,description:"开始定义输入参数"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M5.46257 4.43262C7.21556 2.91688 9.5007 2 12 2C17.5228 2 22 6.47715 22 12C22 14.1361 21.3302 16.1158 20.1892 17.7406L17 12H20C20 7.58172 16.4183 4 12 4C9.84982 4 7.89777 4.84827 6.46023 6.22842L5.46257 4.43262ZM18.5374 19.5674C16.7844 21.0831 14.4993 22 12 22C6.47715 22 2 17.5228 2 12C2 9.86386 2.66979 7.88416 3.8108 6.25944L7 12H4C4 16.4183 7.58172 20 12 20C14.1502 20 16.1022 19.1517 17.5398 17.7716L18.5374 19.5674Z"></path></svg>',title:"循环",type:"loopNode",sortNo:200,description:"用于循环执行任务"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M20.7134 7.12811L20.4668 7.69379C20.2864 8.10792 19.7136 8.10792 19.5331 7.69379L19.2866 7.12811C18.8471 6.11947 18.0555 5.31641 17.0677 4.87708L16.308 4.53922C15.8973 4.35653 15.8973 3.75881 16.308 3.57612L17.0252 3.25714C18.0384 2.80651 18.8442 1.97373 19.2761 0.930828L19.5293 0.319534C19.7058 -0.106511 20.2942 -0.106511 20.4706 0.319534L20.7238 0.930828C21.1558 1.97373 21.9616 2.80651 22.9748 3.25714L23.6919 3.57612C24.1027 3.75881 24.1027 4.35653 23.6919 4.53922L22.9323 4.87708C21.9445 5.31641 21.1529 6.11947 20.7134 7.12811ZM9 2C13.0675 2 16.426 5.03562 16.9337 8.96494L19.1842 12.5037C19.3324 12.7367 19.3025 13.0847 18.9593 13.2317L17 14.071V17C17 18.1046 16.1046 19 15 19H13.001L13 22H4L4.00025 18.3061C4.00033 17.1252 3.56351 16.0087 2.7555 15.0011C1.65707 13.6313 1 11.8924 1 10C1 5.58172 4.58172 2 9 2ZM9 4C5.68629 4 3 6.68629 3 10C3 11.3849 3.46818 12.6929 4.31578 13.7499C5.40965 15.114 6.00036 16.6672 6.00025 18.3063L6.00013 20H11.0007L11.0017 17H15V12.7519L16.5497 12.0881L15.0072 9.66262L14.9501 9.22118C14.5665 6.25141 12.0243 4 9 4ZM19.4893 16.9929L21.1535 18.1024C22.32 16.3562 23 14.2576 23 12.0001C23 11.317 22.9378 10.6486 22.8186 10L20.8756 10.5C20.9574 10.9878 21 11.489 21 12.0001C21 13.8471 20.4436 15.5642 19.4893 16.9929Z"></path></svg>',title:"大模型",type:"llmNode",sortNo:300,description:"使用大模型处理问题"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M15.5 5C13.567 5 12 6.567 12 8.5C12 10.433 13.567 12 15.5 12C17.433 12 19 10.433 19 8.5C19 6.567 17.433 5 15.5 5ZM10 8.5C10 5.46243 12.4624 3 15.5 3C18.5376 3 21 5.46243 21 8.5C21 9.6575 20.6424 10.7315 20.0317 11.6175L22.7071 14.2929L21.2929 15.7071L18.6175 13.0317C17.7315 13.6424 16.6575 14 15.5 14C12.4624 14 10 11.5376 10 8.5ZM3 4H8V6H3V4ZM3 11H8V13H3V11ZM21 18V20H3V18H21Z"></path></svg>',title:"知识库",type:"knowledgeNode",sortNo:400,description:"通过知识库获取内容"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M18.031 16.6168L22.3137 20.8995L20.8995 22.3137L16.6168 18.031C15.0769 19.263 13.124 20 11 20C6.032 20 2 15.968 2 11C2 6.032 6.032 2 11 2C15.968 2 20 6.032 20 11C20 13.124 19.263 15.0769 18.031 16.6168ZM16.0247 15.8748C17.2475 14.6146 18 12.8956 18 11C18 7.1325 14.8675 4 11 4C7.1325 4 4 7.1325 4 11C4 14.8675 7.1325 18 11 18C12.8956 18 14.6146 17.2475 15.8748 16.0247L16.0247 15.8748Z"></path></svg>',title:"搜索引擎",type:"searchEngineNode",sortNo:500,description:"通过搜索引擎搜索内容"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M6.23509 6.45329C4.85101 7.89148 4 9.84636 4 12C4 16.4183 7.58172 20 12 20C13.0808 20 14.1116 19.7857 15.0521 19.3972C15.1671 18.6467 14.9148 17.9266 14.8116 17.6746C14.582 17.115 13.8241 16.1582 12.5589 14.8308C12.2212 14.4758 12.2429 14.2035 12.3636 13.3943L12.3775 13.3029C12.4595 12.7486 12.5971 12.4209 14.4622 12.1248C15.4097 11.9746 15.6589 12.3533 16.0043 12.8777C16.0425 12.9358 16.0807 12.9928 16.1198 13.0499C16.4479 13.5297 16.691 13.6394 17.0582 13.8064C17.2227 13.881 17.428 13.9751 17.7031 14.1314C18.3551 14.504 18.3551 14.9247 18.3551 15.8472V15.9518C18.3551 16.3434 18.3168 16.6872 18.2566 16.9859C19.3478 15.6185 20 13.8854 20 12C20 8.70089 18.003 5.8682 15.1519 4.64482C14.5987 5.01813 13.8398 5.54726 13.575 5.91C13.4396 6.09538 13.2482 7.04166 12.6257 7.11976C12.4626 7.14023 12.2438 7.12589 12.012 7.11097C11.3905 7.07058 10.5402 7.01606 10.268 7.75495C10.0952 8.2232 10.0648 9.49445 10.6239 10.1543C10.7134 10.2597 10.7307 10.4547 10.6699 10.6735C10.59 10.9608 10.4286 11.1356 10.3783 11.1717C10.2819 11.1163 10.0896 10.8931 9.95938 10.7412C9.64554 10.3765 9.25405 9.92233 8.74797 9.78176C8.56395 9.73083 8.36166 9.68867 8.16548 9.64736C7.6164 9.53227 6.99443 9.40134 6.84992 9.09302C6.74442 8.8672 6.74488 8.55621 6.74529 8.22764C6.74529 7.8112 6.74529 7.34029 6.54129 6.88256C6.46246 6.70541 6.35689 6.56446 6.23509 6.45329ZM12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22Z"></path></svg>',title:"Http 请求",type:"httpNode",sortNo:600,description:"通过 HTTP 请求获取数据"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M23 12L15.9289 19.0711L14.5147 17.6569L20.1716 12L14.5147 6.34317L15.9289 4.92896L23 12ZM3.82843 12L9.48528 17.6569L8.07107 19.0711L1 12L8.07107 4.92896L9.48528 6.34317L3.82843 12Z"></path></svg>',title:"动态代码",type:"codeNode",sortNo:700,description:"动态执行代码"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M2 4C2 3.44772 2.44772 3 3 3H21C21.5523 3 22 3.44772 22 4V20C22 20.5523 21.5523 21 21 21H3C2.44772 21 2 20.5523 2 20V4ZM4 5V19H20V5H4ZM7 8H17V11H15V10H13V14H14.5V16H9.5V14H11V10H9V11H7V8Z"></path></svg>',title:"内容模板",type:"templateNode",sortNo:800,description:"通过模板引擎生成内容"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M15.3873 13.4975L17.9403 20.5117L13.2418 22.2218L10.6889 15.2076L6.79004 17.6529L8.4086 1.63318L19.9457 12.8646L15.3873 13.4975ZM15.3768 19.3163L12.6618 11.8568L15.6212 11.4459L9.98201 5.9561L9.19088 13.7863L11.7221 12.1988L14.4371 19.6583L15.3768 19.3163Z"></path></svg>',title:"用户确认",type:"confirmNode",sortNo:900,description:"确认继续或选择内容"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M6 5.1438V16.0002H18.3391L6 5.1438ZM4 2.932C4 2.07155 5.01456 1.61285 5.66056 2.18123L21.6501 16.2494C22.3423 16.8584 21.9116 18.0002 20.9896 18.0002H6V22H4V2.932Z"></path></svg>',title:"结束节点",type:"endNode",sortNo:1e3,description:"结束定义输出参数"}],i=[{label:"基础节点",value:"base"},{label:"业务工具",value:"tools"}],s=[],a=Rn(),l=a.customNodes;if(l){const b=Object.keys(l).sort((k,x)=>(l[k].sortNo||0)-(l[x].sortNo||0));for(let k of b)l[k].group==="base"?o.push({type:k,...l[k]}):s.push({icon:l[k].icon,title:l[k].title,type:k});o.sort((k,x)=>(k.sortNo||0)-(x.sortNo||0))}if(a.hiddenNodes){const b=typeof a.hiddenNodes=="function"?a.hiddenNodes():a.hiddenNodes;if(Array.isArray(b)){for(let k of b)for(let x=0;x<o.length;x++)if(o[x].type===k){o.splice(x,1);break}}}var u=ib(),d=I(u),p=I(d),f=I(p);cd(f,{style:"width: 100%",get items(){return i},onChange:b=>{j(n,b.value.toString(),!0)}}),A(p);var g=V(p,2),h=I(g);ft(h,21,()=>o,kr,(b,k)=>{fa(b,Ae(()=>c(k)))}),A(h);var v=V(h,2);ft(v,21,()=>s,kr,(b,k)=>{fa(b,Ae(()=>c(k)))}),A(v),A(g),A(d);var w=V(d,2);Pe(w,{onclick:()=>{j(r,c(r)?"":"show",!0)},children:(b,k)=>{var x=Ce(),S=oe(x);{var E=D=>{var q=rb();H(D,q)},L=D=>{var q=ob();H(D,q)};ae(S,D=>{c(r)==="show"?D(E):D(L,!1)})}H(b,x)},$$slots:{default:!0}}),A(u),xe(()=>{bt(u,1,`tf-toolbar ${c(r)??""}`),rt(h,`display: ${c(n)==="base"?"flex":"none"}`),rt(v,`display: ${c(n)!=="base"?"flex":"none"}`)}),H(e,u),ce()}se(Ud,{},[],[],!0);const sb=()=>({getNode:e=>Re.getNode(e)}),ab=()=>({ensureParentInNodesBefore:(e,t)=>{Re.updateNodes(n=>{let r=-1;for(let s=0;s<n.length;s++)if(n[s].id===e){r=s;break}if(r<=0)return n;let o=-1;for(let s=0;s<r;s++)if(n[s].parentId===e||n[s].id===t){o=s;break}if(o==-1)return n;const i=n[r];for(let s=r;s>o;s--)n[s]=n[s-1];return n[o]=i,n})}}),lb=()=>({getEdgesByTarget:e=>Re.getEdges().filter(t=>t.target===e)});var ub=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),cb=J('<div class="heading svelte-q0cqsa"><!> <!></div> <!>',1),db=J('<div class="setting-title svelte-q0cqsa"> </div> <div class="setting-item svelte-q0cqsa"><!></div>',1),fb=J('<div class="setting-title svelte-q0cqsa"> </div> <div class="setting-item svelte-q0cqsa"><!></div>',1),pb=J('<div class="setting-title svelte-q0cqsa"> </div> <div class="setting-item svelte-q0cqsa"><div class="slider-container svelte-q0cqsa"><span class="svelte-q0cqsa"> </span> <input/></div></div>',1),gb=J('<div class="setting-title svelte-q0cqsa"> </div> <div class="setting-item svelte-q0cqsa"><!></div>',1),hb=J('<div class="setting-title svelte-q0cqsa"> </div> <div class="setting-item svelte-q0cqsa"><!></div>',1),vb=ge('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),mb=J('<div class="heading svelte-q0cqsa"><!> <!></div> <!>',1),yb=J("<!> <!> <div></div> <!>",1);const wb={hash:"svelte-q0cqsa",code:`.heading.svelte-q0cqsa {display:flex;align-items:center;margin-bottom:10px;}.setting-title.svelte-q0cqsa {font-size:12px;color:#999;margin-bottom:4px;margin-top:10px;}.setting-item.svelte-q0cqsa {display:flex;align-items:center;justify-content:space-between;margin-bottom:10px;gap:10px;}

    /* 新增样式 */.slider-container.svelte-q0cqsa {width:100%;display:flex;flex-direction:column;gap:4px;}.slider-container.svelte-q0cqsa span:where(.svelte-q0cqsa) {font-size:12px;color:#666;display:flex;justify-content:space-between;align-items:center;}input[type="range"].svelte-q0cqsa {width:100%;height:4px;background:#ddd;border-radius:2px;outline:none;-webkit-appearance:none;}input[type="range"].svelte-q0cqsa::-webkit-slider-thumb {-webkit-appearance:none;width:14px;height:14px;background:#007bff;border-radius:50%;cursor:pointer;}`};function Jd(e,t){ue(t,!0),He(e,wb);const n=y(t,"data",7),r=Te(t,["$$slots","$$events","$$legacy","$$host","data"]),o=Ye(),{addParameter:i}=Gt(),s=ot(),{updateNodeData:a}=s,l=w=>{a(o,w)},u=(w,b)=>{l({[w]:b.target?.value})},d={...r,id:o,data:n()},p=document.createElement("div"),f=Rn().customNodes[t.type];f.render?.(p,d,s);const g=f.forms;let h;Ke(()=>{n().expand&&h&&h.append(p)}),Ke(()=>{n()&&f.onUpdate?.(p,{...d,data:n()})}),Ke(()=>{!n().parameters&&f.parameters&&l({parameters:No(JSON.parse(JSON.stringify(f.parameters)))})}),Ke(()=>{!n().outputDefs&&f.outputDefs&&l({outputDefs:No(JSON.parse(JSON.stringify(f.outputDefs)))})});var v={get data(){return n()},set data(w){n(w),m()}};{const w=k=>{var x=Ce(),S=oe(x);cs(S,()=>f.icon),H(k,x)};let b=z(()=>({...n(),description:f.description}));Ot(e,Ae({get data(){return c(b)}},()=>r,{icon:w,children:(k,x)=>{var S=yb(),E=oe(S);{var L=$=>{var C=cb(),_=oe(C),P=I(_);Le(P,{level:3,children:(X,M)=>{he();var Y=_e("输入参数");H(X,Y)},$$slots:{default:!0}});var N=V(P,2);{var T=X=>{Pe(X,{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{i(o)},children:(M,Y)=>{var ee=ub();H(M,ee)},$$slots:{default:!0}})};ae(N,X=>{f.parametersAddEnable!==!1&&X(T)})}A(_);var Z=V(_,2);_t(Z,{}),H($,C)};ae(E,$=>{f.parametersEnable!==!1&&$(L)})}var D=V(E,2);{var q=$=>{var C=Ce(),_=oe(C);ft(_,17,()=>g,kr,(P,N)=>{var T=Ce(),Z=oe(T);{var X=Y=>{var ee=db(),ne=oe(ee),R=I(ne,!0);A(ne);var W=V(ne,2),F=I(W);{let ie=z(()=>n()[c(N).name]||c(N).defaultValue);We(F,Ae({get placeholder(){return c(N).placeholder},style:"width: 100%",get value(){return c(ie)}},()=>c(N).attrs,{onchange:G=>{u(c(N).name,G)}}))}A(W),xe(()=>Oe(R,c(N).label)),H(Y,ee)},M=Y=>{var ee=Ce(),ne=oe(ee);{var R=F=>{var ie=fb(),G=oe(ie),me=I(G,!0);A(G);var we=V(G,2),re=I(we);{let Q=z(()=>n()[c(N).name]||c(N).defaultValue);Ie(re,Ae({rows:3,get placeholder(){return c(N).placeholder},style:"width: 100%",get value(){return c(Q)}},()=>c(N).attrs,{onchange:te=>{u(c(N).name,te)}}))}A(we),xe(()=>Oe(me,c(N).label)),H(F,ie)},W=F=>{var ie=Ce(),G=oe(ie);{var me=re=>{var Q=pb(),te=oe(Q),de=I(te,!0);A(te);var fe=V(te,2),le=I(fe),Ne=I(le),ke=I(Ne);A(Ne);var K=V(Ne,2);rn(K);var st=De=>l({[c(N).name]:parseFloat(De.target.value)});Ue(K,()=>({class:"nodrag",type:"range",...c(N).attrs,value:n()[c(N).name]??c(N).defaultValue,oninput:st}),void 0,void 0,"svelte-q0cqsa"),A(le),A(fe),xe(()=>{Oe(de,c(N).label),Oe(ke,`${c(N).description??""}: ${n()[c(N).name]??c(N).defaultValue??""}`)}),H(re,Q)},we=re=>{var Q=Ce(),te=oe(Q);{var de=le=>{var Ne=gb(),ke=oe(Ne),K=I(ke,!0);A(ke);var st=V(ke,2),De=I(st);{let qe=z(()=>c(N).options||[]),Me=z(()=>n()[c(N).name]?[n()[c(N).name]]:[c(N).defaultValue]);it(De,{get items(){return c(qe)},style:"width: 100%",get placeholder(){return c(N).placeholder},onSelect:at=>{const lt=at.value;l({[c(N).name]:lt})},get value(){return c(Me)}})}A(st),xe(()=>Oe(K,c(N).label)),H(le,Ne)},fe=le=>{var Ne=Ce(),ke=oe(Ne);{var K=De=>{var qe=hb(),Me=oe(qe),at=I(Me,!0);A(Me);var lt=V(Me,2),At=I(lt);{let Ve=z(()=>c(N).chosen?.buttonText);ud(At,{style:"width: 100%",get placeholder(){return c(N).placeholder},get buttonText(){return c(Ve)},onChosen:(Fe,pe,Be)=>{c(N).chosen?.onChosen?.(l,Fe,pe,Be)},get value(){return n()[c(N).chosen?.valueDataKey||""]},get label(){return n()[c(N).chosen?.labelDataKey||""]}})}A(lt),xe(()=>Oe(at,c(N).label)),H(De,qe)},st=De=>{var qe=Ce(),Me=oe(qe);{var at=lt=>{Le(lt,Ae({level:3,mt:"10px"},()=>c(N).attrs,{children:(At,Ve)=>{he();var Fe=_e();xe(()=>Oe(Fe,c(N).label)),H(At,Fe)},$$slots:{default:!0}}))};ae(Me,lt=>{c(N).type==="heading"&&lt(at)},!0)}H(De,qe)};ae(ke,De=>{c(N).type==="chosen"?De(K):De(st,!1)},!0)}H(le,Ne)};ae(te,le=>{c(N).type==="select"?le(de):le(fe,!1)},!0)}H(re,Q)};ae(G,re=>{c(N).type==="slider"?re(me):re(we,!1)},!0)}H(F,ie)};ae(ne,F=>{c(N).type==="textarea"?F(R):F(W,!1)},!0)}H(Y,ee)};ae(Z,Y=>{c(N).type==="input"?Y(X):Y(M,!1)})}H(P,T)}),H($,C)};ae(D,$=>{g&&$(q)})}var B=V(D,2);Et(B,$=>h=$,()=>h);var U=V(B,2);{var O=$=>{var C=mb(),_=oe(C),P=I(_);Le(P,{level:3,mt:"10px",children:(X,M)=>{he();var Y=_e("输出参数");H(X,Y)},$$slots:{default:!0}});var N=V(P,2);{var T=X=>{Pe(X,{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{i(o,"outputDefs")},children:(M,Y)=>{var ee=vb();H(M,ee)},$$slots:{default:!0}})};ae(N,X=>{f.outputDefsAddEnable!==!1&&X(T)})}A(_);var Z=V(_,2);Pn(Z,{}),H($,C)};ae(U,$=>{f.outputDefsEnable!==!1&&$(O)})}xe(()=>{rt(B,f.rootStyle||""),bt(B,1,bn(f.rootClass),"svelte-q0cqsa")}),H(k,S)},$$slots:{icon:!0,default:!0}}))}return ce(v)}se(Jd,{data:{}},[],[],!0);const bb=()=>({updateEdgeData:(e,t,n)=>{const r=Re.getEdge(e);if(!r)return;const o=typeof t=="function"?t(r):t;r.data=n?.replace?o:{...r.data,...o},Re.updateEdges(i=>i.map(s=>s.id===e?r:s))}}),xb=()=>({deleteEdge:e=>{Re.removeEdge(e)}});var _b=J('<div class="panel-content svelte-80qc4q"><div>边属性设置</div> <div class="setting-title svelte-80qc4q">边条件设置</div> <div class="setting-item svelte-80qc4q"><!></div> <div class="setting-item svelte-80qc4q" style="padding: 8px 0"><!> <!></div></div>'),kb=J("<!> <!> <!> <!>",1),$b=J('<div style="position: relative; height: 100%; width: 100%;overflow: hidden"><!> <!></div>');const Cb={hash:"svelte-80qc4q",code:".panel-content.svelte-80qc4q {padding:10px;background-color:#fff;border-radius:5px;box-shadow:0 2px 4px rgba(0, 0, 0, 0.1);width:200px;border:1px solid #efefef;}.setting-title.svelte-80qc4q {margin:10px 0;font-size:12px;color:#999;}.setting-item.svelte-80qc4q {display:flex;gap:5px;align-items:center;justify-content:end;}"};function Qd(e,t){ue(t,!0),He(e,Cb);const n=y(t,"onInit",7),r=ot();n()(r);let o=Se(!1),i=Se(null);const{updateEdgeData:s}=bb(),a=P=>{P.preventDefault(),P.dataTransfer&&(P.dataTransfer.dropEffect="move")},l=P=>{P.preventDefault();const N=r.screenToFlowPosition({x:P.clientX-250,y:P.clientY-100}),T=P.dataTransfer?.getData("application/tinyflow");if(!T)return;const Z=JSON.parse(T),X={id:`node_${ur()}`,position:N,data:{},...Z};Re.addNode(X),Re.selectNodeOnly(X.id)},{getNode:u}=sb(),d=P=>{const N=u(P.source),T=u(P.target);if(P.sourceHandle==="loop_handle"||N.parentId){const Z=r.getEdges();for(let X of Z)if(X.target===P.target){const M=u(X.source);if(P.sourceHandle==="loop_handle"&&M.parentId!==N.id||N.parentId&&M.parentId!==N.parentId)return!1}}return!(!N.parentId&&T.parentId&&T.parentId!==N.id)},{ensureParentInNodesBefore:p}=ab(),f=(P,N)=>{if(!N.isValid)return;const T=N.toNode;if(T.parentId)return;const Z=N.fromNode,X=N.fromHandle,M={position:{...T.position}};if(X.id==="loop_handle"?M.parentId=Z.id:Z.parentId&&(M.parentId=Z.parentId),M.parentId){const Y=u(M.parentId);M.position={x:T.position.x-Y.position.x,y:T.position.y-Y.position.y},p(M.parentId,T.id),r.updateNode(T.id,M)}setTimeout(()=>{Re.getEdges().forEach(Y=>{Y.target===T.id&&Y.source==Z.id&&(j(o,!0),j(i,Y,!0))})})},{getEdgesByTarget:g}=lb(),h=P=>{P.edges.forEach(N=>{N.id===c(i)?.id&&(j(i,null),j(o,!1));const T=u(N.target);if(T&&T.parentId){const Z=g(N.target),X=u(T.parentId);if(Z.length===0)r.updateNode(T.id,{parentId:void 0,position:{x:T.position.x+X.position.x,y:T.position.y+X.position.y}});else{let M=!1;for(let Y=0;Y<Z.length;Y++){const ee=Z[Y],ne=u(ee.source);if(ne.parentId||ne.type==="loopNode"){M=!0;break}}M||r.updateNode(T.id,{parentId:void 0,position:{x:T.position.x+X.position.x,y:T.position.y+X.position.y}})}}})},{deleteEdge:v}=xb(),w=(P,N)=>{},b=P=>{},k={},x=Rn().customNodes;if(x)for(let P of Object.keys(x))k[P]=Jd;const S=Rn().onDataChange;Ke(()=>{S?.({nodes:Re.getNodes(),edges:Re.getEdges(),viewport:Re.getViewport()})});var E={get onInit(){return n()},set onInit(P){n(P),m()}},L=$b(),D=I(L),q=Re.getNodes,B=Re.setNodes,U=Re.getEdges,O=Re.setEdges,$=Re.getViewport,C=Re.setViewport;{let P=z(()=>({...tb,...k})),N=z(()=>({markerEnd:{type:fo.ArrowClosed,width:20,height:20}}));jc(D,{get nodeTypes(){return c(P)},get nodes(){return q()},set nodes(T){B(T)},get edges(){return U()},set edges(T){O(T)},get viewport(){return $()},set viewport(T){C(T)},class:"tinyflow-logo",ondrop:l,ondragover:a,isValidConnection:d,onconnectend:f,onconnectstart:w,onconnect:b,connectionRadius:50,onedgeclick:T=>{j(o,!0),j(i,T.edge,!0)},onbeforeconnect:T=>({...T,id:ur()}),ondelete:h,onclick:T=>{const Z=T.target;Z.classList.contains("svelte-flow__edge-interaction")||Z.classList.contains("panel-content")||Z.closest(".panel-content")||(j(o,!1),j(i,null))},get defaultEdgeOptions(){return c(N)},children:(T,Z)=>{var X=kb(),M=oe(X);rd(M,{});var Y=V(M,2);ed(Y,{});var ee=V(Y,2);id(ee,{});var ne=V(ee,2);{var R=W=>{bo(W,{children:(F,ie)=>{var G=_b(),me=V(I(G),4),we=I(me);{let de=z(()=>c(i)?.data?.condition);Ie(we,{rows:3,placeholder:"请输入边条件",style:"width: 100%",get value(){return c(de)},onchange:fe=>{c(i)&&s(c(i).id,{condition:fe.target?.value})}})}A(me);var re=V(me,2),Q=I(re);Pe(Q,{onclick:()=>{v(c(i)?.id),j(o,!1)},children:(de,fe)=>{he();var le=_e("删除");H(de,le)},$$slots:{default:!0}});var te=V(Q,2);Pe(te,{primary:!0,onclick:()=>{j(o,!1)},children:(de,fe)=>{he();var le=_e("保存");H(de,le)},$$slots:{default:!0}}),A(re),A(G),H(F,G)},$$slots:{default:!0}})};ae(ne,W=>{c(o)&&W(R)})}H(T,X)},$$slots:{default:!0}})}var _=V(D,2);return Ud(_,{}),A(L),H(e,L),ce(E)}se(Qd,{onInit:{}},[],[],!0);function Sb(e,t){ue(t,!0);const n=y(t,"options",7),r=y(t,"onInit",7);let{data:o}=n();if(typeof o=="string")try{o=JSON.parse(o.trim())}catch{console.error("Invalid JSON data:",o)}Re.init(o?.nodes||[],o?.edges||[]),mr("tinyflow_options",n());var i={get options(){return n()},set options(s){n(s),m()},get onInit(){return r()},set onInit(s){r(s),m()}};return Wc(e,{children:(s,a)=>{Qd(s,{get onInit(){return r()}})},$$slots:{default:!0}}),ce(i)}customElements.define("tinyflow-component",se(Sb,{options:{},onInit:{}},[],[],!1));const Eb=Lt.defineComponent({__name:"Tinyflow",props:{className:{},style:{},data:{},provider:{},customNodes:{},onNodeExecute:{type:Function},hiddenNodes:{type:[Array,Function]},onDataChange:{type:Function}},setup(e,{expose:t}){const n=e,r=Lt.ref(null);let o=null;return Lt.onMounted(()=>{r.value&&(o=new Om({...n,element:r.value}))}),Lt.onUnmounted(()=>{o&&(o.destroy(),o=null)}),t({getData:()=>o?o.getData():(console.warn("Tinyflow instance is not initialized"),null)}),(s,a)=>(Lt.openBlock(),Lt.createElementBlock("div",{ref_key:"divRef",ref:r,class:Lt.normalizeClass(["tinyflow",s.className]),style:Lt.normalizeStyle(s.style)},null,6))}});Kn.Tinyflow=Eb,Object.defineProperty(Kn,Symbol.toStringTag,{value:"Module"})});
//# sourceMappingURL=index.umd.js.map
