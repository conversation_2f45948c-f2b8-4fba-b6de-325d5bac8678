# @tinyflow-ai/ui

## 1.1.1

### Patch Changes

- [`872818c`](https://github.com/tinyflow-ai/tinyflow/commit/872818c0d6e73e9ddc3c724a14883ee52666786e) Thanks [@yangfuhai](https://github.com/yangfuhai)! - v1.1.1

- Updated dependencies [[`872818c`](https://github.com/tinyflow-ai/tinyflow/commit/872818c0d6e73e9ddc3c724a14883ee52666786e)]:
    - @tinyflow-ai/ui@1.1.1

## 1.1.0

### Minor Changes

- [`cf73a47`](https://github.com/tinyflow-ai/tinyflow/commit/cf73a478180f9e561d4eca7bfd6d5d8286cedd43) Thanks [@yangfuhai](https://github.com/yangfuhai)! - v1.1.0

### Patch Changes

- Updated dependencies [[`cf73a47`](https://github.com/tinyflow-ai/tinyflow/commit/cf73a478180f9e561d4eca7bfd6d5d8286cedd43)]:
    - @tinyflow-ai/ui@1.1.0

## 1.0.4

### Patch Changes

- [`67b0721`](https://github.com/tinyflow-ai/tinyflow/commit/67b072190068ea41cc29fd21929cd7e1d46ec4b0) Thanks [@yangfuhai](https://github.com/yangfuhai)! - v1.0.4

- Updated dependencies [[`67b0721`](https://github.com/tinyflow-ai/tinyflow/commit/67b072190068ea41cc29fd21929cd7e1d46ec4b0)]:
    - @tinyflow-ai/ui@1.0.4

## 1.0.3

### Patch Changes

- [`47584bb`](https://github.com/tinyflow-ai/tinyflow/commit/47584bb76e6a1eba700cc878b3f1ef1c59c4ea4c) Thanks [@yangfuhai](https://github.com/yangfuhai)! - v1.0.3

- Updated dependencies [[`47584bb`](https://github.com/tinyflow-ai/tinyflow/commit/47584bb76e6a1eba700cc878b3f1ef1c59c4ea4c)]:
    - @tinyflow-ai/ui@1.0.3

## 1.0.2

### Patch Changes

- [`806e81b`](https://github.com/tinyflow-ai/tinyflow/commit/806e81bf028f92861745a03a222cbc7b6506bfd2) Thanks [@yangfuhai](https://github.com/yangfuhai)! - v1.0.2

- Updated dependencies [[`806e81b`](https://github.com/tinyflow-ai/tinyflow/commit/806e81bf028f92861745a03a222cbc7b6506bfd2)]:
    - @tinyflow-ai/ui@1.0.2

## 1.0.1

### Patch Changes

- [`9dc676d`](https://github.com/tinyflow-ai/tinyflow/commit/9dc676d46ccf26d1801487716b0709f89ba3a0d1) Thanks [@yangfuhai](https://github.com/yangfuhai)! - v1.0.1

- Updated dependencies [[`9dc676d`](https://github.com/tinyflow-ai/tinyflow/commit/9dc676d46ccf26d1801487716b0709f89ba3a0d1)]:
    - @tinyflow-ai/ui@1.0.1

## 1.0.0

### Major Changes

- [`e4f94e5`](https://github.com/tinyflow-ai/tinyflow/commit/e4f94e5d4611253e6432e5d91e582f5af7210fd6) Thanks [@yangfuhai](https://github.com/yangfuhai)! - v1.0.0

### Patch Changes

- Updated dependencies [[`e4f94e5`](https://github.com/tinyflow-ai/tinyflow/commit/e4f94e5d4611253e6432e5d91e582f5af7210fd6)]:
    - @tinyflow-ai/ui@1.0.0

## 0.2.4

### Patch Changes

- [`97c7cbd`](https://github.com/tinyflow-ai/tinyflow/commit/97c7cbdaa17cb465883389436ea129fed77b7137) Thanks [@yangfuhai](https://github.com/yangfuhai)! - fixed issues

- Updated dependencies [[`97c7cbd`](https://github.com/tinyflow-ai/tinyflow/commit/97c7cbdaa17cb465883389436ea129fed77b7137)]:
    - @tinyflow-ai/ui@0.2.4

## 0.2.3

### Patch Changes

- [`69b50e0`](https://github.com/tinyflow-ai/tinyflow/commit/69b50e0cdcacac87ce15175b712a0bd4f59a5144) Thanks [@yangfuhai](https://github.com/yangfuhai)! - add loop execute options

- Updated dependencies [[`69b50e0`](https://github.com/tinyflow-ai/tinyflow/commit/69b50e0cdcacac87ce15175b712a0bd4f59a5144)]:
    - @tinyflow-ai/ui@0.2.3

## 0.2.2

### Patch Changes

- [`50bde81`](https://github.com/tinyflow-ai/tinyflow/commit/50bde81fe0ce5723e52632ba8a60f5452ff79804) Thanks [@yangfuhai](https://github.com/yangfuhai)! - fixed loopNode issues

- Updated dependencies [[`50bde81`](https://github.com/tinyflow-ai/tinyflow/commit/50bde81fe0ce5723e52632ba8a60f5452ff79804)]:
    - @tinyflow-ai/ui@0.2.2

## 0.2.1

### Patch Changes

- [`e5756d6`](https://github.com/tinyflow-ai/tinyflow/commit/e5756d639ecf772b67e2176a0cf5f0bb3c3e11f9) Thanks [@yangfuhai](https://github.com/yangfuhai)! - add new features

- Updated dependencies [[`e5756d6`](https://github.com/tinyflow-ai/tinyflow/commit/e5756d639ecf772b67e2176a0cf5f0bb3c3e11f9)]:
    - @tinyflow-ai/ui@0.2.1

## 0.2.0

### Minor Changes

- [`8e5a712`](https://github.com/tinyflow-ai/tinyflow/commit/8e5a7127690b2cb95aad4bd5c77017b67be7ab30) Thanks [@yangfuhai](https://github.com/yangfuhai)! - 修复 LoopNode 的循环参数配置不正确的问题

### Patch Changes

- Updated dependencies [[`8e5a712`](https://github.com/tinyflow-ai/tinyflow/commit/8e5a7127690b2cb95aad4bd5c77017b67be7ab30)]:
    - @tinyflow-ai/ui@0.2.0

## 0.1.10

### Patch Changes

- [`ba531dd`](https://github.com/tinyflow-ai/tinyflow/commit/ba531ddccb4a14daad98d33aaf25fd67ebc0bfec) Thanks [@yangfuhai](https://github.com/yangfuhai)! - 优化 LoopNode 循环节点

- Updated dependencies [[`ba531dd`](https://github.com/tinyflow-ai/tinyflow/commit/ba531ddccb4a14daad98d33aaf25fd67ebc0bfec)]:
    - @tinyflow-ai/ui@0.1.10

## 0.1.9

### Patch Changes

- [`0a45ed5`](https://github.com/tinyflow-ai/tinyflow/commit/0a45ed5fee40958fd4f17e96281246dff669fac9) Thanks [@yangfuhai](https://github.com/yangfuhai)! - 自定义节点新增 chosen 组件配置

- Updated dependencies [[`0a45ed5`](https://github.com/tinyflow-ai/tinyflow/commit/0a45ed5fee40958fd4f17e96281246dff669fac9)]:
    - @tinyflow-ai/ui@0.1.9

## 0.1.8

### Patch Changes

- [`93e37cd`](https://github.com/tinyflow-ai/tinyflow/commit/93e37cde1549b992769cf1b65677c43ee6f19e22) Thanks [@yangfuhai](https://github.com/yangfuhai)! - fixed issues

- Updated dependencies [[`93e37cd`](https://github.com/tinyflow-ai/tinyflow/commit/93e37cde1549b992769cf1b65677c43ee6f19e22)]:
    - @tinyflow-ai/ui@0.1.8

## 0.1.7

### Patch Changes

- [`5107152`](https://github.com/tinyflow-ai/tinyflow/commit/51071527ec1d47cac32f5f0432cb838eb99866d4) Thanks [@yangfuhai](https://github.com/yangfuhai)! - fixed issues

- Updated dependencies [[`5107152`](https://github.com/tinyflow-ai/tinyflow/commit/51071527ec1d47cac32f5f0432cb838eb99866d4)]:
    - @tinyflow-ai/ui@0.1.7

## 0.1.6

### Patch Changes

- [`87d874a`](https://github.com/tinyflow-ai/tinyflow/commit/87d874a8a1ad0086936d5017c9b6a1738d2e7d39) Thanks [@yangfuhai](https://github.com/yangfuhai)! - fixed issues

- Updated dependencies [[`87d874a`](https://github.com/tinyflow-ai/tinyflow/commit/87d874a8a1ad0086936d5017c9b6a1738d2e7d39)]:
    - @tinyflow-ai/ui@0.1.6

## 0.1.5

### Patch Changes

- [`472f6de`](https://github.com/tinyflow-ai/tinyflow/commit/472f6de306ea316809a9859fad71917c59696b1a) Thanks [@yangfuhai](https://github.com/yangfuhai)! - feat: add async configuration

- Updated dependencies [[`472f6de`](https://github.com/tinyflow-ai/tinyflow/commit/472f6de306ea316809a9859fad71917c59696b1a)]:
    - @tinyflow-ai/ui@0.1.5

## 0.1.4

### Patch Changes

- [`147783d`](https://github.com/tinyflow-ai/tinyflow/commit/147783d1a8e5ff30f2ca080e5bcbb5a580d6a941) Thanks [@yangfuhai](https://github.com/yangfuhai)! - feat: add async configuration

- Updated dependencies [[`147783d`](https://github.com/tinyflow-ai/tinyflow/commit/147783d1a8e5ff30f2ca080e5bcbb5a580d6a941)]:
    - @tinyflow-ai/ui@0.1.4

## 0.1.3

### Patch Changes

- [`1f357ed`](https://github.com/tinyflow-ai/tinyflow/commit/1f357edb099982bf6cd62270e76a405a9951169e) Thanks [@yangfuhai](https://github.com/yangfuhai)! - add custom parameters and output defs configuration forCustomNode

- Updated dependencies [[`1f357ed`](https://github.com/tinyflow-ai/tinyflow/commit/1f357edb099982bf6cd62270e76a405a9951169e)]:
    - @tinyflow-ai/ui@0.1.3

## 0.1.2

### Patch Changes

- [`ff4ab77`](https://github.com/tinyflow-ai/tinyflow/commit/ff4ab773cee3d985e7251f2beecfcd47d53314f7) Thanks [@yangfuhai](https://github.com/yangfuhai)! - add custom parameters and outputDefs configuration for CustomNode

- Updated dependencies [[`ff4ab77`](https://github.com/tinyflow-ai/tinyflow/commit/ff4ab773cee3d985e7251f2beecfcd47d53314f7)]:
    - @tinyflow-ai/ui@0.1.2

## 0.1.1

### Patch Changes

- [`0d165f9`](https://github.com/tinyflow-ai/tinyflow/commit/0d165f9ab75959a7ad19590d008caa480b8e18db) Thanks [@yangfuhai](https://github.com/yangfuhai)! - - fix: fixed the parameter description can not show
    - feat: add "nowheel" class name to the textarea
- Updated dependencies [[`0d165f9`](https://github.com/tinyflow-ai/tinyflow/commit/0d165f9ab75959a7ad19590d008caa480b8e18db)]:
    - @tinyflow-ai/ui@0.1.1

## 0.1.0

### Minor Changes

- [`fa467a1`](https://github.com/tinyflow-ai/tinyflow/commit/fa467a1bd6d6d435d4647d6763c6059f73d685cd) Thanks [@yangfuhai](https://github.com/yangfuhai)! - add custom nodes to the base tab

### Patch Changes

- Updated dependencies [[`fa467a1`](https://github.com/tinyflow-ai/tinyflow/commit/fa467a1bd6d6d435d4647d6763c6059f73d685cd)]:
    - @tinyflow-ai/ui@0.1.0

## 0.0.16

### Patch Changes

- [`f590e99`](https://github.com/tinyflow-ai/tinyflow/commit/f590e991d354a497a181d9b7140b436e2640929c) Thanks [@yangfuhai](https://github.com/yangfuhai)! - add node condition

- Updated dependencies [[`f590e99`](https://github.com/tinyflow-ai/tinyflow/commit/f590e991d354a497a181d9b7140b436e2640929c)]:
    - @tinyflow-ai/ui@0.0.16

## 0.0.15

### Patch Changes

- [`2b6b889`](https://github.com/tinyflow-ai/tinyflow/commit/2b6b889a2b946a55dc8b5b85d601e8a76e917cca) Thanks [@yangfuhai](https://github.com/yangfuhai)! - add condition setting in edge

- Updated dependencies [[`2b6b889`](https://github.com/tinyflow-ai/tinyflow/commit/2b6b889a2b946a55dc8b5b85d601e8a76e917cca)]:
    - @tinyflow-ai/ui@0.0.15

## 0.0.14

### Patch Changes

- [`f63106c`](https://github.com/tinyflow-ai/tinyflow/commit/f63106c921740b6f843b7cda9767c96a71f94f0b) Thanks [@yangfuhai](https://github.com/yangfuhai)! - v0.0.14

- Updated dependencies [[`f63106c`](https://github.com/tinyflow-ai/tinyflow/commit/f63106c921740b6f843b7cda9767c96a71f94f0b)]:
    - @tinyflow-ai/ui@0.0.14

## 0.0.13

### Patch Changes

- 4bb46a5: optimize and fixed issues
- Updated dependencies [4bb46a5]
    - @tinyflow-ai/ui@0.0.13

## 0.0.12

### Patch Changes

- 73967cf: - feat: LLMNode add outType config
    - fix: OutputDefItem can not show 'defaultValue'
- Updated dependencies [73967cf]
    - @tinyflow-ai/ui@0.0.12

## 0.1.0

### Minor Changes

- cbbe5cc: - feat: CustomNode add more configs: "rootClass" "rootStyle" "parametersEnable" "outputDefsEnable"
    - style: optimize styles
    - chore: upgrade svelte
- 97c9c75: - feat: CustomNode add more configs: "rootClass" "rootStyle" "parameters
    - style: optimize styles
    - feat: add onNodeExecute config support
    - refactor: optimize RefParameterItem.svelte
    - fix: fixed textarea can not show the value property
    - chore: upgrade svelte

### Patch Changes

- Updated dependencies [cbbe5cc]
- Updated dependencies [97c9c75]
    - @tinyflow-ai/ui@0.1.0

## 0.0.10

### Patch Changes

- 7f8a6e2: optimize styles
- Updated dependencies [7f8a6e2]
    - @tinyflow-ai/ui@0.0.10

## 0.0.9

### Patch Changes

- 6557dd1: - feat: add custom node support
    - feat: add auto publish workflow
- Updated dependencies [6557dd1]
    - @tinyflow-ai/ui@0.0.9

## 0.0.8

### Patch Changes

- 001ed00: - feat: add custom node support
- Updated dependencies [001ed00]
    - @tinyflow-ai/ui@0.0.8

## 0.0.7

### Patch Changes

- v0.0.7
- Updated dependencies
    - @tinyflow-ai/ui@0.0.7

## 0.0.6

### Patch Changes

- feat: add vue demos

    feat: add react demos

- Updated dependencies
    - @tinyflow-ai/ui@0.0.6

## 0.0.5

### Patch Changes

- v0.0.5

    feat: add Knowledge Node
    feat: add Loop Node
    feat: add Search Engine Node
    feat: add Svelte package
    feat: add umd export

- Updated dependencies
    - @tinyflow-ai/ui@0.0.5

## 0.0.4

### Patch Changes

-   - add @tinyflow-ai/react
    - add @tinyflow-ai/vue
    - add HttpNode
    - add tinyflow.destroy() method
    - add tinyflow.setData() method
- Updated dependencies
    - @tinyflow-ai/ui@0.0.4

## 0.0.3

### Patch Changes

- build: optimize package.json
- build: add gen source map files
- refactor: optimize Toolbar.svelte
- feat: add code node
- feat: add TemplateNode

## 0.0.2

### Patch Changes

- db59410: v0.0.2
- feat: NodeWrapper add "allowExecute" "allowCopy" "allowDelete" config support
- feat: add copyNode support
- refactor: use 'children' to replace slot
- fix: fixed build error: No known conditions for "./elements"
- fix: fixed build types not correct
