(function(sr,Jt){typeof exports=="object"&&typeof module<"u"?Jt(exports,require("react")):typeof define=="function"&&define.amd?define(["exports","react"],Jt):(sr=typeof globalThis<"u"?globalThis:sr||self,Jt(sr.tinyflow={},sr.require$$0))})(this,function(sr,Jt){"use strict";var Wo={exports:{}},to={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ha;function Df(){if(Ha)return to;Ha=1;var e=Jt,t=Symbol.for("react.element"),n=Symbol.for("react.fragment"),r=Object.prototype.hasOwnProperty,o=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,i={key:!0,ref:!0,__self:!0,__source:!0};function s(a,l,u){var d,p={},f=null,g=null;u!==void 0&&(f=""+u),l.key!==void 0&&(f=""+l.key),l.ref!==void 0&&(g=l.ref);for(d in l)r.call(l,d)&&!i.hasOwnProperty(d)&&(p[d]=l[d]);if(a&&a.defaultProps)for(d in l=a.defaultProps,l)p[d]===void 0&&(p[d]=l[d]);return{$$typeof:t,type:a,key:f,ref:g,props:p,_owner:o.current}}return to.Fragment=n,to.jsx=s,to.jsxs=s,to}var no={};/**
 * @license React
 * react-jsx-runtime.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Va;function Of(){return Va||(Va=1,process.env.NODE_ENV!=="production"&&function(){var e=Jt,t=Symbol.for("react.element"),n=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),s=Symbol.for("react.provider"),a=Symbol.for("react.context"),l=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),d=Symbol.for("react.suspense_list"),p=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),g=Symbol.for("react.offscreen"),h=Symbol.iterator,v="@@iterator";function w(z){if(z===null||typeof z!="object")return null;var ne=h&&z[h]||z[v];return typeof ne=="function"?ne:null}var b=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function x(z){{for(var ne=arguments.length,pe=new Array(ne>1?ne-1:0),_e=1;_e<ne;_e++)pe[_e-1]=arguments[_e];$("error",z,pe)}}function $(z,ne,pe){{var _e=b.ReactDebugCurrentFrame,Ie=_e.getStackAddendum();Ie!==""&&(ne+="%s",pe=pe.concat([Ie]));var je=pe.map(function(Ve){return String(Ve)});je.unshift("Warning: "+ne),Function.prototype.apply.call(console[z],console,je)}}var S=!1,E=!1,D=!1,O=!1,q=!1,K;K=Symbol.for("react.module.reference");function J(z){return!!(typeof z=="string"||typeof z=="function"||z===r||z===i||q||z===o||z===u||z===d||O||z===g||S||E||D||typeof z=="object"&&z!==null&&(z.$$typeof===f||z.$$typeof===p||z.$$typeof===s||z.$$typeof===a||z.$$typeof===l||z.$$typeof===K||z.getModuleId!==void 0))}function A(z,ne,pe){var _e=z.displayName;if(_e)return _e;var Ie=ne.displayName||ne.name||"";return Ie!==""?pe+"("+Ie+")":pe}function _(z){return z.displayName||"Context"}function k(z){if(z==null)return null;if(typeof z.tag=="number"&&x("Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue."),typeof z=="function")return z.displayName||z.name||null;if(typeof z=="string")return z;switch(z){case r:return"Fragment";case n:return"Portal";case i:return"Profiler";case o:return"StrictMode";case u:return"Suspense";case d:return"SuspenseList"}if(typeof z=="object")switch(z.$$typeof){case a:var ne=z;return _(ne)+".Consumer";case s:var pe=z;return _(pe._context)+".Provider";case l:return A(z,z.render,"ForwardRef");case p:var _e=z.displayName||null;return _e!==null?_e:k(z.type)||"Memo";case f:{var Ie=z,je=Ie._payload,Ve=Ie._init;try{return k(Ve(je))}catch{return null}}}return null}var C=Object.assign,N=0,P,H,Z,Y,M,X,te;function oe(){}oe.__reactDisabledLog=!0;function j(){{if(N===0){P=console.log,H=console.info,Z=console.warn,Y=console.error,M=console.group,X=console.groupCollapsed,te=console.groupEnd;var z={configurable:!0,enumerable:!0,value:oe,writable:!0};Object.defineProperties(console,{info:z,log:z,warn:z,error:z,group:z,groupCollapsed:z,groupEnd:z})}N++}}function G(){{if(N--,N===0){var z={configurable:!0,enumerable:!0,writable:!0};Object.defineProperties(console,{log:C({},z,{value:P}),info:C({},z,{value:H}),warn:C({},z,{value:Z}),error:C({},z,{value:Y}),group:C({},z,{value:M}),groupCollapsed:C({},z,{value:X}),groupEnd:C({},z,{value:te})})}N<0&&x("disabledDepth fell below zero. This is a bug in React. Please file an issue.")}}var F=b.ReactCurrentDispatcher,se;function W(z,ne,pe){{if(se===void 0)try{throw Error()}catch(Ie){var _e=Ie.stack.trim().match(/\n( *(at )?)/);se=_e&&_e[1]||""}return`
`+se+z}}var ye=!1,xe;{var ie=typeof WeakMap=="function"?WeakMap:Map;xe=new ie}function ee(z,ne){if(!z||ye)return"";{var pe=xe.get(z);if(pe!==void 0)return pe}var _e;ye=!0;var Ie=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var je;je=F.current,F.current=null,j();try{if(ne){var Ve=function(){throw Error()};if(Object.defineProperty(Ve.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(Ve,[])}catch(zt){_e=zt}Reflect.construct(z,[],Ve)}else{try{Ve.call()}catch(zt){_e=zt}z.call(Ve.prototype)}}else{try{throw Error()}catch(zt){_e=zt}z()}}catch(zt){if(zt&&_e&&typeof zt.stack=="string"){for(var Le=zt.stack.split(`
`),Tt=_e.stack.split(`
`),rt=Le.length-1,at=Tt.length-1;rt>=1&&at>=0&&Le[rt]!==Tt[at];)at--;for(;rt>=1&&at>=0;rt--,at--)if(Le[rt]!==Tt[at]){if(rt!==1||at!==1)do if(rt--,at--,at<0||Le[rt]!==Tt[at]){var Ut=`
`+Le[rt].replace(" at new "," at ");return z.displayName&&Ut.includes("<anonymous>")&&(Ut=Ut.replace("<anonymous>",z.displayName)),typeof z=="function"&&xe.set(z,Ut),Ut}while(rt>=1&&at>=0);break}}}finally{ye=!1,F.current=je,G(),Error.prepareStackTrace=Ie}var eo=z?z.displayName||z.name:"",Sr=eo?W(eo):"";return typeof z=="function"&&xe.set(z,Sr),Sr}function re(z,ne,pe){return ee(z,!1)}function ge(z){var ne=z.prototype;return!!(ne&&ne.isReactComponent)}function he(z,ne,pe){if(z==null)return"";if(typeof z=="function")return ee(z,ge(z));if(typeof z=="string")return W(z);switch(z){case u:return W("Suspense");case d:return W("SuspenseList")}if(typeof z=="object")switch(z.$$typeof){case l:return re(z.render);case p:return he(z.type,ne,pe);case f:{var _e=z,Ie=_e._payload,je=_e._init;try{return he(je(Ie),ne,pe)}catch{}}}return""}var le=Object.prototype.hasOwnProperty,Te={},ke=b.ReactDebugCurrentFrame;function B(z){if(z){var ne=z._owner,pe=he(z.type,z._source,ne?ne.type:null);ke.setExtraStackFrame(pe)}else ke.setExtraStackFrame(null)}function ct(z,ne,pe,_e,Ie){{var je=Function.call.bind(le);for(var Ve in z)if(je(z,Ve)){var Le=void 0;try{if(typeof z[Ve]!="function"){var Tt=Error((_e||"React class")+": "+pe+" type `"+Ve+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof z[Ve]+"`.This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.");throw Tt.name="Invariant Violation",Tt}Le=z[Ve](ne,Ve,_e,pe,null,"SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED")}catch(rt){Le=rt}Le&&!(Le instanceof Error)&&(B(Ie),x("%s: type specification of %s `%s` is invalid; the type checker function must return `null` or an `Error` but returned a %s. You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument).",_e||"React class",pe,Ve,typeof Le),B(null)),Le instanceof Error&&!(Le.message in Te)&&(Te[Le.message]=!0,B(Ie),x("Failed %s type: %s",pe,Le.message),B(null))}}}var Ae=Array.isArray;function Ze(z){return Ae(z)}function Re(z){{var ne=typeof Symbol=="function"&&Symbol.toStringTag,pe=ne&&z[Symbol.toStringTag]||z.constructor.name||"Object";return pe}}function dt(z){try{return it(z),!1}catch{return!0}}function it(z){return""+z}function Pt(z){if(dt(z))return x("The provided key is an unsupported type %s. This value must be coerced to a string before before using it here.",Re(z)),it(z)}var Be=b.ReactCurrentOwner,Qe={key:!0,ref:!0,__self:!0,__source:!0},ve,Ye;function ft(z){if(le.call(z,"ref")){var ne=Object.getOwnPropertyDescriptor(z,"ref").get;if(ne&&ne.isReactWarning)return!1}return z.ref!==void 0}function st(z){if(le.call(z,"key")){var ne=Object.getOwnPropertyDescriptor(z,"key").get;if(ne&&ne.isReactWarning)return!1}return z.key!==void 0}function Nt(z,ne){typeof z.ref=="string"&&Be.current}function Je(z,ne){{var pe=function(){ve||(ve=!0,x("%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",ne))};pe.isReactWarning=!0,Object.defineProperty(z,"key",{get:pe,configurable:!0})}}function kt(z,ne){{var pe=function(){Ye||(Ye=!0,x("%s: `ref` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://reactjs.org/link/special-props)",ne))};pe.isReactWarning=!0,Object.defineProperty(z,"ref",{get:pe,configurable:!0})}}var Gt=function(z,ne,pe,_e,Ie,je,Ve){var Le={$$typeof:t,type:z,key:ne,ref:pe,props:Ve,_owner:je};return Le._store={},Object.defineProperty(Le._store,"validated",{configurable:!1,enumerable:!1,writable:!0,value:!1}),Object.defineProperty(Le,"_self",{configurable:!1,enumerable:!1,writable:!1,value:_e}),Object.defineProperty(Le,"_source",{configurable:!1,enumerable:!1,writable:!1,value:Ie}),Object.freeze&&(Object.freeze(Le.props),Object.freeze(Le)),Le};function Nn(z,ne,pe,_e,Ie){{var je,Ve={},Le=null,Tt=null;pe!==void 0&&(Pt(pe),Le=""+pe),st(ne)&&(Pt(ne.key),Le=""+ne.key),ft(ne)&&(Tt=ne.ref,Nt(ne,Ie));for(je in ne)le.call(ne,je)&&!Qe.hasOwnProperty(je)&&(Ve[je]=ne[je]);if(z&&z.defaultProps){var rt=z.defaultProps;for(je in rt)Ve[je]===void 0&&(Ve[je]=rt[je])}if(Le||Tt){var at=typeof z=="function"?z.displayName||z.name||"Unknown":z;Le&&Je(Ve,at),Tt&&kt(Ve,at)}return Gt(z,Le,Tt,Ie,_e,Be.current,Ve)}}var or=b.ReactCurrentOwner,Fo=b.ReactDebugCurrentFrame;function ir(z){if(z){var ne=z._owner,pe=he(z.type,z._source,ne?ne.type:null);Fo.setExtraStackFrame(pe)}else Fo.setExtraStackFrame(null)}var fn;fn=!1;function La(z){return typeof z=="object"&&z!==null&&z.$$typeof===t}function _f(){{if(or.current){var z=k(or.current.type);if(z)return`

Check the render method of \``+z+"`."}return""}}function ax(z){return""}var Sf={};function lx(z){{var ne=_f();if(!ne){var pe=typeof z=="string"?z:z.displayName||z.name;pe&&(ne=`

Check the top-level render call using <`+pe+">.")}return ne}}function Ef(z,ne){{if(!z._store||z._store.validated||z.key!=null)return;z._store.validated=!0;var pe=lx(ne);if(Sf[pe])return;Sf[pe]=!0;var _e="";z&&z._owner&&z._owner!==or.current&&(_e=" It was passed a child from "+k(z._owner.type)+"."),ir(z),x('Each child in a list should have a unique "key" prop.%s%s See https://reactjs.org/link/warning-keys for more information.',pe,_e),ir(null)}}function Pf(z,ne){{if(typeof z!="object")return;if(Ze(z))for(var pe=0;pe<z.length;pe++){var _e=z[pe];La(_e)&&Ef(_e,ne)}else if(La(z))z._store&&(z._store.validated=!0);else if(z){var Ie=w(z);if(typeof Ie=="function"&&Ie!==z.entries)for(var je=Ie.call(z),Ve;!(Ve=je.next()).done;)La(Ve.value)&&Ef(Ve.value,ne)}}}function ux(z){{var ne=z.type;if(ne==null||typeof ne=="string")return;var pe;if(typeof ne=="function")pe=ne.propTypes;else if(typeof ne=="object"&&(ne.$$typeof===l||ne.$$typeof===p))pe=ne.propTypes;else return;if(pe){var _e=k(ne);ct(pe,z.props,"prop",_e,z)}else if(ne.PropTypes!==void 0&&!fn){fn=!0;var Ie=k(ne);x("Component %s declared `PropTypes` instead of `propTypes`. Did you misspell the property assignment?",Ie||"Unknown")}typeof ne.getDefaultProps=="function"&&!ne.getDefaultProps.isReactClassApproved&&x("getDefaultProps is only used on classic React.createClass definitions. Use a static property named `defaultProps` instead.")}}function cx(z){{for(var ne=Object.keys(z.props),pe=0;pe<ne.length;pe++){var _e=ne[pe];if(_e!=="children"&&_e!=="key"){ir(z),x("Invalid prop `%s` supplied to `React.Fragment`. React.Fragment can only have `key` and `children` props.",_e),ir(null);break}}z.ref!==null&&(ir(z),x("Invalid attribute `ref` supplied to `React.Fragment`."),ir(null))}}var Nf={};function Tf(z,ne,pe,_e,Ie,je){{var Ve=J(z);if(!Ve){var Le="";(z===void 0||typeof z=="object"&&z!==null&&Object.keys(z).length===0)&&(Le+=" You likely forgot to export your component from the file it's defined in, or you might have mixed up default and named imports.");var Tt=ax();Tt?Le+=Tt:Le+=_f();var rt;z===null?rt="null":Ze(z)?rt="array":z!==void 0&&z.$$typeof===t?(rt="<"+(k(z.type)||"Unknown")+" />",Le=" Did you accidentally export a JSX literal instead of a component?"):rt=typeof z,x("React.jsx: type is invalid -- expected a string (for built-in components) or a class/function (for composite components) but got: %s.%s",rt,Le)}var at=Nn(z,ne,pe,Ie,je);if(at==null)return at;if(Ve){var Ut=ne.children;if(Ut!==void 0)if(_e)if(Ze(Ut)){for(var eo=0;eo<Ut.length;eo++)Pf(Ut[eo],z);Object.freeze&&Object.freeze(Ut)}else x("React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.");else Pf(Ut,z)}if(le.call(ne,"key")){var Sr=k(z),zt=Object.keys(ne).filter(function(vx){return vx!=="key"}),Ma=zt.length>0?"{key: someKey, "+zt.join(": ..., ")+": ...}":"{key: someKey}";if(!Nf[Sr+Ma]){var hx=zt.length>0?"{"+zt.join(": ..., ")+": ...}":"{}";x(`A props object containing a "key" prop is being spread into JSX:
  let props = %s;
  <%s {...props} />
React keys must be passed directly to JSX without using spread:
  let props = %s;
  <%s key={someKey} {...props} />`,Ma,Sr,hx,Sr),Nf[Sr+Ma]=!0}}return z===r?cx(at):ux(at),at}}function dx(z,ne,pe){return Tf(z,ne,pe,!0)}function fx(z,ne,pe){return Tf(z,ne,pe,!1)}var px=fx,gx=dx;no.Fragment=r,no.jsx=px,no.jsxs=gx}()),no}var za;function Lf(){return za||(za=1,process.env.NODE_ENV==="production"?Wo.exports=Df():Wo.exports=Of()),Wo.exports}var Mf=Lf();const Hf="5";typeof window<"u"&&((window.__svelte??={}).v??=new Set).add(Hf);const Ji=1,Qi=2,Aa=4,Vf=8,zf=16,Af=1,Rf=2,Ra=4,If=8,qf=16,Ia=1,Zf=2,qa="[",es="[!",ts="]",ar={},pt=Symbol(),Bf="http://www.w3.org/1999/xhtml",Kf="http://www.w3.org/2000/svg",jf="@attach",Yf=!1;var ro=Array.isArray,Xf=Array.prototype.indexOf,ns=Array.from,Go=Object.keys,Er=Object.defineProperty,Tn=Object.getOwnPropertyDescriptor,Za=Object.getOwnPropertyDescriptors,Ba=Object.prototype,Ff=Array.prototype,Uo=Object.getPrototypeOf,Ka=Object.isExtensible;function oo(e){return typeof e=="function"}const ht=()=>{};function Wf(e){return e()}function Jo(e){for(var t=0;t<e.length;t++)e[t]()}function Gf(){var e,t,n=new Promise((r,o)=>{e=r,t=o});return{promise:n,resolve:e,reject:t}}function _t(e,t,n=!1){return e===void 0?n?t():t:e}function io(e,t){if(Array.isArray(e))return e;if(t===void 0||!(Symbol.iterator in e))return Array.from(e);const n=[];for(const r of e)if(n.push(r),n.length===t)break;return n}const At=2,rs=4,Qo=8,Pr=16,Dn=32,jn=64,ja=128,Kt=256,ei=512,bt=1024,jt=2048,Yn=4096,Qt=8192,lr=16384,os=32768,Nr=65536,Ya=1<<17,Uf=1<<18,Tr=1<<19,Xa=1<<20,is=1<<21,ss=1<<22,ur=1<<23,pn=Symbol("$state"),as=Symbol("legacy props"),Jf=Symbol(""),ls=new class extends Error{name="StaleReactionError";message="The reaction that called `getAbortSignal()` was re-run or destroyed"},Qf=1,us=3,Dr=8;function ep(){throw new Error("https://svelte.dev/e/await_outside_boundary")}function cs(e){throw new Error("https://svelte.dev/e/lifecycle_outside_component")}function tp(){throw new Error("https://svelte.dev/e/async_derived_orphan")}function np(e){throw new Error("https://svelte.dev/e/effect_in_teardown")}function rp(){throw new Error("https://svelte.dev/e/effect_in_unowned_derived")}function op(e){throw new Error("https://svelte.dev/e/effect_orphan")}function ip(){throw new Error("https://svelte.dev/e/effect_update_depth_exceeded")}function sp(){throw new Error("https://svelte.dev/e/hydration_failed")}function ap(e){throw new Error("https://svelte.dev/e/props_invalid_value")}function lp(){throw new Error("https://svelte.dev/e/state_descriptors_fixed")}function up(){throw new Error("https://svelte.dev/e/state_prototype_fixed")}function cp(){throw new Error("https://svelte.dev/e/state_unsafe_mutation")}function so(e){console.warn("https://svelte.dev/e/hydration_mismatch")}function dp(){console.warn("https://svelte.dev/e/select_multiple_invalid_value")}let $e=!1;function Rt(e){$e=e}let Oe;function xt(e){if(e===null)throw so(),ar;return Oe=e}function On(){return xt(gn(Oe))}function R(e){if($e){if(gn(Oe)!==null)throw so(),ar;Oe=e}}function we(e=1){if($e){for(var t=e,n=Oe;t--;)n=gn(n);Oe=n}}function ds(){for(var e=0,t=Oe;;){if(t.nodeType===Dr){var n=t.data;if(n===ts){if(e===0)return t;e-=1}else(n===qa||n===es)&&(e+=1)}var r=gn(t);t.remove(),t=r}}function Fa(e){if(!e||e.nodeType!==Dr)throw so(),ar;return e.data}function Wa(e){return e===this.v}function Ga(e,t){return e!=e?t==t:e!==t||e!==null&&typeof e=="object"||typeof e=="function"}function Ua(e){return!Ga(e,this.v)}let Or=!1,fp=!1;function pp(){Or=!0}const gp=[];function Ja(e,t=!1,n=!1){return ti(e,new Map,"",gp,null,n)}function ti(e,t,n,r,o=null,i=!1){if(typeof e=="object"&&e!==null){var s=t.get(e);if(s!==void 0)return s;if(e instanceof Map)return new Map(e);if(e instanceof Set)return new Set(e);if(ro(e)){var a=Array(e.length);t.set(e,a),o!==null&&t.set(o,a);for(var l=0;l<e.length;l+=1){var u=e[l];l in e&&(a[l]=ti(u,t,n,r,null,i))}return a}if(Uo(e)===Ba){a={},t.set(e,a),o!==null&&t.set(o,a);for(var d in e)a[d]=ti(e[d],t,n,r,null,i);return a}if(e instanceof Date)return structuredClone(e);if(typeof e.toJSON=="function"&&!i)return ti(e.toJSON(),t,n,r,e)}if(e instanceof EventTarget)return e;try{return structuredClone(e)}catch{return e}}let Ge=null;function ni(e){Ge=e}function Xn(e){return Qa().get(e)}function Lr(e,t){return Qa().set(e,t),t}function de(e,t=!1,n){Ge={p:Ge,c:null,e:null,s:e,x:null,l:Or&&!t?{s:null,u:null,$:[]}:null}}function fe(e){var t=Ge,n=t.e;if(n!==null){t.e=null;for(var r of n)Cl(r)}return e!==void 0&&(t.x=e),Ge=t.p,e??{}}function ao(){return!Or||Ge!==null&&Ge.l===null}function Qa(e){return Ge===null&&cs(),Ge.c??=new Map(hp(Ge)||void 0)}function hp(e){let t=e.p;for(;t!==null;){const n=t.c;if(n!==null)return n;t=t.p}return null}const vp=new WeakMap;function mp(e){var t=Pe;if(t===null)return He.f|=ur,e;if((t.f&os)===0){if((t.f&ja)===0)throw!t.parent&&e instanceof Error&&el(e),e;t.b.error(e)}else fs(e,t)}function fs(e,t){for(;t!==null;){if((t.f&ja)!==0)try{t.b.error(e);return}catch(n){e=n}t=t.parent}throw e instanceof Error&&el(e),e}function el(e){const t=vp.get(e);t&&(Er(e,"message",{value:t.message}),Er(e,"stack",{value:t.stack}))}const yp=typeof requestIdleCallback>"u"?e=>setTimeout(e,1):requestIdleCallback;let lo=[],uo=[];function tl(){var e=lo;lo=[],Jo(e)}function nl(){var e=uo;uo=[],Jo(e)}function co(e){lo.length===0&&queueMicrotask(tl),lo.push(e)}function wp(e){uo.length===0&&yp(nl),uo.push(e)}function bp(){lo.length>0&&tl(),uo.length>0&&nl()}function xp(e){let t=0,n=pr(0),r;return()=>{Op()&&(c(n),ho(()=>(t===0&&(r=vt(()=>e(()=>go(n)))),t+=1,()=>{co(()=>{t-=1,t===0&&(r?.(),r=void 0,go(n))})})))}}function Cp(){for(var e=Pe.b;e!==null&&!e.has_pending_snippet();)e=e.parent;return e===null&&ep(),e}function fo(e){var t=At|jt,n=He!==null&&(He.f&At)!==0?He:null;return Pe===null||n!==null&&(n.f&Kt)!==0?t|=Kt:Pe.f|=Tr,{ctx:Ge,deps:null,effects:null,equals:Wa,f:t,fn:e,reactions:null,rv:0,v:pt,wv:0,parent:n??Pe,ac:null}}function $p(e,t){let n=Pe;n===null&&tp();var r=n.b,o=void 0,i=pr(pt),s=null,a=!He;return Mp(()=>{try{var l=e();s&&Promise.resolve(l).catch(()=>{})}catch(g){l=Promise.reject(g)}var u=()=>l;o=s?.then(u,u)??Promise.resolve(l),s=o;var d=gt,p=r.pending;a&&(r.update_pending_count(1),p||d.increment());const f=(g,h=void 0)=>{s=null,p||d.activate(),h?h!==ls&&(i.f|=ur,po(i,h)):((i.f&ur)!==0&&(i.f^=ur),po(i,g)),a&&(r.update_pending_count(-1),p||d.decrement()),sl()};if(o.then(f,g=>f(null,g||"unknown")),d)return()=>{queueMicrotask(()=>d.neuter())}}),new Promise(l=>{function u(d){function p(){d===o?l(i):u(o)}d.then(p,p)}u(o)})}function T(e){const t=fo(e);return Ol(t),t}function ps(e){const t=fo(e);return t.equals=Ua,t}function rl(e){var t=e.effects;if(t!==null){e.effects=null;for(var n=0;n<t.length;n+=1)St(t[n])}}function kp(e){for(var t=e.parent;t!==null;){if((t.f&At)===0)return t;t=t.parent}return null}function gs(e){var t,n=Pe;vn(kp(e));try{rl(e),t=zl(e)}finally{vn(n)}return t}function ol(e){var t=gs(e);if(e.equals(t)||(e.v=t,e.wv=Hl()),!hr){var n=(Wn||(e.f&Kt)!==0)&&e.deps!==null?Yn:bt;Ot(e,n)}}function il(e,t,n){const r=ao()?fo:ps;if(t.length===0){n(e.map(r));return}var o=gt,i=Pe,s=_p(),a=Cp();Promise.all(t.map(l=>$p(l))).then(l=>{o?.activate(),s();try{n([...e.map(r),...l])}catch(u){(i.f&lr)===0&&fs(u,i)}o?.deactivate(),sl()}).catch(l=>{a.error(l)})}function _p(){var e=Pe,t=He,n=Ge,r=gt;return function(){vn(e),hn(t),ni(n),r?.activate()}}function sl(){vn(null),hn(null),ni(null)}const hs=new Set;let gt=null,al=new Set,ri=[];function ll(){const e=ri.shift();ri.length>0&&queueMicrotask(ll),e()}let cr=[],oi=null,vs=!1,ii=!1;class dr{current=new Map;#t=new Map;#e=new Set;#n=0;#o=null;#c=!1;#i=[];#a=[];#s=[];#r=[];#l=[];#d=[];#f=[];skipped_effects=new Set;process(t){cr=[];for(const o of t)this.#g(o);if(this.#i.length===0&&this.#n===0){this.#p();var n=this.#s,r=this.#r;this.#s=[],this.#r=[],this.#l=[],gt=null,cl(n),cl(r),gt===null?gt=this:hs.delete(this),this.#o?.resolve()}else this.#u(this.#s),this.#u(this.#r),this.#u(this.#l);for(const o of this.#i)zr(o);for(const o of this.#a)zr(o);this.#i=[],this.#a=[]}#g(t){t.f^=bt;for(var n=t.first;n!==null;){var r=n.f,o=(r&(Dn|jn))!==0,i=o&&(r&bt)!==0,s=i||(r&Qt)!==0||this.skipped_effects.has(n);if(!s&&n.fn!==null){if(o)n.f^=bt;else if((r&rs)!==0)this.#r.push(n);else if((r&bt)===0)if((r&ss)!==0){var a=n.b?.pending?this.#a:this.#i;a.push(n)}else ai(n)&&((n.f&Pr)!==0&&this.#l.push(n),zr(n));var l=n.first;if(l!==null){n=l;continue}}var u=n.parent;for(n=n.next;n===null&&u!==null;)n=u.next,u=u.parent}}#u(t){for(const n of t)((n.f&jt)!==0?this.#d:this.#f).push(n),Ot(n,bt);t.length=0}capture(t,n){this.#t.has(t)||this.#t.set(t,n),this.current.set(t,t.v)}activate(){gt=this}deactivate(){gt=null;for(const t of al)if(al.delete(t),t(),gt!==null)break}neuter(){this.#c=!0}flush(){cr.length>0?ul():this.#p(),gt===this&&(this.#n===0&&hs.delete(this),this.deactivate())}#p(){if(!this.#c)for(const t of this.#e)t();this.#e.clear()}increment(){this.#n+=1}decrement(){if(this.#n-=1,this.#n===0){for(const t of this.#d)Ot(t,jt),Mr(t);for(const t of this.#f)Ot(t,Yn),Mr(t);this.#s=[],this.#r=[],this.flush()}else this.deactivate()}add_callback(t){this.#e.add(t)}settled(){return(this.#o??=Gf()).promise}static ensure(){if(gt===null){const t=gt=new dr;hs.add(gt),ii||dr.enqueue(()=>{gt===t&&t.flush()})}return gt}static enqueue(t){ri.length===0&&queueMicrotask(ll),ri.unshift(t)}}function m(e){var t=ii;ii=!0;try{for(var n;;){if(bp(),cr.length===0&&(gt?.flush(),cr.length===0))return oi=null,n;ul()}}finally{ii=t}}function ul(){var e=Vr;vs=!0;try{var t=0;for(Tl(!0);cr.length>0;){var n=dr.ensure();if(t++>1e3){var r,o;Sp()}n.process(cr),Fn.clear()}}finally{vs=!1,Tl(e),oi=null}}function Sp(){try{ip()}catch(e){fs(e,oi)}}let fr=null;function cl(e){var t=e.length;if(t!==0){for(var n=0;n<t;){var r=e[n++];if((r.f&(lr|Qt))===0&&ai(r)&&(fr=[],zr(r),r.deps===null&&r.first===null&&r.nodes_start===null&&(r.teardown===null&&r.ac===null?El(r):r.fn=null),fr?.length>0)){Fn.clear();for(const o of fr)zr(o);fr=[]}}fr=null}}function Mr(e){for(var t=oi=e;t.parent!==null;){t=t.parent;var n=t.f;if(vs&&t===Pe&&(n&Pr)!==0)return;if((n&(jn|Dn))!==0){if((n&bt)===0)return;t.f^=bt}}cr.push(t)}const Fn=new Map;function pr(e,t){var n={f:0,v:e,reactions:null,equals:Wa,rv:0,wv:0};return n}function De(e,t){const n=pr(e);return Ol(n),n}function dl(e,t=!1,n=!0){const r=pr(e);return t||(r.equals=Ua),Or&&n&&Ge!==null&&Ge.l!==null&&(Ge.l.s??=[]).push(r),r}function U(e,t,n=!1){He!==null&&(!rn||(He.f&Ya)!==0)&&ao()&&(He.f&(At|Pr|ss|Ya))!==0&&!Ln?.includes(e)&&cp();let r=n?Yt(t):t;return po(e,r)}function po(e,t){if(!e.equals(t)){var n=e.v;hr?Fn.set(e,t):Fn.set(e,n),e.v=t;var r=dr.ensure();r.capture(e,n),(e.f&At)!==0&&((e.f&jt)!==0&&gs(e),Ot(e,(e.f&Kt)===0?bt:Yn)),e.wv=Hl(),pl(e,jt),ao()&&Pe!==null&&(Pe.f&bt)!==0&&(Pe.f&(Dn|jn))===0&&(Xt===null?Vp([e]):Xt.push(e))}return t}function fl(e,t=1){var n=c(e),r=t===1?n++:n--;return U(e,n),r}function go(e){U(e,e.v+1)}function pl(e,t){var n=e.reactions;if(n!==null)for(var r=ao(),o=n.length,i=0;i<o;i++){var s=n[i],a=s.f;if(!(!r&&s===Pe)){var l=(a&jt)===0;l&&Ot(s,t),(a&At)!==0?pl(s,Yn):l&&((a&Pr)!==0&&fr!==null&&fr.push(s),Mr(s))}}}function Yt(e){if(typeof e!="object"||e===null||pn in e)return e;const t=Uo(e);if(t!==Ba&&t!==Ff)return e;var n=new Map,r=ro(e),o=De(0),i=vr,s=a=>{if(vr===i)return a();var l=He,u=vr;hn(null),Ml(i);var d=a();return hn(l),Ml(u),d};return r&&n.set("length",De(e.length)),new Proxy(e,{defineProperty(a,l,u){(!("value"in u)||u.configurable===!1||u.enumerable===!1||u.writable===!1)&&lp();var d=n.get(l);return d===void 0?d=s(()=>{var p=De(u.value);return n.set(l,p),p}):U(d,u.value,!0),!0},deleteProperty(a,l){var u=n.get(l);if(u===void 0){if(l in a){const d=s(()=>De(pt));n.set(l,d),go(o)}}else U(u,pt),go(o);return!0},get(a,l,u){if(l===pn)return e;var d=n.get(l),p=l in a;if(d===void 0&&(!p||Tn(a,l)?.writable)&&(d=s(()=>{var g=Yt(p?a[l]:pt),h=De(g);return h}),n.set(l,d)),d!==void 0){var f=c(d);return f===pt?void 0:f}return Reflect.get(a,l,u)},getOwnPropertyDescriptor(a,l){var u=Reflect.getOwnPropertyDescriptor(a,l);if(u&&"value"in u){var d=n.get(l);d&&(u.value=c(d))}else if(u===void 0){var p=n.get(l),f=p?.v;if(p!==void 0&&f!==pt)return{enumerable:!0,configurable:!0,value:f,writable:!0}}return u},has(a,l){if(l===pn)return!0;var u=n.get(l),d=u!==void 0&&u.v!==pt||Reflect.has(a,l);if(u!==void 0||Pe!==null&&(!d||Tn(a,l)?.writable)){u===void 0&&(u=s(()=>{var f=d?Yt(a[l]):pt,g=De(f);return g}),n.set(l,u));var p=c(u);if(p===pt)return!1}return d},set(a,l,u,d){var p=n.get(l),f=l in a;if(r&&l==="length")for(var g=u;g<p.v;g+=1){var h=n.get(g+"");h!==void 0?U(h,pt):g in a&&(h=s(()=>De(pt)),n.set(g+"",h))}if(p===void 0)(!f||Tn(a,l)?.writable)&&(p=s(()=>De(void 0)),U(p,Yt(u)),n.set(l,p));else{f=p.v!==pt;var v=s(()=>Yt(u));U(p,v)}var w=Reflect.getOwnPropertyDescriptor(a,l);if(w?.set&&w.set.call(d,u),!f){if(r&&typeof l=="string"){var b=n.get("length"),x=Number(l);Number.isInteger(x)&&x>=b.v&&U(b,x+1)}go(o)}return!0},ownKeys(a){c(o);var l=Reflect.ownKeys(a).filter(p=>{var f=n.get(p);return f===void 0||f.v!==pt});for(var[u,d]of n)d.v!==pt&&!(u in a)&&l.push(u);return l},setPrototypeOf(){up()}})}function gl(e){try{if(e!==null&&typeof e=="object"&&pn in e)return e[pn]}catch{}return e}function Ep(e,t){return Object.is(gl(e),gl(t))}var Dt,hl,vl,ml;function ms(){if(Dt===void 0){Dt=window,hl=/Firefox/.test(navigator.userAgent);var e=Element.prototype,t=Node.prototype,n=Text.prototype;vl=Tn(t,"firstChild").get,ml=Tn(t,"nextSibling").get,Ka(e)&&(e.__click=void 0,e.__className=void 0,e.__attributes=null,e.__style=void 0,e.__e=void 0),Ka(n)&&(n.__t=void 0)}}function en(e=""){return document.createTextNode(e)}function lt(e){return vl.call(e)}function gn(e){return ml.call(e)}function I(e,t){if(!$e)return lt(e);var n=lt(Oe);if(n===null)n=Oe.appendChild(en());else if(t&&n.nodeType!==us){var r=en();return n?.before(r),xt(r),r}return xt(n),n}function ae(e,t){if(!$e){var n=lt(e);return n instanceof Comment&&n.data===""?gn(n):n}return Oe}function V(e,t=1,n=!1){let r=$e?Oe:e;for(var o;t--;)o=r,r=gn(r);if(!$e)return r;if(n&&r?.nodeType!==us){var i=en();return r===null?o?.after(i):r.before(i),xt(i),i}return xt(r),r}function ys(e){e.textContent=""}function yl(){return!1}function Pp(e,t){if(t){const n=document.body;e.autofocus=!0,co(()=>{document.activeElement===n&&e.focus()})}}function Np(e){$e&&lt(e)!==null&&ys(e)}let wl=!1;function Tp(){wl||(wl=!0,document.addEventListener("reset",e=>{Promise.resolve().then(()=>{if(!e.defaultPrevented)for(const t of e.target.elements)t.__on_r?.()})},{capture:!0}))}function ws(e){var t=He,n=Pe;hn(null),vn(null);try{return e()}finally{hn(t),vn(n)}}function bl(e){Pe===null&&He===null&&op(),He!==null&&(He.f&Kt)!==0&&Pe===null&&rp(),hr&&np()}function Dp(e,t){var n=t.last;n===null?t.last=t.first=e:(n.next=e,e.prev=n,t.last=e)}function tn(e,t,n,r=!0){var o=Pe;o!==null&&(o.f&Qt)!==0&&(e|=Qt);var i={ctx:Ge,deps:null,nodes_start:null,nodes_end:null,f:e|jt,first:null,fn:t,last:null,next:null,parent:o,b:o&&o.b,prev:null,teardown:null,transitions:null,wv:0,ac:null};if(n)try{zr(i),i.f|=os}catch(l){throw St(i),l}else t!==null&&Mr(i);if(r){var s=i;if(n&&s.deps===null&&s.teardown===null&&s.nodes_start===null&&s.first===s.last&&(s.f&Tr)===0&&(s=s.first),s!==null&&(s.parent=o,o!==null&&Dp(s,o),He!==null&&(He.f&At)!==0&&(e&jn)===0)){var a=He;(a.effects??=[]).push(s)}}return i}function Op(){return He!==null&&!rn}function xl(e){const t=tn(Qo,null,!1);return Ot(t,bt),t.teardown=e,t}function et(e){bl();var t=Pe.f,n=!He&&(t&Dn)!==0&&(t&os)===0;if(n){var r=Ge;(r.e??=[]).push(e)}else return Cl(e)}function Cl(e){return tn(rs|Xa,e,!1)}function $l(e){return bl(),tn(Qo|Xa,e,!0)}function bs(e){dr.ensure();const t=tn(jn|Tr,e,!0);return()=>{St(t)}}function Lp(e){dr.ensure();const t=tn(jn|Tr,e,!0);return(n={})=>new Promise(r=>{n.outro?vo(t,()=>{St(t),r(void 0)}):(St(t),r(void 0))})}function Hr(e){return tn(rs,e,!1)}function Mp(e){return tn(ss|Tr,e,!0)}function ho(e,t=0){return tn(Qo|t,e,!0)}function Se(e,t=[],n=[]){il(t,n,r=>{tn(Qo,()=>e(...r.map(c)),!0)})}function gr(e,t=0){var n=tn(Pr|t,e,!0);return n}function nn(e,t=!0){return tn(Dn|Tr,e,!0,t)}function kl(e){var t=e.teardown;if(t!==null){const n=hr,r=He;Dl(!0),hn(null);try{t.call(null)}finally{Dl(n),hn(r)}}}function _l(e,t=!1){var n=e.first;for(e.first=e.last=null;n!==null;){const o=n.ac;o!==null&&ws(()=>{o.abort(ls)});var r=n.next;(n.f&jn)!==0?n.parent=null:St(n,t),n=r}}function Hp(e){for(var t=e.first;t!==null;){var n=t.next;(t.f&Dn)===0&&St(t),t=n}}function St(e,t=!0){var n=!1;(t||(e.f&Uf)!==0)&&e.nodes_start!==null&&e.nodes_end!==null&&(Sl(e.nodes_start,e.nodes_end),n=!0),_l(e,t&&!n),li(e,0),Ot(e,lr);var r=e.transitions;if(r!==null)for(const i of r)i.stop();kl(e);var o=e.parent;o!==null&&o.first!==null&&El(e),e.next=e.prev=e.teardown=e.ctx=e.deps=e.fn=e.nodes_start=e.nodes_end=e.ac=null}function Sl(e,t){for(;e!==null;){var n=e===t?null:gn(e);e.remove(),e=n}}function El(e){var t=e.parent,n=e.prev,r=e.next;n!==null&&(n.next=r),r!==null&&(r.prev=n),t!==null&&(t.first===e&&(t.first=r),t.last===e&&(t.last=n))}function vo(e,t){var n=[];xs(e,n,!0),Pl(n,()=>{St(e),t&&t()})}function Pl(e,t){var n=e.length;if(n>0){var r=()=>--n||t();for(var o of e)o.out(r)}else t()}function xs(e,t,n){if((e.f&Qt)===0){if(e.f^=Qt,e.transitions!==null)for(const s of e.transitions)(s.is_global||n)&&t.push(s);for(var r=e.first;r!==null;){var o=r.next,i=(r.f&Nr)!==0||(r.f&Dn)!==0;xs(r,t,i?n:!1),r=o}}}function si(e){Nl(e,!0)}function Nl(e,t){if((e.f&Qt)!==0){e.f^=Qt,(e.f&bt)===0&&(Ot(e,jt),Mr(e));for(var n=e.first;n!==null;){var r=n.next,o=(n.f&Nr)!==0||(n.f&Dn)!==0;Nl(n,o?t:!1),n=r}if(e.transitions!==null)for(const i of e.transitions)(i.is_global||t)&&i.in()}}let Vr=!1;function Tl(e){Vr=e}let hr=!1;function Dl(e){hr=e}let He=null,rn=!1;function hn(e){He=e}let Pe=null;function vn(e){Pe=e}let Ln=null;function Ol(e){He!==null&&(Ln===null?Ln=[e]:Ln.push(e))}let Et=null,It=0,Xt=null;function Vp(e){Xt=e}let Ll=1,mo=0,vr=mo;function Ml(e){vr=e}let Wn=!1;function Hl(){return++Ll}function ai(e){var t=e.f;if((t&jt)!==0)return!0;if((t&Yn)!==0){var n=e.deps,r=(t&Kt)!==0;if(n!==null){var o,i,s=(t&ei)!==0,a=r&&Pe!==null&&!Wn,l=n.length;if((s||a)&&(Pe===null||(Pe.f&lr)===0)){var u=e,d=u.parent;for(o=0;o<l;o++)i=n[o],(s||!i?.reactions?.includes(u))&&(i.reactions??=[]).push(u);s&&(u.f^=ei),a&&d!==null&&(d.f&Kt)===0&&(u.f^=Kt)}for(o=0;o<l;o++)if(i=n[o],ai(i)&&ol(i),i.wv>e.wv)return!0}(!r||Pe!==null&&!Wn)&&Ot(e,bt)}return!1}function Vl(e,t,n=!0){var r=e.reactions;if(r!==null&&!Ln?.includes(e))for(var o=0;o<r.length;o++){var i=r[o];(i.f&At)!==0?Vl(i,t,!1):t===i&&(n?Ot(i,jt):(i.f&bt)!==0&&Ot(i,Yn),Mr(i))}}function zl(e){var t=Et,n=It,r=Xt,o=He,i=Wn,s=Ln,a=Ge,l=rn,u=vr,d=e.f;Et=null,It=0,Xt=null,Wn=(d&Kt)!==0&&(rn||!Vr||He===null),He=(d&(Dn|jn))===0?e:null,Ln=null,ni(e.ctx),rn=!1,vr=++mo,e.ac!==null&&(ws(()=>{e.ac.abort(ls)}),e.ac=null);try{e.f|=is;var p=e.fn,f=p(),g=e.deps;if(Et!==null){var h;if(li(e,It),g!==null&&It>0)for(g.length=It+Et.length,h=0;h<Et.length;h++)g[It+h]=Et[h];else e.deps=g=Et;if(!Wn||(d&At)!==0&&e.reactions!==null)for(h=It;h<g.length;h++)(g[h].reactions??=[]).push(e)}else g!==null&&It<g.length&&(li(e,It),g.length=It);if(ao()&&Xt!==null&&!rn&&g!==null&&(e.f&(At|Yn|jt))===0)for(h=0;h<Xt.length;h++)Vl(Xt[h],e);return o!==null&&o!==e&&(mo++,Xt!==null&&(r===null?r=Xt:r.push(...Xt))),(e.f&ur)!==0&&(e.f^=ur),f}catch(v){return mp(v)}finally{e.f^=is,Et=t,It=n,Xt=r,He=o,Wn=i,Ln=s,ni(a),rn=l,vr=u}}function zp(e,t){let n=t.reactions;if(n!==null){var r=Xf.call(n,e);if(r!==-1){var o=n.length-1;o===0?n=t.reactions=null:(n[r]=n[o],n.pop())}}n===null&&(t.f&At)!==0&&(Et===null||!Et.includes(t))&&(Ot(t,Yn),(t.f&(Kt|ei))===0&&(t.f^=ei),rl(t),li(t,0))}function li(e,t){var n=e.deps;if(n!==null)for(var r=t;r<n.length;r++)zp(e,n[r])}function zr(e){var t=e.f;if((t&lr)===0){Ot(e,bt);var n=Pe,r=Vr;Pe=e,Vr=!0;try{(t&Pr)!==0?Hp(e):_l(e),kl(e);var o=zl(e);e.teardown=typeof o=="function"?o:null,e.wv=Ll;var i;Yf&&fp&&(e.f&jt)!==0&&e.deps}finally{Vr=r,Pe=n}}}function c(e){var t=e.f,n=(t&At)!==0;if(He!==null&&!rn){var r=Pe!==null&&(Pe.f&lr)!==0;if(!r&&!Ln?.includes(e)){var o=He.deps;if((He.f&is)!==0)e.rv<mo&&(e.rv=mo,Et===null&&o!==null&&o[It]===e?It++:Et===null?Et=[e]:(!Wn||!Et.includes(e))&&Et.push(e));else{(He.deps??=[]).push(e);var i=e.reactions;i===null?e.reactions=[He]:i.includes(He)||i.push(He)}}}else if(n&&e.deps===null&&e.effects===null){var s=e,a=s.parent;a!==null&&(a.f&Kt)===0&&(s.f^=Kt)}if(hr){if(Fn.has(e))return Fn.get(e);if(n){s=e;var l=s.v;return((s.f&bt)===0&&s.reactions!==null||Al(s))&&(l=gs(s)),Fn.set(s,l),l}}else n&&(s=e,ai(s)&&ol(s));if((e.f&ur)!==0)throw e.v;return e.v}function Al(e){if(e.v===pt)return!0;if(e.deps===null)return!1;for(const t of e.deps)if(Fn.has(t)||(t.f&At)!==0&&Al(t))return!0;return!1}function vt(e){var t=rn;try{return rn=!0,e()}finally{rn=t}}const Ap=-7169;function Ot(e,t){e.f=e.f&Ap|t}function Rp(e,t){var n={};for(var r in e)t.includes(r)||(n[r]=e[r]);return n}function Cs(e){if(!(typeof e!="object"||!e||e instanceof EventTarget)){if(pn in e)$s(e);else if(!Array.isArray(e))for(let t in e){const n=e[t];typeof n=="object"&&n&&pn in n&&$s(n)}}}function $s(e,t=new Set){if(typeof e=="object"&&e!==null&&!(e instanceof EventTarget)&&!t.has(e)){t.add(e),e instanceof Date&&e.getTime();for(let r in e)try{$s(e[r],t)}catch{}const n=Uo(e);if(n!==Object.prototype&&n!==Array.prototype&&n!==Map.prototype&&n!==Set.prototype&&n!==Date.prototype){const r=Za(n);for(let o in r){const i=r[o].get;if(i)try{i.call(e)}catch{}}}}}const Rl=new Set,ks=new Set;function _s(e,t,n,r={}){function o(i){if(r.capture||yo.call(t,i),!i.cancelBubble)return ws(()=>n?.call(this,i))}return e.startsWith("pointer")||e.startsWith("touch")||e==="wheel"?co(()=>{t.addEventListener(e,o,r)}):t.addEventListener(e,o,r),o}function Ss(e,t,n,r={}){var o=_s(t,e,n,r);return()=>{e.removeEventListener(t,o,r)}}function Il(e,t,n,r,o){var i={capture:r,passive:o},s=_s(e,t,n,i);(t===document.body||t===window||t===document||t instanceof HTMLMediaElement)&&xl(()=>{t.removeEventListener(e,s,i)})}function Mn(e){for(var t=0;t<e.length;t++)Rl.add(e[t]);for(var n of ks)n(e)}let ql=null;function yo(e){var t=this,n=t.ownerDocument,r=e.type,o=e.composedPath?.()||[],i=o[0]||e.target;ql=e;var s=0,a=ql===e&&e.__root;if(a){var l=o.indexOf(a);if(l!==-1&&(t===document||t===window)){e.__root=t;return}var u=o.indexOf(t);if(u===-1)return;l<=u&&(s=l)}if(i=o[s]||e.target,i!==t){Er(e,"currentTarget",{configurable:!0,get(){return i||n}});var d=He,p=Pe;hn(null),vn(null);try{for(var f,g=[];i!==null;){var h=i.assignedSlot||i.parentNode||i.host||null;try{var v=i["__"+r];if(v!=null&&(!i.disabled||e.target===i))if(ro(v)){var[w,...b]=v;w.apply(i,[e,...b])}else v.call(i,e)}catch(x){f?g.push(x):f=x}if(e.cancelBubble||h===t||h===null)break;i=h}if(f){for(let x of g)queueMicrotask(()=>{throw x});throw f}}finally{e.__root=t,delete e.currentTarget,hn(d),vn(p)}}}function Es(e){var t=document.createElement("template");return t.innerHTML=e.replaceAll("<!>","<!---->"),t.content}function Lt(e,t){var n=Pe;n.nodes_start===null&&(n.nodes_start=e,n.nodes_end=t)}function Q(e,t){var n=(t&Ia)!==0,r=(t&Zf)!==0,o,i=!e.startsWith("<!>");return()=>{if($e)return Lt(Oe,null),Oe;o===void 0&&(o=Es(i?e:"<!>"+e),n||(o=lt(o)));var s=r||hl?document.importNode(o,!0):o.cloneNode(!0);if(n){var a=lt(s),l=s.lastChild;Lt(a,l)}else Lt(s,s);return s}}function Ip(e,t,n="svg"){var r=!e.startsWith("<!>"),o=(t&Ia)!==0,i=`<${n}>${r?e:"<!>"+e}</${n}>`,s;return()=>{if($e)return Lt(Oe,null),Oe;if(!s){var a=Es(i),l=lt(a);if(o)for(s=document.createDocumentFragment();lt(l);)s.appendChild(lt(l));else s=lt(l)}var u=s.cloneNode(!0);if(o){var d=lt(u),p=u.lastChild;Lt(d,p)}else Lt(u,u);return u}}function me(e,t){return Ip(e,t,"svg")}function Ee(e=""){if(!$e){var t=en(e+"");return Lt(t,t),t}var n=Oe;return n.nodeType!==us&&(n.before(n=en()),xt(n)),Lt(n,n),n}function Ne(){if($e)return Lt(Oe,null),Oe;var e=document.createDocumentFragment(),t=document.createComment(""),n=en();return e.append(t,n),Lt(t,n),e}function L(e,t){if($e){Pe.nodes_end=Oe,On();return}e!==null&&e.before(t)}function qp(e){return e.endsWith("capture")&&e!=="gotpointercapture"&&e!=="lostpointercapture"}const Zp=["beforeinput","click","change","dblclick","contextmenu","focusin","focusout","input","keydown","keyup","mousedown","mousemove","mouseout","mouseover","mouseup","pointerdown","pointermove","pointerout","pointerover","pointerup","touchend","touchmove","touchstart"];function Bp(e){return Zp.includes(e)}const Kp={formnovalidate:"formNoValidate",ismap:"isMap",nomodule:"noModule",playsinline:"playsInline",readonly:"readOnly",defaultvalue:"defaultValue",defaultchecked:"defaultChecked",srcobject:"srcObject",novalidate:"noValidate",allowfullscreen:"allowFullscreen",disablepictureinpicture:"disablePictureInPicture",disableremoteplayback:"disableRemotePlayback"};function jp(e){return e=e.toLowerCase(),Kp[e]??e}const Yp=["touchstart","touchmove"];function Xp(e){return Yp.includes(e)}const Fp=["textarea","script","style","title"];function Wp(e){return Fp.includes(e)}function Xe(e,t){var n=t==null?"":typeof t=="object"?t+"":t;n!==(e.__t??=e.nodeValue)&&(e.__t=n,e.nodeValue=n+"")}function Zl(e,t){return Bl(e,t)}function Gp(e,t){ms(),t.intro=t.intro??!1;const n=t.target,r=$e,o=Oe;try{for(var i=lt(n);i&&(i.nodeType!==Dr||i.data!==qa);)i=gn(i);if(!i)throw ar;Rt(!0),xt(i),On();const s=Bl(e,{...t,anchor:i});if(Oe===null||Oe.nodeType!==Dr||Oe.data!==ts)throw so(),ar;return Rt(!1),s}catch(s){if(s instanceof Error&&s.message.split(`
`).some(a=>a.startsWith("https://svelte.dev/e/")))throw s;return s!==ar&&console.warn("Failed to hydrate: ",s),t.recover===!1&&sp(),ms(),ys(n),Rt(!1),Zl(e,t)}finally{Rt(r),xt(o)}}const Ar=new Map;function Bl(e,{target:t,anchor:n,props:r={},events:o,context:i,intro:s=!0}){ms();var a=new Set,l=p=>{for(var f=0;f<p.length;f++){var g=p[f];if(!a.has(g)){a.add(g);var h=Xp(g);t.addEventListener(g,yo,{passive:h});var v=Ar.get(g);v===void 0?(document.addEventListener(g,yo,{passive:h}),Ar.set(g,1)):Ar.set(g,v+1)}}};l(ns(Rl)),ks.add(l);var u=void 0,d=Lp(()=>{var p=n??t.appendChild(en());return nn(()=>{if(i){de({});var f=Ge;f.c=i}o&&(r.$$events=o),$e&&Lt(p,null),u=e(p,r)||{},$e&&(Pe.nodes_end=Oe),i&&fe()}),()=>{for(var f of a){t.removeEventListener(f,yo);var g=Ar.get(f);--g===0?(document.removeEventListener(f,yo),Ar.delete(f)):Ar.set(f,g)}ks.delete(l),p!==n&&p.parentNode?.removeChild(p)}});return Ps.set(u,d),u}let Ps=new WeakMap;function Up(e,t){const n=Ps.get(e);return n?(Ps.delete(e),n(t)):Promise.resolve()}function tt(e,t,...n){var r=e,o=ht,i;gr(()=>{o!==(o=t())&&(i&&(St(i),i=null),i=nn(()=>o(r,...n)))},Nr),$e&&(r=Oe)}function Gn(e){Ge===null&&cs(),Or&&Ge.l!==null?Jp(Ge).m.push(e):et(()=>{const t=vt(e);if(typeof t=="function")return t})}function ui(e){Ge===null&&cs(),Gn(()=>()=>vt(e))}function Jp(e){var t=e.l;return t.u??={a:[],b:[],m:[]}}function ce(e,t,n=!1){$e&&On();var r=e,o=null,i=null,s=pt,a=n?Nr:0,l=!1;const u=(f,g=!0)=>{l=!0,p(g,f)};function d(){var f=s?o:i,g=s?i:o;f&&si(f),g&&vo(g,()=>{s?i=null:o=null})}const p=(f,g)=>{if(s===(s=f))return;let h=!1;if($e){const b=Fa(r)===es;!!s===b&&(r=ds(),xt(r),Rt(!1),h=!0)}var v=yl(),w=r;s?o??=g&&nn(()=>g(w)):i??=g&&nn(()=>g(w)),v||d(),h&&Rt(!0)};gr(()=>{l=!1,t(u),l||p(null,null)},a),$e&&(r=Oe)}function Qp(e,t){$e&&xt(lt(e)),ho(()=>{var n=t();for(var r in n){var o=n[r];o?e.style.setProperty(r,o):e.style.removeProperty(r)}})}function Rr(e,t){return t}function eg(e,t,n){for(var r=e.items,o=[],i=t.length,s=0;s<i;s++)xs(t[s].e,o,!0);var a=i>0&&o.length===0&&n!==null;if(a){var l=n.parentNode;ys(l),l.append(n),r.clear(),mn(e,t[0].prev,t[i-1].next)}Pl(o,()=>{for(var u=0;u<i;u++){var d=t[u];a||(r.delete(d.k),mn(e,d.prev,d.next)),St(d.e,!a)}})}function Ct(e,t,n,r,o,i=null){var s=e,a={flags:t,items:new Map,first:null},l=(t&Aa)!==0;if(l){var u=e;s=$e?xt(lt(u)):u.appendChild(en())}$e&&On();var d=null,p=!1,f=new Map,g=ps(()=>{var b=n();return ro(b)?b:b==null?[]:ns(b)}),h,v;function w(){tg(v,h,a,f,s,o,t,r,n),i!==null&&(h.length===0?d?si(d):d=nn(()=>i(s)):d!==null&&vo(d,()=>{d=null}))}gr(()=>{v??=Pe,h=c(g);var b=h.length;if(p&&b===0)return;p=b===0;let x=!1;if($e){var $=Fa(s)===es;$!==(b===0)&&(s=ds(),xt(s),Rt(!1),x=!0)}if($e){for(var S=null,E,D=0;D<b;D++){if(Oe.nodeType===Dr&&Oe.data===ts){s=Oe,x=!0,Rt(!1);break}var O=h[D],q=r(O,D);E=Kl(Oe,a,S,null,O,q,D,o,t,n),a.items.set(q,E),S=E}b>0&&xt(ds())}$e?b===0&&i&&(d=nn(()=>i(s))):w(),x&&Rt(!0),c(g)}),$e&&(s=Oe)}function tg(e,t,n,r,o,i,s,a,l){var u=(s&Vf)!==0,d=(s&(Ji|Qi))!==0,p=t.length,f=n.items,g=n.first,h=g,v,w=null,b,x=[],$=[],S,E,D,O;if(u)for(O=0;O<p;O+=1)S=t[O],E=a(S,O),D=f.get(E),D!==void 0&&(D.a?.measure(),(b??=new Set).add(D));for(O=0;O<p;O+=1){if(S=t[O],E=a(S,O),D=f.get(E),D===void 0){var q=r.get(E);if(q!==void 0){r.delete(E),f.set(E,q);var K=w?w.next:h;mn(n,w,q),mn(n,q,K),Ns(q,K,o),w=q}else{var J=h?h.e.nodes_start:o;w=Kl(J,n,w,w===null?n.first:w.next,S,E,O,i,s,l)}f.set(E,w),x=[],$=[],h=w.next;continue}if(d&&ng(D,S,O,s),(D.e.f&Qt)!==0&&(si(D.e),u&&(D.a?.unfix(),(b??=new Set).delete(D))),D!==h){if(v!==void 0&&v.has(D)){if(x.length<$.length){var A=$[0],_;w=A.prev;var k=x[0],C=x[x.length-1];for(_=0;_<x.length;_+=1)Ns(x[_],A,o);for(_=0;_<$.length;_+=1)v.delete($[_]);mn(n,k.prev,C.next),mn(n,w,k),mn(n,C,A),h=A,w=C,O-=1,x=[],$=[]}else v.delete(D),Ns(D,h,o),mn(n,D.prev,D.next),mn(n,D,w===null?n.first:w.next),mn(n,w,D),w=D;continue}for(x=[],$=[];h!==null&&h.k!==E;)(h.e.f&Qt)===0&&(v??=new Set).add(h),$.push(h),h=h.next;if(h===null)continue;D=h}x.push(D),w=D,h=D.next}if(h!==null||v!==void 0){for(var N=v===void 0?[]:ns(v);h!==null;)(h.e.f&Qt)===0&&N.push(h),h=h.next;var P=N.length;if(P>0){var H=(s&Aa)!==0&&p===0?o:null;if(u){for(O=0;O<P;O+=1)N[O].a?.measure();for(O=0;O<P;O+=1)N[O].a?.fix()}eg(n,N,H)}}u&&co(()=>{if(b!==void 0)for(D of b)D.a?.apply()}),e.first=n.first&&n.first.e,e.last=w&&w.e;for(var Z of r.values())St(Z.e);r.clear()}function ng(e,t,n,r){(r&Ji)!==0&&po(e.v,t),(r&Qi)!==0?po(e.i,n):e.i=n}function Kl(e,t,n,r,o,i,s,a,l,u,d){var p=(l&Ji)!==0,f=(l&zf)===0,g=p?f?dl(o,!1,!1):pr(o):o,h=(l&Qi)===0?s:pr(s),v={i:h,v:g,k:i,a:null,e:null,prev:n,next:r};try{if(e===null){var w=document.createDocumentFragment();w.append(e=en())}return v.e=nn(()=>a(e,g,h,u),$e),v.e.prev=n&&n.e,v.e.next=r&&r.e,n===null?d||(t.first=v):(n.next=v,n.e.next=v.e),r!==null&&(r.prev=v,r.e.prev=v.e),v}finally{}}function Ns(e,t,n){for(var r=e.next?e.next.e.nodes_start:n,o=t?t.e.nodes_start:n,i=e.e.nodes_start;i!==null&&i!==r;){var s=gn(i);o.before(i),i=s}}function mn(e,t,n){t===null?e.first=n:(t.next=n,t.e.next=n&&n.e),n!==null&&(n.prev=t,n.e.prev=t&&t.e)}function Ts(e,t,n=!1,r=!1,o=!1){var i=e,s="";Se(()=>{var a=Pe;if(s===(s=t()??"")){$e&&On();return}if(a.nodes_start!==null&&(Sl(a.nodes_start,a.nodes_end),a.nodes_start=a.nodes_end=null),s!==""){if($e){Oe.data;for(var l=On(),u=l;l!==null&&(l.nodeType!==Dr||l.data!=="");)u=l,l=gn(l);if(l===null)throw so(),ar;Lt(Oe,u),i=xt(l);return}var d=s+"";n?d=`<svg>${d}</svg>`:r&&(d=`<math>${d}</math>`);var p=Es(d);if((n||r)&&(p=lt(p)),Lt(lt(p),p.lastChild),n||r)for(;lt(p);)i.before(lt(p));else i.before(p)}})}function Ds(e,t,n){$e&&On();var r=e,o,i,s=null,a=null;function l(){i&&(vo(i),i=null),s&&(s.lastChild.remove(),r.before(s),s=null),i=a,a=null}gr(()=>{if(o!==(o=t())){var u=yl();if(o){var d=r;u&&(s=document.createDocumentFragment(),s.append(d=en()),i&&gt.skipped_effects.add(i)),a=nn(()=>n(d,o))}u?gt.add_callback(l):l()}},Nr),$e&&(r=Oe)}function rg(e,t,n,r,o,i){let s=$e;$e&&On();var a,l,u=null;$e&&Oe.nodeType===Qf&&(u=Oe,On());var d=$e?Oe:e,p;gr(()=>{const f=t()||null;var g=f==="svg"?Kf:null;f!==a&&(p&&(f===null?vo(p,()=>{p=null,l=null}):f===l?si(p):St(p)),f&&f!==l&&(p=nn(()=>{if(u=$e?u:g?document.createElementNS(g,f):document.createElement(f),Lt(u,u),r){$e&&Wp(f)&&u.append(document.createComment(""));var h=$e?lt(u):u.appendChild(en());$e&&(h===null?Rt(!1):xt(h)),r(u,h)}Pe.nodes_end=u,d.before(u)})),a=f,a&&(l=a))},Nr),s&&(Rt(!0),xt(d))}function qe(e,t){Hr(()=>{var n=e.getRootNode(),r=n.host?n:n.head??n.ownerDocument.head;if(!r.querySelector("#"+t.hash)){const o=document.createElement("style");o.id=t.hash,o.textContent=t.code,r.appendChild(o)}})}function $t(e,t,n){Hr(()=>{var r=vt(()=>t(e,n?.())||{});if(n&&r?.update){var o=!1,i={};ho(()=>{var s=n();Cs(s),o&&Ga(i,s)&&(i=s,r.update(s))}),o=!0}if(r?.destroy)return()=>r.destroy()})}function og(e,t){var n=void 0,r;gr(()=>{n!==(n=t())&&(r&&(St(r),r=null),n&&(r=nn(()=>{Hr(()=>n(e))})))})}function jl(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=jl(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function ig(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=jl(e))&&(r&&(r+=" "),r+=t);return r}function Hn(e){return typeof e=="object"?ig(e):e??""}const Yl=[...` 	
\r\f \v\uFEFF`];function sg(e,t,n){var r=e==null?"":""+e;if(t&&(r=r?r+" "+t:t),n){for(var o in n)if(n[o])r=r?r+" "+o:o;else if(r.length)for(var i=o.length,s=0;(s=r.indexOf(o,s))>=0;){var a=s+i;(s===0||Yl.includes(r[s-1]))&&(a===r.length||Yl.includes(r[a]))?r=(s===0?"":r.substring(0,s))+r.substring(a+1):s=a}}return r===""?null:r}function Xl(e,t=!1){var n=t?" !important;":";",r="";for(var o in e){var i=e[o];i!=null&&i!==""&&(r+=" "+o+": "+i+n)}return r}function Os(e){return e[0]!=="-"||e[1]!=="-"?e.toLowerCase():e}function ag(e,t){if(t){var n="",r,o;if(Array.isArray(t)?(r=t[0],o=t[1]):r=t,e){e=String(e).replaceAll(/\s*\/\*.*?\*\/\s*/g,"").trim();var i=!1,s=0,a=!1,l=[];r&&l.push(...Object.keys(r).map(Os)),o&&l.push(...Object.keys(o).map(Os));var u=0,d=-1;const v=e.length;for(var p=0;p<v;p++){var f=e[p];if(a?f==="/"&&e[p-1]==="*"&&(a=!1):i?i===f&&(i=!1):f==="/"&&e[p+1]==="*"?a=!0:f==='"'||f==="'"?i=f:f==="("?s++:f===")"&&s--,!a&&i===!1&&s===0){if(f===":"&&d===-1)d=p;else if(f===";"||p===v-1){if(d!==-1){var g=Os(e.substring(u,d).trim());if(!l.includes(g)){f!==";"&&p++;var h=e.substring(u,p).trim();n+=" "+h+";"}}u=p+1,d=-1}}}}return r&&(n+=Xl(r)),o&&(n+=Xl(o,!0)),n=n.trim(),n===""?null:n}return e==null?null:String(e)}function Mt(e,t,n,r,o,i){var s=e.__className;if($e||s!==n||s===void 0){var a=sg(n,r,i);(!$e||a!==e.getAttribute("class"))&&(a==null?e.removeAttribute("class"):t?e.className=a:e.setAttribute("class",a)),e.__className=n}else if(i&&o!==i)for(var l in i){var u=!!i[l];(o==null||u!==!!o[l])&&e.classList.toggle(l,u)}return i}function Ls(e,t={},n,r){for(var o in n){var i=n[o];t[o]!==i&&(n[o]==null?e.style.removeProperty(o):e.style.setProperty(o,i,r))}}function mt(e,t,n,r){var o=e.__style;if($e||o!==t){var i=ag(t,r);(!$e||i!==e.getAttribute("style"))&&(i==null?e.removeAttribute("style"):e.style.cssText=i),e.__style=t}else r&&(Array.isArray(r)?(Ls(e,n?.[0],r[0]),Ls(e,n?.[1],r[1],"important")):Ls(e,n,r));return r}function Ms(e,t,n=!1){if(e.multiple){if(t==null)return;if(!ro(t))return dp();for(var r of e.options)r.selected=t.includes(Fl(r));return}for(r of e.options){var o=Fl(r);if(Ep(o,t)){r.selected=!0;return}}(!n||t!==void 0)&&(e.selectedIndex=-1)}function lg(e){var t=new MutationObserver(()=>{Ms(e,e.__value)});t.observe(e,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["value"]}),xl(()=>{t.disconnect()})}function Fl(e){return"__value"in e?e.__value:e.value}const Un=Symbol("class"),yn=Symbol("style"),Wl=Symbol("is custom element"),Gl=Symbol("is html");function wn(e){if($e){var t=!1,n=()=>{if(!t){if(t=!0,e.hasAttribute("value")){var r=e.value;Ce(e,"value",null),e.value=r}if(e.hasAttribute("checked")){var o=e.checked;Ce(e,"checked",null),e.checked=o}}};e.__on_r=n,wp(n),Tp()}}function ci(e,t){var n=di(e);n.value===(n.value=t??void 0)||e.value===t&&(t!==0||e.nodeName!=="PROGRESS")||(e.value=t??"")}function Hs(e,t){var n=di(e);n.checked!==(n.checked=t??void 0)&&(e.checked=t)}function ug(e,t){t?e.hasAttribute("selected")||e.setAttribute("selected",""):e.removeAttribute("selected")}function Ce(e,t,n,r){var o=di(e);$e&&(o[t]=e.getAttribute(t),t==="src"||t==="srcset"||t==="href"&&e.nodeName==="LINK")||o[t]!==(o[t]=n)&&(t==="loading"&&(e[Jf]=n),n==null?e.removeAttribute(t):typeof n!="string"&&Jl(e).includes(t)?e[t]=n:e.setAttribute(t,n))}function cg(e,t,n,r,o=!1){var i=di(e),s=i[Wl],a=!i[Gl];let l=$e&&s;l&&Rt(!1);var u=t||{},d=e.tagName==="OPTION";for(var p in t)p in n||(n[p]=null);n.class?n.class=Hn(n.class):(r||n[Un])&&(n.class=null),n[yn]&&(n.style??=null);var f=Jl(e);for(const $ in n){let S=n[$];if(d&&$==="value"&&S==null){e.value=e.__value="",u[$]=S;continue}if($==="class"){var g=e.namespaceURI==="http://www.w3.org/1999/xhtml";Mt(e,g,S,r,t?.[Un],n[Un]),u[$]=S,u[Un]=n[Un];continue}if($==="style"){mt(e,S,t?.[yn],n[yn]),u[$]=S,u[yn]=n[yn];continue}var h=u[$];if(!(S===h&&!(S===void 0&&e.hasAttribute($)))){u[$]=S;var v=$[0]+$[1];if(v!=="$$")if(v==="on"){const E={},D="$$"+$;let O=$.slice(2);var w=Bp(O);if(qp(O)&&(O=O.slice(0,-7),E.capture=!0),!w&&h){if(S!=null)continue;e.removeEventListener(O,u[D],E),u[D]=null}if(S!=null)if(w)e[`__${O}`]=S,Mn([O]);else{let q=function(K){u[$].call(this,K)};u[D]=_s(O,e,q,E)}else w&&(e[`__${O}`]=void 0)}else if($==="style")Ce(e,$,S);else if($==="autofocus")Pp(e,!!S);else if(!s&&($==="__value"||$==="value"&&S!=null))e.value=e.__value=S;else if($==="selected"&&d)ug(e,S);else{var b=$;a||(b=jp(b));var x=b==="defaultValue"||b==="defaultChecked";if(S==null&&!s&&!x)if(i[$]=null,b==="value"||b==="checked"){let E=e;const D=t===void 0;if(b==="value"){let O=E.defaultValue;E.removeAttribute(b),E.defaultValue=O,E.value=E.__value=D?O:null}else{let O=E.defaultChecked;E.removeAttribute(b),E.defaultChecked=O,E.checked=D?O:!1}}else e.removeAttribute($);else x||f.includes(b)&&(s||typeof S!="string")?(e[b]=S,b in i&&(i[b]=pt)):typeof S!="function"&&Ce(e,b,S)}}}return l&&Rt(!0),u}function ut(e,t,n=[],r=[],o,i=!1){il(n,r,s=>{var a=void 0,l={},u=e.nodeName==="SELECT",d=!1;if(gr(()=>{var f=t(...s.map(c)),g=cg(e,a,f,o,i);d&&u&&"value"in f&&Ms(e,f.value);for(let v of Object.getOwnPropertySymbols(l))f[v]||St(l[v]);for(let v of Object.getOwnPropertySymbols(f)){var h=f[v];v.description===jf&&(!a||h!==a[v])&&(l[v]&&St(l[v]),l[v]=nn(()=>og(e,()=>h))),g[v]=h}a=g}),u){var p=e;Hr(()=>{Ms(p,a.value,!0),lg(p)})}d=!0})}function di(e){return e.__attributes??={[Wl]:e.nodeName.includes("-"),[Gl]:e.namespaceURI===Bf}}var Ul=new Map;function Jl(e){var t=e.getAttribute("is")||e.nodeName,n=Ul.get(t);if(n)return n;Ul.set(t,n=[]);for(var r,o=e,i=Element.prototype;i!==o;){r=Za(o);for(var s in r)r[s].set&&n.push(s);o=Uo(o)}return n}class Vs{#t=new WeakMap;#e;#n;static entries=new WeakMap;constructor(t){this.#n=t}observe(t,n){var r=this.#t.get(t)||new Set;return r.add(n),this.#t.set(t,r),this.#o().observe(t,this.#n),()=>{var o=this.#t.get(t);o.delete(n),o.size===0&&(this.#t.delete(t),this.#e.unobserve(t))}}#o(){return this.#e??(this.#e=new ResizeObserver(t=>{for(var n of t){Vs.entries.set(n.target,n);for(var r of this.#t.get(n.target)||[])r(n)}}))}}var dg=new Vs({box:"border-box"});function Ql(e,t,n){var r=dg.observe(e,()=>n(e[t]));Hr(()=>(vt(()=>n(e[t])),r))}function eu(e,t){return e===t||e?.[pn]===t}function qt(e={},t,n,r){return Hr(()=>{var o,i;return ho(()=>{o=i,i=[],vt(()=>{e!==n(...i)&&(t(e,...i),o&&eu(n(...o),e)&&t(null,...o))})}),()=>{co(()=>{i&&eu(n(...i),e)&&t(null,...i)})}}),e}function tu(e=!1){const t=Ge,n=t.l.u;if(!n)return;let r=()=>Cs(t.s);if(e){let o=0,i={};const s=fo(()=>{let a=!1;const l=t.s;for(const u in l)l[u]!==i[u]&&(i[u]=l[u],a=!0);return a&&o++,o});r=()=>c(s)}n.b.length&&$l(()=>{nu(t,r),Jo(n.b)}),et(()=>{const o=vt(()=>n.m.map(Wf));return()=>{for(const i of o)typeof i=="function"&&i()}}),n.a.length&&et(()=>{nu(t,r),Jo(n.a)})}function nu(e,t){if(e.l.s)for(const n of e.l.s)c(n);t()}let fi=!1;function fg(e){var t=fi;try{return fi=!1,[e(),fi]}finally{fi=t}}const pg={get(e,t){if(!e.exclude.includes(t))return e.props[t]},set(e,t){return!1},getOwnPropertyDescriptor(e,t){if(!e.exclude.includes(t)&&t in e.props)return{enumerable:!0,configurable:!0,value:e.props[t]}},has(e,t){return e.exclude.includes(t)?!1:t in e.props},ownKeys(e){return Reflect.ownKeys(e.props).filter(t=>!e.exclude.includes(t))}};function Ke(e,t,n){return new Proxy({props:e,exclude:t},pg)}const gg={get(e,t){if(!e.exclude.includes(t))return c(e.version),t in e.special?e.special[t]():e.props[t]},set(e,t,n){if(!(t in e.special)){var r=Pe;try{vn(e.parent_effect),e.special[t]=y({get[t](){return e.props[t]}},t,Ra)}finally{vn(r)}}return e.special[t](n),fl(e.version),!0},getOwnPropertyDescriptor(e,t){if(!e.exclude.includes(t)&&t in e.props)return{enumerable:!0,configurable:!0,value:e.props[t]}},deleteProperty(e,t){return e.exclude.includes(t)||(e.exclude.push(t),fl(e.version)),!0},has(e,t){return e.exclude.includes(t)?!1:t in e.props},ownKeys(e){return Reflect.ownKeys(e.props).filter(t=>!e.exclude.includes(t))}};function ru(e,t){return new Proxy({props:e,exclude:t,special:{},version:pr(0),parent_effect:Pe},gg)}const hg={get(e,t){let n=e.props.length;for(;n--;){let r=e.props[n];if(oo(r)&&(r=r()),typeof r=="object"&&r!==null&&t in r)return r[t]}},set(e,t,n){let r=e.props.length;for(;r--;){let o=e.props[r];oo(o)&&(o=o());const i=Tn(o,t);if(i&&i.set)return i.set(n),!0}return!1},getOwnPropertyDescriptor(e,t){let n=e.props.length;for(;n--;){let r=e.props[n];if(oo(r)&&(r=r()),typeof r=="object"&&r!==null&&t in r){const o=Tn(r,t);return o&&!o.configurable&&(o.configurable=!0),o}}},has(e,t){if(t===pn||t===as)return!1;for(let n of e.props)if(oo(n)&&(n=n()),n!=null&&t in n)return!0;return!1},ownKeys(e){const t=[];for(let n of e.props)if(oo(n)&&(n=n()),!!n){for(const r in n)t.includes(r)||t.push(r);for(const r of Object.getOwnPropertySymbols(n))t.includes(r)||t.push(r)}return t}};function Fe(...e){return new Proxy({props:e},hg)}function y(e,t,n,r){var o=!Or||(n&Rf)!==0,i=(n&If)!==0,s=(n&qf)!==0,a=r,l=!0,u=()=>(l&&(l=!1,a=s?vt(r):r),a),d;if(i){var p=pn in e||as in e;d=Tn(e,t)?.set??(p&&t in e?$=>e[t]=$:void 0)}var f,g=!1;i?[f,g]=fg(()=>e[t]):f=e[t],f===void 0&&r!==void 0&&(f=u(),d&&(o&&ap(),d(f)));var h;if(o?h=()=>{var $=e[t];return $===void 0?u():(l=!0,$)}:h=()=>{var $=e[t];return $!==void 0&&(a=void 0),$===void 0?a:$},o&&(n&Ra)===0)return h;if(d){var v=e.$$legacy;return function($,S){return arguments.length>0?((!o||!S||v||g)&&d(S?h():$),$):h()}}var w=!1,b=((n&Af)!==0?fo:ps)(()=>(w=!1,h()));i&&c(b);var x=Pe;return function($,S){if(arguments.length>0){const E=S?c(b):o&&i?Yt($):$;return U(b,E),w=!0,a!==void 0&&(a=E),$}return hr&&w||(x.f&lr)!==0?b.v:c(b)}}function vg(e){return new mg(e)}class mg{#t;#e;constructor(t){var n=new Map,r=(i,s)=>{var a=dl(s,!1,!1);return n.set(i,a),a};const o=new Proxy({...t.props||{},$$events:{}},{get(i,s){return c(n.get(s)??r(s,Reflect.get(i,s)))},has(i,s){return s===as?!0:(c(n.get(s)??r(s,Reflect.get(i,s))),Reflect.has(i,s))},set(i,s,a){return U(n.get(s)??r(s,a),a),Reflect.set(i,s,a)}});this.#e=(t.hydrate?Gp:Zl)(t.component,{target:t.target,anchor:t.anchor,props:o,context:t.context,intro:t.intro??!1,recover:t.recover}),(!t?.props?.$$host||t.sync===!1)&&m(),this.#t=o.$$events;for(const i of Object.keys(this.#e))i==="$set"||i==="$destroy"||i==="$on"||Er(this,i,{get(){return this.#e[i]},set(s){this.#e[i]=s},enumerable:!0});this.#e.$set=i=>{Object.assign(o,i)},this.#e.$destroy=()=>{Up(this.#e)}}$set(t){this.#e.$set(t)}$on(t,n){this.#t[t]=this.#t[t]||[];const r=(...o)=>n.call(this,...o);return this.#t[t].push(r),()=>{this.#t[t]=this.#t[t].filter(o=>o!==r)}}$destroy(){this.#e.$destroy()}}let ou;typeof HTMLElement=="function"&&(ou=class extends HTMLElement{$$ctor;$$s;$$c;$$cn=!1;$$d={};$$r=!1;$$p_d={};$$l={};$$l_u=new Map;$$me;constructor(e,t,n){super(),this.$$ctor=e,this.$$s=t,n&&this.attachShadow({mode:"open"})}addEventListener(e,t,n){if(this.$$l[e]=this.$$l[e]||[],this.$$l[e].push(t),this.$$c){const r=this.$$c.$on(e,t);this.$$l_u.set(t,r)}super.addEventListener(e,t,n)}removeEventListener(e,t,n){if(super.removeEventListener(e,t,n),this.$$c){const r=this.$$l_u.get(t);r&&(r(),this.$$l_u.delete(t))}}async connectedCallback(){if(this.$$cn=!0,!this.$$c){let e=function(r){return o=>{const i=document.createElement("slot");r!=="default"&&(i.name=r),L(o,i)}};if(await Promise.resolve(),!this.$$cn||this.$$c)return;const t={},n=yg(this);for(const r of this.$$s)r in n&&(r==="default"&&!this.$$d.children?(this.$$d.children=e(r),t.default=!0):t[r]=e(r));for(const r of this.attributes){const o=this.$$g_p(r.name);o in this.$$d||(this.$$d[o]=pi(o,r.value,this.$$p_d,"toProp"))}for(const r in this.$$p_d)!(r in this.$$d)&&this[r]!==void 0&&(this.$$d[r]=this[r],delete this[r]);this.$$c=vg({component:this.$$ctor,target:this.shadowRoot||this,props:{...this.$$d,$$slots:t,$$host:this}}),this.$$me=bs(()=>{ho(()=>{this.$$r=!0;for(const r of Go(this.$$c)){if(!this.$$p_d[r]?.reflect)continue;this.$$d[r]=this.$$c[r];const o=pi(r,this.$$d[r],this.$$p_d,"toAttribute");o==null?this.removeAttribute(this.$$p_d[r].attribute||r):this.setAttribute(this.$$p_d[r].attribute||r,o)}this.$$r=!1})});for(const r in this.$$l)for(const o of this.$$l[r]){const i=this.$$c.$on(r,o);this.$$l_u.set(o,i)}this.$$l={}}}attributeChangedCallback(e,t,n){this.$$r||(e=this.$$g_p(e),this.$$d[e]=pi(e,n,this.$$p_d,"toProp"),this.$$c?.$set({[e]:this.$$d[e]}))}disconnectedCallback(){this.$$cn=!1,Promise.resolve().then(()=>{!this.$$cn&&this.$$c&&(this.$$c.$destroy(),this.$$me(),this.$$c=void 0)})}$$g_p(e){return Go(this.$$p_d).find(t=>this.$$p_d[t].attribute===e||!this.$$p_d[t].attribute&&t.toLowerCase()===e)||e}});function pi(e,t,n,r){const o=n[e]?.type;if(t=o==="Boolean"&&typeof t!="boolean"?t!=null:t,!r||!n[e])return t;if(r==="toAttribute")switch(o){case"Object":case"Array":return t==null?null:JSON.stringify(t);case"Boolean":return t?"":null;case"Number":return t??null;default:return t}else switch(o){case"Object":case"Array":return t&&JSON.parse(t);case"Boolean":return t;case"Number":return t!=null?+t:t;default:return t}}function yg(e){const t={};return e.childNodes.forEach(n=>{t[n.slot||"default"]=!0}),t}function ue(e,t,n,r,o,i){let s=class extends ou{constructor(){super(e,n,o),this.$$p_d=t}static get observedAttributes(){return Go(t).map(a=>(t[a].attribute||a).toLowerCase())}};return Go(t).forEach(a=>{Er(s.prototype,a,{get(){return this.$$c&&a in this.$$c?this.$$c[a]:this.$$d[a]},set(l){l=pi(a,l,t),this.$$d[a]=l;var u=this.$$c;if(u){var d=Tn(u,a)?.get;d?u[a]=l:u.$set({[a]:l})}}})}),r.forEach(a=>{Er(s.prototype,a,{get(){return this.$$c?.[a]}})}),e.element=s,s}var wg={value:()=>{}};function gi(){for(var e=0,t=arguments.length,n={},r;e<t;++e){if(!(r=arguments[e]+"")||r in n||/[\s.]/.test(r))throw new Error("illegal type: "+r);n[r]=[]}return new hi(n)}function hi(e){this._=e}function bg(e,t){return e.trim().split(/^|\s+/).map(function(n){var r="",o=n.indexOf(".");if(o>=0&&(r=n.slice(o+1),n=n.slice(0,o)),n&&!t.hasOwnProperty(n))throw new Error("unknown type: "+n);return{type:n,name:r}})}hi.prototype=gi.prototype={constructor:hi,on:function(e,t){var n=this._,r=bg(e+"",n),o,i=-1,s=r.length;if(arguments.length<2){for(;++i<s;)if((o=(e=r[i]).type)&&(o=xg(n[o],e.name)))return o;return}if(t!=null&&typeof t!="function")throw new Error("invalid callback: "+t);for(;++i<s;)if(o=(e=r[i]).type)n[o]=iu(n[o],e.name,t);else if(t==null)for(o in n)n[o]=iu(n[o],e.name,null);return this},copy:function(){var e={},t=this._;for(var n in t)e[n]=t[n].slice();return new hi(e)},call:function(e,t){if((o=arguments.length-2)>0)for(var n=new Array(o),r=0,o,i;r<o;++r)n[r]=arguments[r+2];if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(i=this._[e],r=0,o=i.length;r<o;++r)i[r].value.apply(t,n)},apply:function(e,t,n){if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(var r=this._[e],o=0,i=r.length;o<i;++o)r[o].value.apply(t,n)}};function xg(e,t){for(var n=0,r=e.length,o;n<r;++n)if((o=e[n]).name===t)return o.value}function iu(e,t,n){for(var r=0,o=e.length;r<o;++r)if(e[r].name===t){e[r]=wg,e=e.slice(0,r).concat(e.slice(r+1));break}return n!=null&&e.push({name:t,value:n}),e}var zs="http://www.w3.org/1999/xhtml";const su={svg:"http://www.w3.org/2000/svg",xhtml:zs,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function vi(e){var t=e+="",n=t.indexOf(":");return n>=0&&(t=e.slice(0,n))!=="xmlns"&&(e=e.slice(n+1)),su.hasOwnProperty(t)?{space:su[t],local:e}:e}function Cg(e){return function(){var t=this.ownerDocument,n=this.namespaceURI;return n===zs&&t.documentElement.namespaceURI===zs?t.createElement(e):t.createElementNS(n,e)}}function $g(e){return function(){return this.ownerDocument.createElementNS(e.space,e.local)}}function au(e){var t=vi(e);return(t.local?$g:Cg)(t)}function kg(){}function As(e){return e==null?kg:function(){return this.querySelector(e)}}function _g(e){typeof e!="function"&&(e=As(e));for(var t=this._groups,n=t.length,r=new Array(n),o=0;o<n;++o)for(var i=t[o],s=i.length,a=r[o]=new Array(s),l,u,d=0;d<s;++d)(l=i[d])&&(u=e.call(l,l.__data__,d,i))&&("__data__"in l&&(u.__data__=l.__data__),a[d]=u);return new Zt(r,this._parents)}function Sg(e){return e==null?[]:Array.isArray(e)?e:Array.from(e)}function Eg(){return[]}function lu(e){return e==null?Eg:function(){return this.querySelectorAll(e)}}function Pg(e){return function(){return Sg(e.apply(this,arguments))}}function Ng(e){typeof e=="function"?e=Pg(e):e=lu(e);for(var t=this._groups,n=t.length,r=[],o=[],i=0;i<n;++i)for(var s=t[i],a=s.length,l,u=0;u<a;++u)(l=s[u])&&(r.push(e.call(l,l.__data__,u,s)),o.push(l));return new Zt(r,o)}function uu(e){return function(){return this.matches(e)}}function cu(e){return function(t){return t.matches(e)}}var Tg=Array.prototype.find;function Dg(e){return function(){return Tg.call(this.children,e)}}function Og(){return this.firstElementChild}function Lg(e){return this.select(e==null?Og:Dg(typeof e=="function"?e:cu(e)))}var Mg=Array.prototype.filter;function Hg(){return Array.from(this.children)}function Vg(e){return function(){return Mg.call(this.children,e)}}function zg(e){return this.selectAll(e==null?Hg:Vg(typeof e=="function"?e:cu(e)))}function Ag(e){typeof e!="function"&&(e=uu(e));for(var t=this._groups,n=t.length,r=new Array(n),o=0;o<n;++o)for(var i=t[o],s=i.length,a=r[o]=[],l,u=0;u<s;++u)(l=i[u])&&e.call(l,l.__data__,u,i)&&a.push(l);return new Zt(r,this._parents)}function du(e){return new Array(e.length)}function Rg(){return new Zt(this._enter||this._groups.map(du),this._parents)}function mi(e,t){this.ownerDocument=e.ownerDocument,this.namespaceURI=e.namespaceURI,this._next=null,this._parent=e,this.__data__=t}mi.prototype={constructor:mi,appendChild:function(e){return this._parent.insertBefore(e,this._next)},insertBefore:function(e,t){return this._parent.insertBefore(e,t)},querySelector:function(e){return this._parent.querySelector(e)},querySelectorAll:function(e){return this._parent.querySelectorAll(e)}};function Ig(e){return function(){return e}}function qg(e,t,n,r,o,i){for(var s=0,a,l=t.length,u=i.length;s<u;++s)(a=t[s])?(a.__data__=i[s],r[s]=a):n[s]=new mi(e,i[s]);for(;s<l;++s)(a=t[s])&&(o[s]=a)}function Zg(e,t,n,r,o,i,s){var a,l,u=new Map,d=t.length,p=i.length,f=new Array(d),g;for(a=0;a<d;++a)(l=t[a])&&(f[a]=g=s.call(l,l.__data__,a,t)+"",u.has(g)?o[a]=l:u.set(g,l));for(a=0;a<p;++a)g=s.call(e,i[a],a,i)+"",(l=u.get(g))?(r[a]=l,l.__data__=i[a],u.delete(g)):n[a]=new mi(e,i[a]);for(a=0;a<d;++a)(l=t[a])&&u.get(f[a])===l&&(o[a]=l)}function Bg(e){return e.__data__}function Kg(e,t){if(!arguments.length)return Array.from(this,Bg);var n=t?Zg:qg,r=this._parents,o=this._groups;typeof e!="function"&&(e=Ig(e));for(var i=o.length,s=new Array(i),a=new Array(i),l=new Array(i),u=0;u<i;++u){var d=r[u],p=o[u],f=p.length,g=jg(e.call(d,d&&d.__data__,u,r)),h=g.length,v=a[u]=new Array(h),w=s[u]=new Array(h),b=l[u]=new Array(f);n(d,p,v,w,b,g,t);for(var x=0,$=0,S,E;x<h;++x)if(S=v[x]){for(x>=$&&($=x+1);!(E=w[$])&&++$<h;);S._next=E||null}}return s=new Zt(s,r),s._enter=a,s._exit=l,s}function jg(e){return typeof e=="object"&&"length"in e?e:Array.from(e)}function Yg(){return new Zt(this._exit||this._groups.map(du),this._parents)}function Xg(e,t,n){var r=this.enter(),o=this,i=this.exit();return typeof e=="function"?(r=e(r),r&&(r=r.selection())):r=r.append(e+""),t!=null&&(o=t(o),o&&(o=o.selection())),n==null?i.remove():n(i),r&&o?r.merge(o).order():o}function Fg(e){for(var t=e.selection?e.selection():e,n=this._groups,r=t._groups,o=n.length,i=r.length,s=Math.min(o,i),a=new Array(o),l=0;l<s;++l)for(var u=n[l],d=r[l],p=u.length,f=a[l]=new Array(p),g,h=0;h<p;++h)(g=u[h]||d[h])&&(f[h]=g);for(;l<o;++l)a[l]=n[l];return new Zt(a,this._parents)}function Wg(){for(var e=this._groups,t=-1,n=e.length;++t<n;)for(var r=e[t],o=r.length-1,i=r[o],s;--o>=0;)(s=r[o])&&(i&&s.compareDocumentPosition(i)^4&&i.parentNode.insertBefore(s,i),i=s);return this}function Gg(e){e||(e=Ug);function t(p,f){return p&&f?e(p.__data__,f.__data__):!p-!f}for(var n=this._groups,r=n.length,o=new Array(r),i=0;i<r;++i){for(var s=n[i],a=s.length,l=o[i]=new Array(a),u,d=0;d<a;++d)(u=s[d])&&(l[d]=u);l.sort(t)}return new Zt(o,this._parents).order()}function Ug(e,t){return e<t?-1:e>t?1:e>=t?0:NaN}function Jg(){var e=arguments[0];return arguments[0]=this,e.apply(null,arguments),this}function Qg(){return Array.from(this)}function eh(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r=e[t],o=0,i=r.length;o<i;++o){var s=r[o];if(s)return s}return null}function th(){let e=0;for(const t of this)++e;return e}function nh(){return!this.node()}function rh(e){for(var t=this._groups,n=0,r=t.length;n<r;++n)for(var o=t[n],i=0,s=o.length,a;i<s;++i)(a=o[i])&&e.call(a,a.__data__,i,o);return this}function oh(e){return function(){this.removeAttribute(e)}}function ih(e){return function(){this.removeAttributeNS(e.space,e.local)}}function sh(e,t){return function(){this.setAttribute(e,t)}}function ah(e,t){return function(){this.setAttributeNS(e.space,e.local,t)}}function lh(e,t){return function(){var n=t.apply(this,arguments);n==null?this.removeAttribute(e):this.setAttribute(e,n)}}function uh(e,t){return function(){var n=t.apply(this,arguments);n==null?this.removeAttributeNS(e.space,e.local):this.setAttributeNS(e.space,e.local,n)}}function ch(e,t){var n=vi(e);if(arguments.length<2){var r=this.node();return n.local?r.getAttributeNS(n.space,n.local):r.getAttribute(n)}return this.each((t==null?n.local?ih:oh:typeof t=="function"?n.local?uh:lh:n.local?ah:sh)(n,t))}function fu(e){return e.ownerDocument&&e.ownerDocument.defaultView||e.document&&e||e.defaultView}function dh(e){return function(){this.style.removeProperty(e)}}function fh(e,t,n){return function(){this.style.setProperty(e,t,n)}}function ph(e,t,n){return function(){var r=t.apply(this,arguments);r==null?this.style.removeProperty(e):this.style.setProperty(e,r,n)}}function gh(e,t,n){return arguments.length>1?this.each((t==null?dh:typeof t=="function"?ph:fh)(e,t,n??"")):Ir(this.node(),e)}function Ir(e,t){return e.style.getPropertyValue(t)||fu(e).getComputedStyle(e,null).getPropertyValue(t)}function hh(e){return function(){delete this[e]}}function vh(e,t){return function(){this[e]=t}}function mh(e,t){return function(){var n=t.apply(this,arguments);n==null?delete this[e]:this[e]=n}}function yh(e,t){return arguments.length>1?this.each((t==null?hh:typeof t=="function"?mh:vh)(e,t)):this.node()[e]}function pu(e){return e.trim().split(/^|\s+/)}function Rs(e){return e.classList||new gu(e)}function gu(e){this._node=e,this._names=pu(e.getAttribute("class")||"")}gu.prototype={add:function(e){var t=this._names.indexOf(e);t<0&&(this._names.push(e),this._node.setAttribute("class",this._names.join(" ")))},remove:function(e){var t=this._names.indexOf(e);t>=0&&(this._names.splice(t,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(e){return this._names.indexOf(e)>=0}};function hu(e,t){for(var n=Rs(e),r=-1,o=t.length;++r<o;)n.add(t[r])}function vu(e,t){for(var n=Rs(e),r=-1,o=t.length;++r<o;)n.remove(t[r])}function wh(e){return function(){hu(this,e)}}function bh(e){return function(){vu(this,e)}}function xh(e,t){return function(){(t.apply(this,arguments)?hu:vu)(this,e)}}function Ch(e,t){var n=pu(e+"");if(arguments.length<2){for(var r=Rs(this.node()),o=-1,i=n.length;++o<i;)if(!r.contains(n[o]))return!1;return!0}return this.each((typeof t=="function"?xh:t?wh:bh)(n,t))}function $h(){this.textContent=""}function kh(e){return function(){this.textContent=e}}function _h(e){return function(){var t=e.apply(this,arguments);this.textContent=t??""}}function Sh(e){return arguments.length?this.each(e==null?$h:(typeof e=="function"?_h:kh)(e)):this.node().textContent}function Eh(){this.innerHTML=""}function Ph(e){return function(){this.innerHTML=e}}function Nh(e){return function(){var t=e.apply(this,arguments);this.innerHTML=t??""}}function Th(e){return arguments.length?this.each(e==null?Eh:(typeof e=="function"?Nh:Ph)(e)):this.node().innerHTML}function Dh(){this.nextSibling&&this.parentNode.appendChild(this)}function Oh(){return this.each(Dh)}function Lh(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function Mh(){return this.each(Lh)}function Hh(e){var t=typeof e=="function"?e:au(e);return this.select(function(){return this.appendChild(t.apply(this,arguments))})}function Vh(){return null}function zh(e,t){var n=typeof e=="function"?e:au(e),r=t==null?Vh:typeof t=="function"?t:As(t);return this.select(function(){return this.insertBefore(n.apply(this,arguments),r.apply(this,arguments)||null)})}function Ah(){var e=this.parentNode;e&&e.removeChild(this)}function Rh(){return this.each(Ah)}function Ih(){var e=this.cloneNode(!1),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function qh(){var e=this.cloneNode(!0),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function Zh(e){return this.select(e?qh:Ih)}function Bh(e){return arguments.length?this.property("__data__",e):this.node().__data__}function Kh(e){return function(t){e.call(this,t,this.__data__)}}function jh(e){return e.trim().split(/^|\s+/).map(function(t){var n="",r=t.indexOf(".");return r>=0&&(n=t.slice(r+1),t=t.slice(0,r)),{type:t,name:n}})}function Yh(e){return function(){var t=this.__on;if(t){for(var n=0,r=-1,o=t.length,i;n<o;++n)i=t[n],(!e.type||i.type===e.type)&&i.name===e.name?this.removeEventListener(i.type,i.listener,i.options):t[++r]=i;++r?t.length=r:delete this.__on}}}function Xh(e,t,n){return function(){var r=this.__on,o,i=Kh(t);if(r){for(var s=0,a=r.length;s<a;++s)if((o=r[s]).type===e.type&&o.name===e.name){this.removeEventListener(o.type,o.listener,o.options),this.addEventListener(o.type,o.listener=i,o.options=n),o.value=t;return}}this.addEventListener(e.type,i,n),o={type:e.type,name:e.name,value:t,listener:i,options:n},r?r.push(o):this.__on=[o]}}function Fh(e,t,n){var r=jh(e+""),o,i=r.length,s;if(arguments.length<2){var a=this.node().__on;if(a){for(var l=0,u=a.length,d;l<u;++l)for(o=0,d=a[l];o<i;++o)if((s=r[o]).type===d.type&&s.name===d.name)return d.value}return}for(a=t?Xh:Yh,o=0;o<i;++o)this.each(a(r[o],t,n));return this}function mu(e,t,n){var r=fu(e),o=r.CustomEvent;typeof o=="function"?o=new o(t,n):(o=r.document.createEvent("Event"),n?(o.initEvent(t,n.bubbles,n.cancelable),o.detail=n.detail):o.initEvent(t,!1,!1)),e.dispatchEvent(o)}function Wh(e,t){return function(){return mu(this,e,t)}}function Gh(e,t){return function(){return mu(this,e,t.apply(this,arguments))}}function Uh(e,t){return this.each((typeof t=="function"?Gh:Wh)(e,t))}function*Jh(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var r=e[t],o=0,i=r.length,s;o<i;++o)(s=r[o])&&(yield s)}var yu=[null];function Zt(e,t){this._groups=e,this._parents=t}function wo(){return new Zt([[document.documentElement]],yu)}function Qh(){return this}Zt.prototype=wo.prototype={constructor:Zt,select:_g,selectAll:Ng,selectChild:Lg,selectChildren:zg,filter:Ag,data:Kg,enter:Rg,exit:Yg,join:Xg,merge:Fg,selection:Qh,order:Wg,sort:Gg,call:Jg,nodes:Qg,node:eh,size:th,empty:nh,each:rh,attr:ch,style:gh,property:yh,classed:Ch,text:Sh,html:Th,raise:Oh,lower:Mh,append:Hh,insert:zh,remove:Rh,clone:Zh,datum:Bh,on:Fh,dispatch:Uh,[Symbol.iterator]:Jh};function Ft(e){return typeof e=="string"?new Zt([[document.querySelector(e)]],[document.documentElement]):new Zt([[e]],yu)}function ev(e){let t;for(;t=e.sourceEvent;)e=t;return e}function on(e,t){if(e=ev(e),t===void 0&&(t=e.currentTarget),t){var n=t.ownerSVGElement||t;if(n.createSVGPoint){var r=n.createSVGPoint();return r.x=e.clientX,r.y=e.clientY,r=r.matrixTransform(t.getScreenCTM().inverse()),[r.x,r.y]}if(t.getBoundingClientRect){var o=t.getBoundingClientRect();return[e.clientX-o.left-t.clientLeft,e.clientY-o.top-t.clientTop]}}return[e.pageX,e.pageY]}const tv={passive:!1},bo={capture:!0,passive:!1};function Is(e){e.stopImmediatePropagation()}function qr(e){e.preventDefault(),e.stopImmediatePropagation()}function wu(e){var t=e.document.documentElement,n=Ft(e).on("dragstart.drag",qr,bo);"onselectstart"in t?n.on("selectstart.drag",qr,bo):(t.__noselect=t.style.MozUserSelect,t.style.MozUserSelect="none")}function bu(e,t){var n=e.document.documentElement,r=Ft(e).on("dragstart.drag",null);t&&(r.on("click.drag",qr,bo),setTimeout(function(){r.on("click.drag",null)},0)),"onselectstart"in n?r.on("selectstart.drag",null):(n.style.MozUserSelect=n.__noselect,delete n.__noselect)}const yi=e=>()=>e;function qs(e,{sourceEvent:t,subject:n,target:r,identifier:o,active:i,x:s,y:a,dx:l,dy:u,dispatch:d}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},subject:{value:n,enumerable:!0,configurable:!0},target:{value:r,enumerable:!0,configurable:!0},identifier:{value:o,enumerable:!0,configurable:!0},active:{value:i,enumerable:!0,configurable:!0},x:{value:s,enumerable:!0,configurable:!0},y:{value:a,enumerable:!0,configurable:!0},dx:{value:l,enumerable:!0,configurable:!0},dy:{value:u,enumerable:!0,configurable:!0},_:{value:d}})}qs.prototype.on=function(){var e=this._.on.apply(this._,arguments);return e===this._?this:e};function nv(e){return!e.ctrlKey&&!e.button}function rv(){return this.parentNode}function ov(e,t){return t??{x:e.x,y:e.y}}function iv(){return navigator.maxTouchPoints||"ontouchstart"in this}function sv(){var e=nv,t=rv,n=ov,r=iv,o={},i=gi("start","drag","end"),s=0,a,l,u,d,p=0;function f(S){S.on("mousedown.drag",g).filter(r).on("touchstart.drag",w).on("touchmove.drag",b,tv).on("touchend.drag touchcancel.drag",x).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function g(S,E){if(!(d||!e.call(this,S,E))){var D=$(this,t.call(this,S,E),S,E,"mouse");D&&(Ft(S.view).on("mousemove.drag",h,bo).on("mouseup.drag",v,bo),wu(S.view),Is(S),u=!1,a=S.clientX,l=S.clientY,D("start",S))}}function h(S){if(qr(S),!u){var E=S.clientX-a,D=S.clientY-l;u=E*E+D*D>p}o.mouse("drag",S)}function v(S){Ft(S.view).on("mousemove.drag mouseup.drag",null),bu(S.view,u),qr(S),o.mouse("end",S)}function w(S,E){if(e.call(this,S,E)){var D=S.changedTouches,O=t.call(this,S,E),q=D.length,K,J;for(K=0;K<q;++K)(J=$(this,O,S,E,D[K].identifier,D[K]))&&(Is(S),J("start",S,D[K]))}}function b(S){var E=S.changedTouches,D=E.length,O,q;for(O=0;O<D;++O)(q=o[E[O].identifier])&&(qr(S),q("drag",S,E[O]))}function x(S){var E=S.changedTouches,D=E.length,O,q;for(d&&clearTimeout(d),d=setTimeout(function(){d=null},500),O=0;O<D;++O)(q=o[E[O].identifier])&&(Is(S),q("end",S,E[O]))}function $(S,E,D,O,q,K){var J=i.copy(),A=on(K||D,E),_,k,C;if((C=n.call(S,new qs("beforestart",{sourceEvent:D,target:f,identifier:q,active:s,x:A[0],y:A[1],dx:0,dy:0,dispatch:J}),O))!=null)return _=C.x-A[0]||0,k=C.y-A[1]||0,function N(P,H,Z){var Y=A,M;switch(P){case"start":o[q]=N,M=s++;break;case"end":delete o[q],--s;case"drag":A=on(Z||H,E),M=s;break}J.call(P,S,new qs(P,{sourceEvent:H,subject:C,target:f,identifier:q,active:M,x:A[0]+_,y:A[1]+k,dx:A[0]-Y[0],dy:A[1]-Y[1],dispatch:J}),O)}}return f.filter=function(S){return arguments.length?(e=typeof S=="function"?S:yi(!!S),f):e},f.container=function(S){return arguments.length?(t=typeof S=="function"?S:yi(S),f):t},f.subject=function(S){return arguments.length?(n=typeof S=="function"?S:yi(S),f):n},f.touchable=function(S){return arguments.length?(r=typeof S=="function"?S:yi(!!S),f):r},f.on=function(){var S=i.on.apply(i,arguments);return S===i?f:S},f.clickDistance=function(S){return arguments.length?(p=(S=+S)*S,f):Math.sqrt(p)},f}function Zs(e,t,n){e.prototype=t.prototype=n,n.constructor=e}function xu(e,t){var n=Object.create(e.prototype);for(var r in t)n[r]=t[r];return n}function xo(){}var Co=.7,wi=1/Co,Zr="\\s*([+-]?\\d+)\\s*",$o="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",bn="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",av=/^#([0-9a-f]{3,8})$/,lv=new RegExp(`^rgb\\(${Zr},${Zr},${Zr}\\)$`),uv=new RegExp(`^rgb\\(${bn},${bn},${bn}\\)$`),cv=new RegExp(`^rgba\\(${Zr},${Zr},${Zr},${$o}\\)$`),dv=new RegExp(`^rgba\\(${bn},${bn},${bn},${$o}\\)$`),fv=new RegExp(`^hsl\\(${$o},${bn},${bn}\\)$`),pv=new RegExp(`^hsla\\(${$o},${bn},${bn},${$o}\\)$`),Cu={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};Zs(xo,mr,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:$u,formatHex:$u,formatHex8:gv,formatHsl:hv,formatRgb:ku,toString:ku});function $u(){return this.rgb().formatHex()}function gv(){return this.rgb().formatHex8()}function hv(){return Nu(this).formatHsl()}function ku(){return this.rgb().formatRgb()}function mr(e){var t,n;return e=(e+"").trim().toLowerCase(),(t=av.exec(e))?(n=t[1].length,t=parseInt(t[1],16),n===6?_u(t):n===3?new Ht(t>>8&15|t>>4&240,t>>4&15|t&240,(t&15)<<4|t&15,1):n===8?bi(t>>24&255,t>>16&255,t>>8&255,(t&255)/255):n===4?bi(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|t&240,((t&15)<<4|t&15)/255):null):(t=lv.exec(e))?new Ht(t[1],t[2],t[3],1):(t=uv.exec(e))?new Ht(t[1]*255/100,t[2]*255/100,t[3]*255/100,1):(t=cv.exec(e))?bi(t[1],t[2],t[3],t[4]):(t=dv.exec(e))?bi(t[1]*255/100,t[2]*255/100,t[3]*255/100,t[4]):(t=fv.exec(e))?Pu(t[1],t[2]/100,t[3]/100,1):(t=pv.exec(e))?Pu(t[1],t[2]/100,t[3]/100,t[4]):Cu.hasOwnProperty(e)?_u(Cu[e]):e==="transparent"?new Ht(NaN,NaN,NaN,0):null}function _u(e){return new Ht(e>>16&255,e>>8&255,e&255,1)}function bi(e,t,n,r){return r<=0&&(e=t=n=NaN),new Ht(e,t,n,r)}function vv(e){return e instanceof xo||(e=mr(e)),e?(e=e.rgb(),new Ht(e.r,e.g,e.b,e.opacity)):new Ht}function Bs(e,t,n,r){return arguments.length===1?vv(e):new Ht(e,t,n,r??1)}function Ht(e,t,n,r){this.r=+e,this.g=+t,this.b=+n,this.opacity=+r}Zs(Ht,Bs,xu(xo,{brighter(e){return e=e==null?wi:Math.pow(wi,e),new Ht(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=e==null?Co:Math.pow(Co,e),new Ht(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new Ht(yr(this.r),yr(this.g),yr(this.b),xi(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Su,formatHex:Su,formatHex8:mv,formatRgb:Eu,toString:Eu}));function Su(){return`#${wr(this.r)}${wr(this.g)}${wr(this.b)}`}function mv(){return`#${wr(this.r)}${wr(this.g)}${wr(this.b)}${wr((isNaN(this.opacity)?1:this.opacity)*255)}`}function Eu(){const e=xi(this.opacity);return`${e===1?"rgb(":"rgba("}${yr(this.r)}, ${yr(this.g)}, ${yr(this.b)}${e===1?")":`, ${e})`}`}function xi(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function yr(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function wr(e){return e=yr(e),(e<16?"0":"")+e.toString(16)}function Pu(e,t,n,r){return r<=0?e=t=n=NaN:n<=0||n>=1?e=t=NaN:t<=0&&(e=NaN),new sn(e,t,n,r)}function Nu(e){if(e instanceof sn)return new sn(e.h,e.s,e.l,e.opacity);if(e instanceof xo||(e=mr(e)),!e)return new sn;if(e instanceof sn)return e;e=e.rgb();var t=e.r/255,n=e.g/255,r=e.b/255,o=Math.min(t,n,r),i=Math.max(t,n,r),s=NaN,a=i-o,l=(i+o)/2;return a?(t===i?s=(n-r)/a+(n<r)*6:n===i?s=(r-t)/a+2:s=(t-n)/a+4,a/=l<.5?i+o:2-i-o,s*=60):a=l>0&&l<1?0:s,new sn(s,a,l,e.opacity)}function yv(e,t,n,r){return arguments.length===1?Nu(e):new sn(e,t,n,r??1)}function sn(e,t,n,r){this.h=+e,this.s=+t,this.l=+n,this.opacity=+r}Zs(sn,yv,xu(xo,{brighter(e){return e=e==null?wi:Math.pow(wi,e),new sn(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=e==null?Co:Math.pow(Co,e),new sn(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,n=this.l,r=n+(n<.5?n:1-n)*t,o=2*n-r;return new Ht(Ks(e>=240?e-240:e+120,o,r),Ks(e,o,r),Ks(e<120?e+240:e-120,o,r),this.opacity)},clamp(){return new sn(Tu(this.h),Ci(this.s),Ci(this.l),xi(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=xi(this.opacity);return`${e===1?"hsl(":"hsla("}${Tu(this.h)}, ${Ci(this.s)*100}%, ${Ci(this.l)*100}%${e===1?")":`, ${e})`}`}}));function Tu(e){return e=(e||0)%360,e<0?e+360:e}function Ci(e){return Math.max(0,Math.min(1,e||0))}function Ks(e,t,n){return(e<60?t+(n-t)*e/60:e<180?n:e<240?t+(n-t)*(240-e)/60:t)*255}const js=e=>()=>e;function wv(e,t){return function(n){return e+n*t}}function bv(e,t,n){return e=Math.pow(e,n),t=Math.pow(t,n)-e,n=1/n,function(r){return Math.pow(e+r*t,n)}}function xv(e){return(e=+e)==1?Du:function(t,n){return n-t?bv(t,n,e):js(isNaN(t)?n:t)}}function Du(e,t){var n=t-e;return n?wv(e,n):js(isNaN(e)?t:e)}const $i=function e(t){var n=xv(t);function r(o,i){var s=n((o=Bs(o)).r,(i=Bs(i)).r),a=n(o.g,i.g),l=n(o.b,i.b),u=Du(o.opacity,i.opacity);return function(d){return o.r=s(d),o.g=a(d),o.b=l(d),o.opacity=u(d),o+""}}return r.gamma=e,r}(1);function Cv(e,t){t||(t=[]);var n=e?Math.min(t.length,e.length):0,r=t.slice(),o;return function(i){for(o=0;o<n;++o)r[o]=e[o]*(1-i)+t[o]*i;return r}}function $v(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}function kv(e,t){var n=t?t.length:0,r=e?Math.min(n,e.length):0,o=new Array(r),i=new Array(n),s;for(s=0;s<r;++s)o[s]=ko(e[s],t[s]);for(;s<n;++s)i[s]=t[s];return function(a){for(s=0;s<r;++s)i[s]=o[s](a);return i}}function _v(e,t){var n=new Date;return e=+e,t=+t,function(r){return n.setTime(e*(1-r)+t*r),n}}function xn(e,t){return e=+e,t=+t,function(n){return e*(1-n)+t*n}}function Sv(e,t){var n={},r={},o;(e===null||typeof e!="object")&&(e={}),(t===null||typeof t!="object")&&(t={});for(o in t)o in e?n[o]=ko(e[o],t[o]):r[o]=t[o];return function(i){for(o in n)r[o]=n[o](i);return r}}var Ys=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Xs=new RegExp(Ys.source,"g");function Ev(e){return function(){return e}}function Pv(e){return function(t){return e(t)+""}}function Ou(e,t){var n=Ys.lastIndex=Xs.lastIndex=0,r,o,i,s=-1,a=[],l=[];for(e=e+"",t=t+"";(r=Ys.exec(e))&&(o=Xs.exec(t));)(i=o.index)>n&&(i=t.slice(n,i),a[s]?a[s]+=i:a[++s]=i),(r=r[0])===(o=o[0])?a[s]?a[s]+=o:a[++s]=o:(a[++s]=null,l.push({i:s,x:xn(r,o)})),n=Xs.lastIndex;return n<t.length&&(i=t.slice(n),a[s]?a[s]+=i:a[++s]=i),a.length<2?l[0]?Pv(l[0].x):Ev(t):(t=l.length,function(u){for(var d=0,p;d<t;++d)a[(p=l[d]).i]=p.x(u);return a.join("")})}function ko(e,t){var n=typeof t,r;return t==null||n==="boolean"?js(t):(n==="number"?xn:n==="string"?(r=mr(t))?(t=r,$i):Ou:t instanceof mr?$i:t instanceof Date?_v:$v(t)?Cv:Array.isArray(t)?kv:typeof t.valueOf!="function"&&typeof t.toString!="function"||isNaN(t)?Sv:xn)(e,t)}var Lu=180/Math.PI,Mu={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function Hu(e,t,n,r,o,i){var s,a,l;return(s=Math.sqrt(e*e+t*t))&&(e/=s,t/=s),(l=e*n+t*r)&&(n-=e*l,r-=t*l),(a=Math.sqrt(n*n+r*r))&&(n/=a,r/=a,l/=a),e*r<t*n&&(e=-e,t=-t,l=-l,s=-s),{translateX:o,translateY:i,rotate:Math.atan2(t,e)*Lu,skewX:Math.atan(l)*Lu,scaleX:s,scaleY:a}}var ki;function Nv(e){const t=new(typeof DOMMatrix=="function"?DOMMatrix:WebKitCSSMatrix)(e+"");return t.isIdentity?Mu:Hu(t.a,t.b,t.c,t.d,t.e,t.f)}function Tv(e){return e==null||(ki||(ki=document.createElementNS("http://www.w3.org/2000/svg","g")),ki.setAttribute("transform",e),!(e=ki.transform.baseVal.consolidate()))?Mu:(e=e.matrix,Hu(e.a,e.b,e.c,e.d,e.e,e.f))}function Vu(e,t,n,r){function o(u){return u.length?u.pop()+" ":""}function i(u,d,p,f,g,h){if(u!==p||d!==f){var v=g.push("translate(",null,t,null,n);h.push({i:v-4,x:xn(u,p)},{i:v-2,x:xn(d,f)})}else(p||f)&&g.push("translate("+p+t+f+n)}function s(u,d,p,f){u!==d?(u-d>180?d+=360:d-u>180&&(u+=360),f.push({i:p.push(o(p)+"rotate(",null,r)-2,x:xn(u,d)})):d&&p.push(o(p)+"rotate("+d+r)}function a(u,d,p,f){u!==d?f.push({i:p.push(o(p)+"skewX(",null,r)-2,x:xn(u,d)}):d&&p.push(o(p)+"skewX("+d+r)}function l(u,d,p,f,g,h){if(u!==p||d!==f){var v=g.push(o(g)+"scale(",null,",",null,")");h.push({i:v-4,x:xn(u,p)},{i:v-2,x:xn(d,f)})}else(p!==1||f!==1)&&g.push(o(g)+"scale("+p+","+f+")")}return function(u,d){var p=[],f=[];return u=e(u),d=e(d),i(u.translateX,u.translateY,d.translateX,d.translateY,p,f),s(u.rotate,d.rotate,p,f),a(u.skewX,d.skewX,p,f),l(u.scaleX,u.scaleY,d.scaleX,d.scaleY,p,f),u=d=null,function(g){for(var h=-1,v=f.length,w;++h<v;)p[(w=f[h]).i]=w.x(g);return p.join("")}}}var Dv=Vu(Nv,"px, ","px)","deg)"),Ov=Vu(Tv,", ",")",")"),Lv=1e-12;function zu(e){return((e=Math.exp(e))+1/e)/2}function Mv(e){return((e=Math.exp(e))-1/e)/2}function Hv(e){return((e=Math.exp(2*e))-1)/(e+1)}const _i=function e(t,n,r){function o(i,s){var a=i[0],l=i[1],u=i[2],d=s[0],p=s[1],f=s[2],g=d-a,h=p-l,v=g*g+h*h,w,b;if(v<Lv)b=Math.log(f/u)/t,w=function(O){return[a+O*g,l+O*h,u*Math.exp(t*O*b)]};else{var x=Math.sqrt(v),$=(f*f-u*u+r*v)/(2*u*n*x),S=(f*f-u*u-r*v)/(2*f*n*x),E=Math.log(Math.sqrt($*$+1)-$),D=Math.log(Math.sqrt(S*S+1)-S);b=(D-E)/t,w=function(O){var q=O*b,K=zu(E),J=u/(n*x)*(K*Hv(t*q+E)-Mv(E));return[a+J*g,l+J*h,u*K/zu(t*q+E)]}}return w.duration=b*1e3*t/Math.SQRT2,w}return o.rho=function(i){var s=Math.max(.001,+i),a=s*s,l=a*a;return e(s,a,l)},o}(Math.SQRT2,2,4);var Br=0,_o=0,So=0,Au=1e3,Si,Eo,Ei=0,br=0,Pi=0,Po=typeof performance=="object"&&performance.now?performance:Date,Ru=typeof window=="object"&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(e){setTimeout(e,17)};function Fs(){return br||(Ru(Vv),br=Po.now()+Pi)}function Vv(){br=0}function Ni(){this._call=this._time=this._next=null}Ni.prototype=Iu.prototype={constructor:Ni,restart:function(e,t,n){if(typeof e!="function")throw new TypeError("callback is not a function");n=(n==null?Fs():+n)+(t==null?0:+t),!this._next&&Eo!==this&&(Eo?Eo._next=this:Si=this,Eo=this),this._call=e,this._time=n,Ws()},stop:function(){this._call&&(this._call=null,this._time=1/0,Ws())}};function Iu(e,t,n){var r=new Ni;return r.restart(e,t,n),r}function zv(){Fs(),++Br;for(var e=Si,t;e;)(t=br-e._time)>=0&&e._call.call(void 0,t),e=e._next;--Br}function qu(){br=(Ei=Po.now())+Pi,Br=_o=0;try{zv()}finally{Br=0,Rv(),br=0}}function Av(){var e=Po.now(),t=e-Ei;t>Au&&(Pi-=t,Ei=e)}function Rv(){for(var e,t=Si,n,r=1/0;t;)t._call?(r>t._time&&(r=t._time),e=t,t=t._next):(n=t._next,t._next=null,t=e?e._next=n:Si=n);Eo=e,Ws(r)}function Ws(e){if(!Br){_o&&(_o=clearTimeout(_o));var t=e-br;t>24?(e<1/0&&(_o=setTimeout(qu,e-Po.now()-Pi)),So&&(So=clearInterval(So))):(So||(Ei=Po.now(),So=setInterval(Av,Au)),Br=1,Ru(qu))}}function Zu(e,t,n){var r=new Ni;return t=t==null?0:+t,r.restart(o=>{r.stop(),e(o+t)},t,n),r}var Iv=gi("start","end","cancel","interrupt"),qv=[],Bu=0,Ku=1,Gs=2,Ti=3,ju=4,Us=5,Di=6;function Oi(e,t,n,r,o,i){var s=e.__transition;if(!s)e.__transition={};else if(n in s)return;Zv(e,n,{name:t,index:r,group:o,on:Iv,tween:qv,time:i.time,delay:i.delay,duration:i.duration,ease:i.ease,timer:null,state:Bu})}function Js(e,t){var n=an(e,t);if(n.state>Bu)throw new Error("too late; already scheduled");return n}function Cn(e,t){var n=an(e,t);if(n.state>Ti)throw new Error("too late; already running");return n}function an(e,t){var n=e.__transition;if(!n||!(n=n[t]))throw new Error("transition not found");return n}function Zv(e,t,n){var r=e.__transition,o;r[t]=n,n.timer=Iu(i,0,n.time);function i(u){n.state=Ku,n.timer.restart(s,n.delay,n.time),n.delay<=u&&s(u-n.delay)}function s(u){var d,p,f,g;if(n.state!==Ku)return l();for(d in r)if(g=r[d],g.name===n.name){if(g.state===Ti)return Zu(s);g.state===ju?(g.state=Di,g.timer.stop(),g.on.call("interrupt",e,e.__data__,g.index,g.group),delete r[d]):+d<t&&(g.state=Di,g.timer.stop(),g.on.call("cancel",e,e.__data__,g.index,g.group),delete r[d])}if(Zu(function(){n.state===Ti&&(n.state=ju,n.timer.restart(a,n.delay,n.time),a(u))}),n.state=Gs,n.on.call("start",e,e.__data__,n.index,n.group),n.state===Gs){for(n.state=Ti,o=new Array(f=n.tween.length),d=0,p=-1;d<f;++d)(g=n.tween[d].value.call(e,e.__data__,n.index,n.group))&&(o[++p]=g);o.length=p+1}}function a(u){for(var d=u<n.duration?n.ease.call(null,u/n.duration):(n.timer.restart(l),n.state=Us,1),p=-1,f=o.length;++p<f;)o[p].call(e,d);n.state===Us&&(n.on.call("end",e,e.__data__,n.index,n.group),l())}function l(){n.state=Di,n.timer.stop(),delete r[t];for(var u in r)return;delete e.__transition}}function Li(e,t){var n=e.__transition,r,o,i=!0,s;if(n){t=t==null?null:t+"";for(s in n){if((r=n[s]).name!==t){i=!1;continue}o=r.state>Gs&&r.state<Us,r.state=Di,r.timer.stop(),r.on.call(o?"interrupt":"cancel",e,e.__data__,r.index,r.group),delete n[s]}i&&delete e.__transition}}function Bv(e){return this.each(function(){Li(this,e)})}function Kv(e,t){var n,r;return function(){var o=Cn(this,e),i=o.tween;if(i!==n){r=n=i;for(var s=0,a=r.length;s<a;++s)if(r[s].name===t){r=r.slice(),r.splice(s,1);break}}o.tween=r}}function jv(e,t,n){var r,o;if(typeof n!="function")throw new Error;return function(){var i=Cn(this,e),s=i.tween;if(s!==r){o=(r=s).slice();for(var a={name:t,value:n},l=0,u=o.length;l<u;++l)if(o[l].name===t){o[l]=a;break}l===u&&o.push(a)}i.tween=o}}function Yv(e,t){var n=this._id;if(e+="",arguments.length<2){for(var r=an(this.node(),n).tween,o=0,i=r.length,s;o<i;++o)if((s=r[o]).name===e)return s.value;return null}return this.each((t==null?Kv:jv)(n,e,t))}function Qs(e,t,n){var r=e._id;return e.each(function(){var o=Cn(this,r);(o.value||(o.value={}))[t]=n.apply(this,arguments)}),function(o){return an(o,r).value[t]}}function Yu(e,t){var n;return(typeof t=="number"?xn:t instanceof mr?$i:(n=mr(t))?(t=n,$i):Ou)(e,t)}function Xv(e){return function(){this.removeAttribute(e)}}function Fv(e){return function(){this.removeAttributeNS(e.space,e.local)}}function Wv(e,t,n){var r,o=n+"",i;return function(){var s=this.getAttribute(e);return s===o?null:s===r?i:i=t(r=s,n)}}function Gv(e,t,n){var r,o=n+"",i;return function(){var s=this.getAttributeNS(e.space,e.local);return s===o?null:s===r?i:i=t(r=s,n)}}function Uv(e,t,n){var r,o,i;return function(){var s,a=n(this),l;return a==null?void this.removeAttribute(e):(s=this.getAttribute(e),l=a+"",s===l?null:s===r&&l===o?i:(o=l,i=t(r=s,a)))}}function Jv(e,t,n){var r,o,i;return function(){var s,a=n(this),l;return a==null?void this.removeAttributeNS(e.space,e.local):(s=this.getAttributeNS(e.space,e.local),l=a+"",s===l?null:s===r&&l===o?i:(o=l,i=t(r=s,a)))}}function Qv(e,t){var n=vi(e),r=n==="transform"?Ov:Yu;return this.attrTween(e,typeof t=="function"?(n.local?Jv:Uv)(n,r,Qs(this,"attr."+e,t)):t==null?(n.local?Fv:Xv)(n):(n.local?Gv:Wv)(n,r,t))}function e1(e,t){return function(n){this.setAttribute(e,t.call(this,n))}}function t1(e,t){return function(n){this.setAttributeNS(e.space,e.local,t.call(this,n))}}function n1(e,t){var n,r;function o(){var i=t.apply(this,arguments);return i!==r&&(n=(r=i)&&t1(e,i)),n}return o._value=t,o}function r1(e,t){var n,r;function o(){var i=t.apply(this,arguments);return i!==r&&(n=(r=i)&&e1(e,i)),n}return o._value=t,o}function o1(e,t){var n="attr."+e;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(t==null)return this.tween(n,null);if(typeof t!="function")throw new Error;var r=vi(e);return this.tween(n,(r.local?n1:r1)(r,t))}function i1(e,t){return function(){Js(this,e).delay=+t.apply(this,arguments)}}function s1(e,t){return t=+t,function(){Js(this,e).delay=t}}function a1(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?i1:s1)(t,e)):an(this.node(),t).delay}function l1(e,t){return function(){Cn(this,e).duration=+t.apply(this,arguments)}}function u1(e,t){return t=+t,function(){Cn(this,e).duration=t}}function c1(e){var t=this._id;return arguments.length?this.each((typeof e=="function"?l1:u1)(t,e)):an(this.node(),t).duration}function d1(e,t){if(typeof t!="function")throw new Error;return function(){Cn(this,e).ease=t}}function f1(e){var t=this._id;return arguments.length?this.each(d1(t,e)):an(this.node(),t).ease}function p1(e,t){return function(){var n=t.apply(this,arguments);if(typeof n!="function")throw new Error;Cn(this,e).ease=n}}function g1(e){if(typeof e!="function")throw new Error;return this.each(p1(this._id,e))}function h1(e){typeof e!="function"&&(e=uu(e));for(var t=this._groups,n=t.length,r=new Array(n),o=0;o<n;++o)for(var i=t[o],s=i.length,a=r[o]=[],l,u=0;u<s;++u)(l=i[u])&&e.call(l,l.__data__,u,i)&&a.push(l);return new Vn(r,this._parents,this._name,this._id)}function v1(e){if(e._id!==this._id)throw new Error;for(var t=this._groups,n=e._groups,r=t.length,o=n.length,i=Math.min(r,o),s=new Array(r),a=0;a<i;++a)for(var l=t[a],u=n[a],d=l.length,p=s[a]=new Array(d),f,g=0;g<d;++g)(f=l[g]||u[g])&&(p[g]=f);for(;a<r;++a)s[a]=t[a];return new Vn(s,this._parents,this._name,this._id)}function m1(e){return(e+"").trim().split(/^|\s+/).every(function(t){var n=t.indexOf(".");return n>=0&&(t=t.slice(0,n)),!t||t==="start"})}function y1(e,t,n){var r,o,i=m1(t)?Js:Cn;return function(){var s=i(this,e),a=s.on;a!==r&&(o=(r=a).copy()).on(t,n),s.on=o}}function w1(e,t){var n=this._id;return arguments.length<2?an(this.node(),n).on.on(e):this.each(y1(n,e,t))}function b1(e){return function(){var t=this.parentNode;for(var n in this.__transition)if(+n!==e)return;t&&t.removeChild(this)}}function x1(){return this.on("end.remove",b1(this._id))}function C1(e){var t=this._name,n=this._id;typeof e!="function"&&(e=As(e));for(var r=this._groups,o=r.length,i=new Array(o),s=0;s<o;++s)for(var a=r[s],l=a.length,u=i[s]=new Array(l),d,p,f=0;f<l;++f)(d=a[f])&&(p=e.call(d,d.__data__,f,a))&&("__data__"in d&&(p.__data__=d.__data__),u[f]=p,Oi(u[f],t,n,f,u,an(d,n)));return new Vn(i,this._parents,t,n)}function $1(e){var t=this._name,n=this._id;typeof e!="function"&&(e=lu(e));for(var r=this._groups,o=r.length,i=[],s=[],a=0;a<o;++a)for(var l=r[a],u=l.length,d,p=0;p<u;++p)if(d=l[p]){for(var f=e.call(d,d.__data__,p,l),g,h=an(d,n),v=0,w=f.length;v<w;++v)(g=f[v])&&Oi(g,t,n,v,f,h);i.push(f),s.push(d)}return new Vn(i,s,t,n)}var k1=wo.prototype.constructor;function _1(){return new k1(this._groups,this._parents)}function S1(e,t){var n,r,o;return function(){var i=Ir(this,e),s=(this.style.removeProperty(e),Ir(this,e));return i===s?null:i===n&&s===r?o:o=t(n=i,r=s)}}function Xu(e){return function(){this.style.removeProperty(e)}}function E1(e,t,n){var r,o=n+"",i;return function(){var s=Ir(this,e);return s===o?null:s===r?i:i=t(r=s,n)}}function P1(e,t,n){var r,o,i;return function(){var s=Ir(this,e),a=n(this),l=a+"";return a==null&&(l=a=(this.style.removeProperty(e),Ir(this,e))),s===l?null:s===r&&l===o?i:(o=l,i=t(r=s,a))}}function N1(e,t){var n,r,o,i="style."+t,s="end."+i,a;return function(){var l=Cn(this,e),u=l.on,d=l.value[i]==null?a||(a=Xu(t)):void 0;(u!==n||o!==d)&&(r=(n=u).copy()).on(s,o=d),l.on=r}}function T1(e,t,n){var r=(e+="")=="transform"?Dv:Yu;return t==null?this.styleTween(e,S1(e,r)).on("end.style."+e,Xu(e)):typeof t=="function"?this.styleTween(e,P1(e,r,Qs(this,"style."+e,t))).each(N1(this._id,e)):this.styleTween(e,E1(e,r,t),n).on("end.style."+e,null)}function D1(e,t,n){return function(r){this.style.setProperty(e,t.call(this,r),n)}}function O1(e,t,n){var r,o;function i(){var s=t.apply(this,arguments);return s!==o&&(r=(o=s)&&D1(e,s,n)),r}return i._value=t,i}function L1(e,t,n){var r="style."+(e+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(t==null)return this.tween(r,null);if(typeof t!="function")throw new Error;return this.tween(r,O1(e,t,n??""))}function M1(e){return function(){this.textContent=e}}function H1(e){return function(){var t=e(this);this.textContent=t??""}}function V1(e){return this.tween("text",typeof e=="function"?H1(Qs(this,"text",e)):M1(e==null?"":e+""))}function z1(e){return function(t){this.textContent=e.call(this,t)}}function A1(e){var t,n;function r(){var o=e.apply(this,arguments);return o!==n&&(t=(n=o)&&z1(o)),t}return r._value=e,r}function R1(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(e==null)return this.tween(t,null);if(typeof e!="function")throw new Error;return this.tween(t,A1(e))}function I1(){for(var e=this._name,t=this._id,n=Fu(),r=this._groups,o=r.length,i=0;i<o;++i)for(var s=r[i],a=s.length,l,u=0;u<a;++u)if(l=s[u]){var d=an(l,t);Oi(l,e,n,u,s,{time:d.time+d.delay+d.duration,delay:0,duration:d.duration,ease:d.ease})}return new Vn(r,this._parents,e,n)}function q1(){var e,t,n=this,r=n._id,o=n.size();return new Promise(function(i,s){var a={value:s},l={value:function(){--o===0&&i()}};n.each(function(){var u=Cn(this,r),d=u.on;d!==e&&(t=(e=d).copy(),t._.cancel.push(a),t._.interrupt.push(a),t._.end.push(l)),u.on=t}),o===0&&i()})}var Z1=0;function Vn(e,t,n,r){this._groups=e,this._parents=t,this._name=n,this._id=r}function Fu(){return++Z1}var zn=wo.prototype;Vn.prototype={constructor:Vn,select:C1,selectAll:$1,selectChild:zn.selectChild,selectChildren:zn.selectChildren,filter:h1,merge:v1,selection:_1,transition:I1,call:zn.call,nodes:zn.nodes,node:zn.node,size:zn.size,empty:zn.empty,each:zn.each,on:w1,attr:Qv,attrTween:o1,style:T1,styleTween:L1,text:V1,textTween:R1,remove:x1,tween:Yv,delay:a1,duration:c1,ease:f1,easeVarying:g1,end:q1,[Symbol.iterator]:zn[Symbol.iterator]};function B1(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}var K1={time:null,delay:0,duration:250,ease:B1};function j1(e,t){for(var n;!(n=e.__transition)||!(n=n[t]);)if(!(e=e.parentNode))throw new Error(`transition ${t} not found`);return n}function Y1(e){var t,n;e instanceof Vn?(t=e._id,e=e._name):(t=Fu(),(n=K1).time=Fs(),e=e==null?null:e+"");for(var r=this._groups,o=r.length,i=0;i<o;++i)for(var s=r[i],a=s.length,l,u=0;u<a;++u)(l=s[u])&&Oi(l,e,t,u,s,n||j1(l,t));return new Vn(r,this._parents,e,t)}wo.prototype.interrupt=Bv,wo.prototype.transition=Y1;const Mi=e=>()=>e;function X1(e,{sourceEvent:t,target:n,transform:r,dispatch:o}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},target:{value:n,enumerable:!0,configurable:!0},transform:{value:r,enumerable:!0,configurable:!0},_:{value:o}})}function An(e,t,n){this.k=e,this.x=t,this.y=n}An.prototype={constructor:An,scale:function(e){return e===1?this:new An(this.k*e,this.x,this.y)},translate:function(e,t){return e===0&t===0?this:new An(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var Hi=new An(1,0,0);Wu.prototype=An.prototype;function Wu(e){for(;!e.__zoom;)if(!(e=e.parentNode))return Hi;return e.__zoom}function ea(e){e.stopImmediatePropagation()}function No(e){e.preventDefault(),e.stopImmediatePropagation()}function F1(e){return(!e.ctrlKey||e.type==="wheel")&&!e.button}function W1(){var e=this;return e instanceof SVGElement?(e=e.ownerSVGElement||e,e.hasAttribute("viewBox")?(e=e.viewBox.baseVal,[[e.x,e.y],[e.x+e.width,e.y+e.height]]):[[0,0],[e.width.baseVal.value,e.height.baseVal.value]]):[[0,0],[e.clientWidth,e.clientHeight]]}function Gu(){return this.__zoom||Hi}function G1(e){return-e.deltaY*(e.deltaMode===1?.05:e.deltaMode?1:.002)*(e.ctrlKey?10:1)}function U1(){return navigator.maxTouchPoints||"ontouchstart"in this}function J1(e,t,n){var r=e.invertX(t[0][0])-n[0][0],o=e.invertX(t[1][0])-n[1][0],i=e.invertY(t[0][1])-n[0][1],s=e.invertY(t[1][1])-n[1][1];return e.translate(o>r?(r+o)/2:Math.min(0,r)||Math.max(0,o),s>i?(i+s)/2:Math.min(0,i)||Math.max(0,s))}function Uu(){var e=F1,t=W1,n=J1,r=G1,o=U1,i=[0,1/0],s=[[-1/0,-1/0],[1/0,1/0]],a=250,l=_i,u=gi("start","zoom","end"),d,p,f,g=500,h=150,v=0,w=10;function b(C){C.property("__zoom",Gu).on("wheel.zoom",q,{passive:!1}).on("mousedown.zoom",K).on("dblclick.zoom",J).filter(o).on("touchstart.zoom",A).on("touchmove.zoom",_).on("touchend.zoom touchcancel.zoom",k).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}b.transform=function(C,N,P,H){var Z=C.selection?C.selection():C;Z.property("__zoom",Gu),C!==Z?E(C,N,P,H):Z.interrupt().each(function(){D(this,arguments).event(H).start().zoom(null,typeof N=="function"?N.apply(this,arguments):N).end()})},b.scaleBy=function(C,N,P,H){b.scaleTo(C,function(){var Z=this.__zoom.k,Y=typeof N=="function"?N.apply(this,arguments):N;return Z*Y},P,H)},b.scaleTo=function(C,N,P,H){b.transform(C,function(){var Z=t.apply(this,arguments),Y=this.__zoom,M=P==null?S(Z):typeof P=="function"?P.apply(this,arguments):P,X=Y.invert(M),te=typeof N=="function"?N.apply(this,arguments):N;return n($(x(Y,te),M,X),Z,s)},P,H)},b.translateBy=function(C,N,P,H){b.transform(C,function(){return n(this.__zoom.translate(typeof N=="function"?N.apply(this,arguments):N,typeof P=="function"?P.apply(this,arguments):P),t.apply(this,arguments),s)},null,H)},b.translateTo=function(C,N,P,H,Z){b.transform(C,function(){var Y=t.apply(this,arguments),M=this.__zoom,X=H==null?S(Y):typeof H=="function"?H.apply(this,arguments):H;return n(Hi.translate(X[0],X[1]).scale(M.k).translate(typeof N=="function"?-N.apply(this,arguments):-N,typeof P=="function"?-P.apply(this,arguments):-P),Y,s)},H,Z)};function x(C,N){return N=Math.max(i[0],Math.min(i[1],N)),N===C.k?C:new An(N,C.x,C.y)}function $(C,N,P){var H=N[0]-P[0]*C.k,Z=N[1]-P[1]*C.k;return H===C.x&&Z===C.y?C:new An(C.k,H,Z)}function S(C){return[(+C[0][0]+ +C[1][0])/2,(+C[0][1]+ +C[1][1])/2]}function E(C,N,P,H){C.on("start.zoom",function(){D(this,arguments).event(H).start()}).on("interrupt.zoom end.zoom",function(){D(this,arguments).event(H).end()}).tween("zoom",function(){var Z=this,Y=arguments,M=D(Z,Y).event(H),X=t.apply(Z,Y),te=P==null?S(X):typeof P=="function"?P.apply(Z,Y):P,oe=Math.max(X[1][0]-X[0][0],X[1][1]-X[0][1]),j=Z.__zoom,G=typeof N=="function"?N.apply(Z,Y):N,F=l(j.invert(te).concat(oe/j.k),G.invert(te).concat(oe/G.k));return function(se){if(se===1)se=G;else{var W=F(se),ye=oe/W[2];se=new An(ye,te[0]-W[0]*ye,te[1]-W[1]*ye)}M.zoom(null,se)}})}function D(C,N,P){return!P&&C.__zooming||new O(C,N)}function O(C,N){this.that=C,this.args=N,this.active=0,this.sourceEvent=null,this.extent=t.apply(C,N),this.taps=0}O.prototype={event:function(C){return C&&(this.sourceEvent=C),this},start:function(){return++this.active===1&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(C,N){return this.mouse&&C!=="mouse"&&(this.mouse[1]=N.invert(this.mouse[0])),this.touch0&&C!=="touch"&&(this.touch0[1]=N.invert(this.touch0[0])),this.touch1&&C!=="touch"&&(this.touch1[1]=N.invert(this.touch1[0])),this.that.__zoom=N,this.emit("zoom"),this},end:function(){return--this.active===0&&(delete this.that.__zooming,this.emit("end")),this},emit:function(C){var N=Ft(this.that).datum();u.call(C,this.that,new X1(C,{sourceEvent:this.sourceEvent,target:b,transform:this.that.__zoom,dispatch:u}),N)}};function q(C,...N){if(!e.apply(this,arguments))return;var P=D(this,N).event(C),H=this.__zoom,Z=Math.max(i[0],Math.min(i[1],H.k*Math.pow(2,r.apply(this,arguments)))),Y=on(C);if(P.wheel)(P.mouse[0][0]!==Y[0]||P.mouse[0][1]!==Y[1])&&(P.mouse[1]=H.invert(P.mouse[0]=Y)),clearTimeout(P.wheel);else{if(H.k===Z)return;P.mouse=[Y,H.invert(Y)],Li(this),P.start()}No(C),P.wheel=setTimeout(M,h),P.zoom("mouse",n($(x(H,Z),P.mouse[0],P.mouse[1]),P.extent,s));function M(){P.wheel=null,P.end()}}function K(C,...N){if(f||!e.apply(this,arguments))return;var P=C.currentTarget,H=D(this,N,!0).event(C),Z=Ft(C.view).on("mousemove.zoom",te,!0).on("mouseup.zoom",oe,!0),Y=on(C,P),M=C.clientX,X=C.clientY;wu(C.view),ea(C),H.mouse=[Y,this.__zoom.invert(Y)],Li(this),H.start();function te(j){if(No(j),!H.moved){var G=j.clientX-M,F=j.clientY-X;H.moved=G*G+F*F>v}H.event(j).zoom("mouse",n($(H.that.__zoom,H.mouse[0]=on(j,P),H.mouse[1]),H.extent,s))}function oe(j){Z.on("mousemove.zoom mouseup.zoom",null),bu(j.view,H.moved),No(j),H.event(j).end()}}function J(C,...N){if(e.apply(this,arguments)){var P=this.__zoom,H=on(C.changedTouches?C.changedTouches[0]:C,this),Z=P.invert(H),Y=P.k*(C.shiftKey?.5:2),M=n($(x(P,Y),H,Z),t.apply(this,N),s);No(C),a>0?Ft(this).transition().duration(a).call(E,M,H,C):Ft(this).call(b.transform,M,H,C)}}function A(C,...N){if(e.apply(this,arguments)){var P=C.touches,H=P.length,Z=D(this,N,C.changedTouches.length===H).event(C),Y,M,X,te;for(ea(C),M=0;M<H;++M)X=P[M],te=on(X,this),te=[te,this.__zoom.invert(te),X.identifier],Z.touch0?!Z.touch1&&Z.touch0[2]!==te[2]&&(Z.touch1=te,Z.taps=0):(Z.touch0=te,Y=!0,Z.taps=1+!!d);d&&(d=clearTimeout(d)),Y&&(Z.taps<2&&(p=te[0],d=setTimeout(function(){d=null},g)),Li(this),Z.start())}}function _(C,...N){if(this.__zooming){var P=D(this,N).event(C),H=C.changedTouches,Z=H.length,Y,M,X,te;for(No(C),Y=0;Y<Z;++Y)M=H[Y],X=on(M,this),P.touch0&&P.touch0[2]===M.identifier?P.touch0[0]=X:P.touch1&&P.touch1[2]===M.identifier&&(P.touch1[0]=X);if(M=P.that.__zoom,P.touch1){var oe=P.touch0[0],j=P.touch0[1],G=P.touch1[0],F=P.touch1[1],se=(se=G[0]-oe[0])*se+(se=G[1]-oe[1])*se,W=(W=F[0]-j[0])*W+(W=F[1]-j[1])*W;M=x(M,Math.sqrt(se/W)),X=[(oe[0]+G[0])/2,(oe[1]+G[1])/2],te=[(j[0]+F[0])/2,(j[1]+F[1])/2]}else if(P.touch0)X=P.touch0[0],te=P.touch0[1];else return;P.zoom("touch",n($(M,X,te),P.extent,s))}}function k(C,...N){if(this.__zooming){var P=D(this,N).event(C),H=C.changedTouches,Z=H.length,Y,M;for(ea(C),f&&clearTimeout(f),f=setTimeout(function(){f=null},g),Y=0;Y<Z;++Y)M=H[Y],P.touch0&&P.touch0[2]===M.identifier?delete P.touch0:P.touch1&&P.touch1[2]===M.identifier&&delete P.touch1;if(P.touch1&&!P.touch0&&(P.touch0=P.touch1,delete P.touch1),P.touch0)P.touch0[1]=this.__zoom.invert(P.touch0[0]);else if(P.end(),P.taps===2&&(M=on(M,this),Math.hypot(p[0]-M[0],p[1]-M[1])<w)){var X=Ft(this).on("dblclick.zoom");X&&X.apply(this,arguments)}}}return b.wheelDelta=function(C){return arguments.length?(r=typeof C=="function"?C:Mi(+C),b):r},b.filter=function(C){return arguments.length?(e=typeof C=="function"?C:Mi(!!C),b):e},b.touchable=function(C){return arguments.length?(o=typeof C=="function"?C:Mi(!!C),b):o},b.extent=function(C){return arguments.length?(t=typeof C=="function"?C:Mi([[+C[0][0],+C[0][1]],[+C[1][0],+C[1][1]]]),b):t},b.scaleExtent=function(C){return arguments.length?(i[0]=+C[0],i[1]=+C[1],b):[i[0],i[1]]},b.translateExtent=function(C){return arguments.length?(s[0][0]=+C[0][0],s[1][0]=+C[1][0],s[0][1]=+C[0][1],s[1][1]=+C[1][1],b):[[s[0][0],s[0][1]],[s[1][0],s[1][1]]]},b.constrain=function(C){return arguments.length?(n=C,b):n},b.duration=function(C){return arguments.length?(a=+C,b):a},b.interpolate=function(C){return arguments.length?(l=C,b):l},b.on=function(){var C=u.on.apply(u,arguments);return C===u?b:C},b.clickDistance=function(C){return arguments.length?(v=(C=+C)*C,b):Math.sqrt(v)},b.tapDistance=function(C){return arguments.length?(w=+C,b):w},b}const To={error001:()=>"[React Flow]: Seems like you have not used zustand provider as an ancestor. Help: https://reactflow.dev/error#001",error002:()=>"It looks like you've created a new nodeTypes or edgeTypes object. If this wasn't on purpose please define the nodeTypes/edgeTypes outside of the component or memoize them.",error003:e=>`Node type "${e}" not found. Using fallback type "default".`,error004:()=>"The React Flow parent container needs a width and a height to render the graph.",error005:()=>"Only child nodes can use a parent extent.",error006:()=>"Can't create edge. An edge needs a source and a target.",error007:e=>`The old edge with id=${e} does not exist.`,error009:e=>`Marker type "${e}" doesn't exist.`,error008:(e,{id:t,sourceHandle:n,targetHandle:r})=>`Couldn't create edge for ${e} handle id: "${e==="source"?n:r}", edge id: ${t}.`,error010:()=>"Handle: No node id found. Make sure to only use a Handle inside a custom Node.",error011:e=>`Edge type "${e}" not found. Using fallback type "default".`,error012:e=>`Node with id "${e}" does not exist, it may have been removed. This can happen when a node is deleted before the "onNodeClick" handler is called.`,error013:(e="react")=>`It seems that you haven't loaded the styles. Please import '@xyflow/${e}/dist/style.css' or base.css to make sure everything is working properly.`,error014:()=>"useNodeConnections: No node ID found. Call useNodeConnections inside a custom Node or provide a node ID.",error015:()=>"It seems that you are trying to drag a node that is not initialized. Please use onNodesChange as explained in the docs."},ta=[[Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY],[Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY]],Ju=["Enter"," ","Escape"],Q1={"node.a11yDescription.default":"Press enter or space to select a node. Press delete to remove it and escape to cancel.","node.a11yDescription.keyboardDisabled":"Press enter or space to select a node. You can then use the arrow keys to move the node around. Press delete to remove it and escape to cancel.","node.a11yDescription.ariaLiveMessage":({direction:e,x:t,y:n})=>`Moved selected node ${e}. New position, x: ${t}, y: ${n}`,"edge.a11yDescription.default":"Press enter or space to select an edge. You can then press delete to remove it or escape to cancel.","controls.ariaLabel":"Control Panel","controls.zoomIn.ariaLabel":"Zoom In","controls.zoomOut.ariaLabel":"Zoom Out","controls.fitView.ariaLabel":"Fit View","controls.interactive.ariaLabel":"Toggle Interactivity","minimap.ariaLabel":"Mini Map","handle.ariaLabel":"Handle"};var Kr;(function(e){e.Strict="strict",e.Loose="loose"})(Kr||(Kr={}));var $n;(function(e){e.Free="free",e.Vertical="vertical",e.Horizontal="horizontal"})($n||($n={}));var Vi;(function(e){e.Partial="partial",e.Full="full"})(Vi||(Vi={}));const na={inProgress:!1,isValid:null,from:null,fromHandle:null,fromPosition:null,fromNode:null,to:null,toHandle:null,toPosition:null,toNode:null};var Rn;(function(e){e.Bezier="default",e.Straight="straight",e.Step="step",e.SmoothStep="smoothstep",e.SimpleBezier="simplebezier"})(Rn||(Rn={}));var Do;(function(e){e.Arrow="arrow",e.ArrowClosed="arrowclosed"})(Do||(Do={}));var be;(function(e){e.Left="left",e.Top="top",e.Right="right",e.Bottom="bottom"})(be||(be={}));const Qu={[be.Left]:be.Right,[be.Right]:be.Left,[be.Top]:be.Bottom,[be.Bottom]:be.Top};function e0(e,t){if(!e&&!t)return!0;if(!e||!t||e.size!==t.size)return!1;if(!e.size&&!t.size)return!0;for(const n of e.keys())if(!t.has(n))return!1;return!0}function ec(e,t,n){if(!n)return;const r=[];e.forEach((o,i)=>{t?.has(i)||r.push(o)}),r.length&&n(r)}function t0(e){return e===null?null:e?"valid":"invalid"}const tc=e=>"id"in e&&"source"in e&&"target"in e,n0=e=>"id"in e&&"position"in e&&!("source"in e)&&!("target"in e),ra=e=>"id"in e&&"internals"in e&&!("source"in e)&&!("target"in e),Oo=(e,t=[0,0])=>{const{width:n,height:r}=Jn(e),o=e.origin??t,i=n*o[0],s=r*o[1];return{x:e.position.x-i,y:e.position.y-s}},r0=(e,t={nodeOrigin:[0,0]})=>{if(e.length===0)return{x:0,y:0,width:0,height:0};const n=e.reduce((r,o)=>{const i=typeof o=="string";let s=!t.nodeLookup&&!i?o:void 0;t.nodeLookup&&(s=i?t.nodeLookup.get(o):ra(o)?o:t.nodeLookup.get(o.id));const a=s?Ri(s,t.nodeOrigin):{x:0,y:0,x2:0,y2:0};return zi(r,a)},{x:1/0,y:1/0,x2:-1/0,y2:-1/0});return Ai(n)},Lo=(e,t={})=>{if(e.size===0)return{x:0,y:0,width:0,height:0};let n={x:1/0,y:1/0,x2:-1/0,y2:-1/0};return e.forEach(r=>{if(t.filter===void 0||t.filter(r)){const o=Ri(r);n=zi(n,o)}}),Ai(n)},oa=(e,t,[n,r,o]=[0,0,1],i=!1,s=!1)=>{const a={...Vo(t,[n,r,o]),width:t.width/o,height:t.height/o},l=[];for(const u of e.values()){const{measured:d,selectable:p=!0,hidden:f=!1}=u;if(s&&!p||f)continue;const g=d.width??u.width??u.initialWidth??null,h=d.height??u.height??u.initialHeight??null,v=Mo(a,Yr(u)),w=(g??0)*(h??0),b=i&&v>0;(!u.internals.handleBounds||b||v>=w||u.dragging)&&l.push(u)}return l},o0=(e,t)=>{const n=new Set;return e.forEach(r=>{n.add(r.id)}),t.filter(r=>n.has(r.source)||n.has(r.target))};function i0(e,t){const n=new Map,r=t?.nodes?new Set(t.nodes.map(o=>o.id)):null;return e.forEach(o=>{o.measured.width&&o.measured.height&&(t?.includeHiddenNodes||!o.hidden)&&(!r||r.has(o.id))&&n.set(o.id,o)}),n}async function s0({nodes:e,width:t,height:n,panZoom:r,minZoom:o,maxZoom:i},s){if(e.size===0)return Promise.resolve(!0);const a=i0(e,s),l=Lo(a),u=sa(l,t,n,s?.minZoom??o,s?.maxZoom??i,s?.padding??.1);return await r.setViewport(u,{duration:s?.duration,ease:s?.ease,interpolate:s?.interpolate}),Promise.resolve(!0)}function nc({nodeId:e,nextPosition:t,nodeLookup:n,nodeOrigin:r=[0,0],nodeExtent:o,onError:i}){const s=n.get(e),a=s.parentId?n.get(s.parentId):void 0,{x:l,y:u}=a?a.internals.positionAbsolute:{x:0,y:0},d=s.origin??r;let p=s.extent||o;if(s.extent==="parent"&&!s.expandParent)if(!a)i?.("005",To.error005());else{const g=a.measured.width,h=a.measured.height;g&&h&&(p=[[l,u],[l+g,u+h]])}else a&&Fr(s.extent)&&(p=[[s.extent[0][0]+l,s.extent[0][1]+u],[s.extent[1][0]+l,s.extent[1][1]+u]]);const f=Fr(p)?xr(t,p,s.measured):t;return(s.measured.width===void 0||s.measured.height===void 0)&&i?.("015",To.error015()),{position:{x:f.x-l+(s.measured.width??0)*d[0],y:f.y-u+(s.measured.height??0)*d[1]},positionAbsolute:f}}async function a0({nodesToRemove:e=[],edgesToRemove:t=[],nodes:n,edges:r,onBeforeDelete:o}){const i=new Set(e.map(p=>p.id)),s=[];for(const p of n){if(p.deletable===!1)continue;const f=i.has(p.id),g=!f&&p.parentId&&s.find(h=>h.id===p.parentId);(f||g)&&s.push(p)}const a=new Set(t.map(p=>p.id)),l=r.filter(p=>p.deletable!==!1),u=o0(s,l);for(const p of l)a.has(p.id)&&!u.find(f=>f.id===p.id)&&u.push(p);if(!o)return{edges:u,nodes:s};const d=await o({nodes:s,edges:u});return typeof d=="boolean"?d?{edges:u,nodes:s}:{edges:[],nodes:[]}:d}const jr=(e,t=0,n=1)=>Math.min(Math.max(e,t),n),xr=(e={x:0,y:0},t,n)=>({x:jr(e.x,t[0][0],t[1][0]-(n?.width??0)),y:jr(e.y,t[0][1],t[1][1]-(n?.height??0))});function rc(e,t,n){const{width:r,height:o}=Jn(n),{x:i,y:s}=n.internals.positionAbsolute;return xr(e,[[i,s],[i+r,s+o]],t)}const oc=(e,t,n)=>e<t?jr(Math.abs(e-t),1,t)/t:e>n?-jr(Math.abs(e-n),1,t)/t:0,ic=(e,t,n=15,r=40)=>{const o=oc(e.x,r,t.width-r)*n,i=oc(e.y,r,t.height-r)*n;return[o,i]},zi=(e,t)=>({x:Math.min(e.x,t.x),y:Math.min(e.y,t.y),x2:Math.max(e.x2,t.x2),y2:Math.max(e.y2,t.y2)}),ia=({x:e,y:t,width:n,height:r})=>({x:e,y:t,x2:e+n,y2:t+r}),Ai=({x:e,y:t,x2:n,y2:r})=>({x:e,y:t,width:n-e,height:r-t}),Yr=(e,t=[0,0])=>{const{x:n,y:r}=ra(e)?e.internals.positionAbsolute:Oo(e,t);return{x:n,y:r,width:e.measured?.width??e.width??e.initialWidth??0,height:e.measured?.height??e.height??e.initialHeight??0}},Ri=(e,t=[0,0])=>{const{x:n,y:r}=ra(e)?e.internals.positionAbsolute:Oo(e,t);return{x:n,y:r,x2:n+(e.measured?.width??e.width??e.initialWidth??0),y2:r+(e.measured?.height??e.height??e.initialHeight??0)}},sc=(e,t)=>Ai(zi(ia(e),ia(t))),Mo=(e,t)=>{const n=Math.max(0,Math.min(e.x+e.width,t.x+t.width)-Math.max(e.x,t.x)),r=Math.max(0,Math.min(e.y+e.height,t.y+t.height)-Math.max(e.y,t.y));return Math.ceil(n*r)},ac=e=>In(e.width)&&In(e.height)&&In(e.x)&&In(e.y),In=e=>!isNaN(e)&&isFinite(e),l0=(e,t)=>{},Ho=(e,t=[1,1])=>({x:t[0]*Math.round(e.x/t[0]),y:t[1]*Math.round(e.y/t[1])}),Vo=({x:e,y:t},[n,r,o],i=!1,s=[1,1])=>{const a={x:(e-n)/o,y:(t-r)/o};return i?Ho(a,s):a},Ii=({x:e,y:t},[n,r,o])=>({x:e*o+n,y:t*o+r});function Xr(e,t){if(typeof e=="number")return Math.floor((t-t/(1+e))*.5);if(typeof e=="string"&&e.endsWith("px")){const n=parseFloat(e);if(!Number.isNaN(n))return Math.floor(n)}if(typeof e=="string"&&e.endsWith("%")){const n=parseFloat(e);if(!Number.isNaN(n))return Math.floor(t*n*.01)}return console.error(`[React Flow] The padding value "${e}" is invalid. Please provide a number or a string with a valid unit (px or %).`),0}function u0(e,t,n){if(typeof e=="string"||typeof e=="number"){const r=Xr(e,n),o=Xr(e,t);return{top:r,right:o,bottom:r,left:o,x:o*2,y:r*2}}if(typeof e=="object"){const r=Xr(e.top??e.y??0,n),o=Xr(e.bottom??e.y??0,n),i=Xr(e.left??e.x??0,t),s=Xr(e.right??e.x??0,t);return{top:r,right:s,bottom:o,left:i,x:i+s,y:r+o}}return{top:0,right:0,bottom:0,left:0,x:0,y:0}}function c0(e,t,n,r,o,i){const{x:s,y:a}=Ii(e,[t,n,r]),{x:l,y:u}=Ii({x:e.x+e.width,y:e.y+e.height},[t,n,r]),d=o-l,p=i-u;return{left:Math.floor(s),top:Math.floor(a),right:Math.floor(d),bottom:Math.floor(p)}}const sa=(e,t,n,r,o,i)=>{const s=u0(i,t,n),a=(t-s.x)/e.width,l=(n-s.y)/e.height,u=Math.min(a,l),d=jr(u,r,o),p=e.x+e.width/2,f=e.y+e.height/2,g=t/2-p*d,h=n/2-f*d,v=c0(e,g,h,d,t,n),w={left:Math.min(v.left-s.left,0),top:Math.min(v.top-s.top,0),right:Math.min(v.right-s.right,0),bottom:Math.min(v.bottom-s.bottom,0)};return{x:g-w.left+w.right,y:h-w.top+w.bottom,zoom:d}},Cr=()=>typeof navigator<"u"&&navigator?.userAgent?.indexOf("Mac")>=0;function Fr(e){return e!=null&&e!=="parent"}function Jn(e){return{width:e.measured?.width??e.width??e.initialWidth??0,height:e.measured?.height??e.height??e.initialHeight??0}}function lc(e){return(e.measured?.width??e.width??e.initialWidth)!==void 0&&(e.measured?.height??e.height??e.initialHeight)!==void 0}function d0(e,t={width:0,height:0},n,r,o){const i={...e},s=r.get(n);if(s){const a=s.origin||o;i.x+=s.internals.positionAbsolute.x-(t.width??0)*a[0],i.y+=s.internals.positionAbsolute.y-(t.height??0)*a[1]}return i}function f0(e){return{...Q1,...e||{}}}function aa(e,{snapGrid:t=[0,0],snapToGrid:n=!1,transform:r,containerBounds:o}){const{x:i,y:s}=kn(e),a=Vo({x:i-(o?.left??0),y:s-(o?.top??0)},r),{x:l,y:u}=n?Ho(a,t):a;return{xSnapped:l,ySnapped:u,...a}}const uc=e=>({width:e.offsetWidth,height:e.offsetHeight}),cc=e=>e?.getRootNode?.()||window?.document,p0=["INPUT","SELECT","TEXTAREA"];function dc(e){const t=e.composedPath?.()?.[0]||e.target;return t?.nodeType!==1?!1:p0.includes(t.nodeName)||t.hasAttribute("contenteditable")||!!t.closest(".nokey")}const fc=e=>"clientX"in e,kn=(e,t)=>{const n=fc(e),r=n?e.clientX:e.touches?.[0].clientX,o=n?e.clientY:e.touches?.[0].clientY;return{x:r-(t?.left??0),y:o-(t?.top??0)}},pc=(e,t,n,r,o)=>{const i=t.querySelectorAll(`.${e}`);return!i||!i.length?null:Array.from(i).map(s=>{const a=s.getBoundingClientRect();return{id:s.getAttribute("data-handleid"),type:e,nodeId:o,position:s.getAttribute("data-handlepos"),x:(a.left-n.left)/r,y:(a.top-n.top)/r,...uc(s)}})};function g0({sourceX:e,sourceY:t,targetX:n,targetY:r,sourceControlX:o,sourceControlY:i,targetControlX:s,targetControlY:a}){const l=e*.125+o*.375+s*.375+n*.125,u=t*.125+i*.375+a*.375+r*.125,d=Math.abs(l-e),p=Math.abs(u-t);return[l,u,d,p]}function qi(e,t){return e>=0?.5*e:t*25*Math.sqrt(-e)}function gc({pos:e,x1:t,y1:n,x2:r,y2:o,c:i}){switch(e){case be.Left:return[t-qi(t-r,i),n];case be.Right:return[t+qi(r-t,i),n];case be.Top:return[t,n-qi(n-o,i)];case be.Bottom:return[t,n+qi(o-n,i)]}}function hc({sourceX:e,sourceY:t,sourcePosition:n=be.Bottom,targetX:r,targetY:o,targetPosition:i=be.Top,curvature:s=.25}){const[a,l]=gc({pos:n,x1:e,y1:t,x2:r,y2:o,c:s}),[u,d]=gc({pos:i,x1:r,y1:o,x2:e,y2:t,c:s}),[p,f,g,h]=g0({sourceX:e,sourceY:t,targetX:r,targetY:o,sourceControlX:a,sourceControlY:l,targetControlX:u,targetControlY:d});return[`M${e},${t} C${a},${l} ${u},${d} ${r},${o}`,p,f,g,h]}function vc({sourceX:e,sourceY:t,targetX:n,targetY:r}){const o=Math.abs(n-e)/2,i=n<e?n+o:n-o,s=Math.abs(r-t)/2,a=r<t?r+s:r-s;return[i,a,o,s]}function h0({sourceNode:e,targetNode:t,selected:n=!1,zIndex:r,elevateOnSelect:o=!1}){if(r!==void 0)return r;const i=o&&n?1e3:0,s=Math.max(e.parentId?e.internals.z:0,t.parentId?t.internals.z:0);return i+s}function v0({sourceNode:e,targetNode:t,width:n,height:r,transform:o}){const i=zi(Ri(e),Ri(t));i.x===i.x2&&(i.x2+=1),i.y===i.y2&&(i.y2+=1);const s={x:-o[0]/o[2],y:-o[1]/o[2],width:n/o[2],height:r/o[2]};return Mo(s,Ai(i))>0}const m0=({source:e,sourceHandle:t,target:n,targetHandle:r})=>`xy-edge__${e}${t||""}-${n}${r||""}`,y0=(e,t)=>t.some(n=>n.source===e.source&&n.target===e.target&&(n.sourceHandle===e.sourceHandle||!n.sourceHandle&&!e.sourceHandle)&&(n.targetHandle===e.targetHandle||!n.targetHandle&&!e.targetHandle)),w0=(e,t)=>{if(!e.source||!e.target)return t;let n;return tc(e)?n={...e}:n={...e,id:m0(e)},y0(n,t)?t:(n.sourceHandle===null&&delete n.sourceHandle,n.targetHandle===null&&delete n.targetHandle,t.concat(n))};function mc({sourceX:e,sourceY:t,targetX:n,targetY:r}){const[o,i,s,a]=vc({sourceX:e,sourceY:t,targetX:n,targetY:r});return[`M ${e},${t}L ${n},${r}`,o,i,s,a]}const yc={[be.Left]:{x:-1,y:0},[be.Right]:{x:1,y:0},[be.Top]:{x:0,y:-1},[be.Bottom]:{x:0,y:1}},b0=({source:e,sourcePosition:t=be.Bottom,target:n})=>t===be.Left||t===be.Right?e.x<n.x?{x:1,y:0}:{x:-1,y:0}:e.y<n.y?{x:0,y:1}:{x:0,y:-1},wc=(e,t)=>Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2));function x0({source:e,sourcePosition:t=be.Bottom,target:n,targetPosition:r=be.Top,center:o,offset:i,stepPosition:s}){const a=yc[t],l=yc[r],u={x:e.x+a.x*i,y:e.y+a.y*i},d={x:n.x+l.x*i,y:n.y+l.y*i},p=b0({source:u,sourcePosition:t,target:d}),f=p.x!==0?"x":"y",g=p[f];let h=[],v,w;const b={x:0,y:0},x={x:0,y:0},[,,$,S]=vc({sourceX:e.x,sourceY:e.y,targetX:n.x,targetY:n.y});if(a[f]*l[f]===-1){f==="x"?(v=o.x??u.x+(d.x-u.x)*s,w=o.y??(u.y+d.y)/2):(v=o.x??(u.x+d.x)/2,w=o.y??u.y+(d.y-u.y)*s);const E=[{x:v,y:u.y},{x:v,y:d.y}],D=[{x:u.x,y:w},{x:d.x,y:w}];a[f]===g?h=f==="x"?E:D:h=f==="x"?D:E}else{const E=[{x:u.x,y:d.y}],D=[{x:d.x,y:u.y}];if(f==="x"?h=a.x===g?D:E:h=a.y===g?E:D,t===r){const A=Math.abs(e[f]-n[f]);if(A<=i){const _=Math.min(i-1,i-A);a[f]===g?b[f]=(u[f]>e[f]?-1:1)*_:x[f]=(d[f]>n[f]?-1:1)*_}}if(t!==r){const A=f==="x"?"y":"x",_=a[f]===l[A],k=u[A]>d[A],C=u[A]<d[A];(a[f]===1&&(!_&&k||_&&C)||a[f]!==1&&(!_&&C||_&&k))&&(h=f==="x"?E:D)}const O={x:u.x+b.x,y:u.y+b.y},q={x:d.x+x.x,y:d.y+x.y},K=Math.max(Math.abs(O.x-h[0].x),Math.abs(q.x-h[0].x)),J=Math.max(Math.abs(O.y-h[0].y),Math.abs(q.y-h[0].y));K>=J?(v=(O.x+q.x)/2,w=h[0].y):(v=h[0].x,w=(O.y+q.y)/2)}return[[e,{x:u.x+b.x,y:u.y+b.y},...h,{x:d.x+x.x,y:d.y+x.y},n],v,w,$,S]}function C0(e,t,n,r){const o=Math.min(wc(e,t)/2,wc(t,n)/2,r),{x:i,y:s}=t;if(e.x===i&&i===n.x||e.y===s&&s===n.y)return`L${i} ${s}`;if(e.y===s){const u=e.x<n.x?-1:1,d=e.y<n.y?1:-1;return`L ${i+o*u},${s}Q ${i},${s} ${i},${s+o*d}`}const a=e.x<n.x?1:-1,l=e.y<n.y?-1:1;return`L ${i},${s+o*l}Q ${i},${s} ${i+o*a},${s}`}function la({sourceX:e,sourceY:t,sourcePosition:n=be.Bottom,targetX:r,targetY:o,targetPosition:i=be.Top,borderRadius:s=5,centerX:a,centerY:l,offset:u=20,stepPosition:d=.5}){const[p,f,g,h,v]=x0({source:{x:e,y:t},sourcePosition:n,target:{x:r,y:o},targetPosition:i,center:{x:a,y:l},offset:u,stepPosition:d});return[p.reduce((w,b,x)=>{let $="";return x>0&&x<p.length-1?$=C0(p[x-1],b,p[x+1],s):$=`${x===0?"M":"L"}${b.x} ${b.y}`,w+=$,w},""),f,g,h,v]}function bc(e){return e&&!!(e.internals.handleBounds||e.handles?.length)&&!!(e.measured.width||e.width||e.initialWidth)}function $0(e){const{sourceNode:t,targetNode:n}=e;if(!bc(t)||!bc(n))return null;const r=t.internals.handleBounds||xc(t.handles),o=n.internals.handleBounds||xc(n.handles),i=Cc(r?.source??[],e.sourceHandle),s=Cc(e.connectionMode===Kr.Strict?o?.target??[]:(o?.target??[]).concat(o?.source??[]),e.targetHandle);if(!i||!s)return e.onError?.("008",To.error008(i?"target":"source",{id:e.id,sourceHandle:e.sourceHandle,targetHandle:e.targetHandle})),null;const a=i?.position||be.Bottom,l=s?.position||be.Top,u=zo(t,i,a),d=zo(n,s,l);return{sourceX:u.x,sourceY:u.y,targetX:d.x,targetY:d.y,sourcePosition:a,targetPosition:l}}function xc(e){if(!e)return null;const t=[],n=[];for(const r of e)r.width=r.width??1,r.height=r.height??1,r.type==="source"?t.push(r):r.type==="target"&&n.push(r);return{source:t,target:n}}function zo(e,t,n=be.Left,r=!1){const o=(t?.x??0)+e.internals.positionAbsolute.x,i=(t?.y??0)+e.internals.positionAbsolute.y,{width:s,height:a}=t??Jn(e);if(r)return{x:o+s/2,y:i+a/2};switch(t?.position??n){case be.Top:return{x:o+s/2,y:i};case be.Right:return{x:o+s,y:i+a/2};case be.Bottom:return{x:o+s/2,y:i+a};case be.Left:return{x:o,y:i+a/2}}}function Cc(e,t){return e&&(t?e.find(n=>n.id===t):e[0])||null}function ua(e,t){return e?typeof e=="string"?e:`${t?`${t}__`:""}${Object.keys(e).sort().map(n=>`${n}=${e[n]}`).join("&")}`:""}function k0(e,{id:t,defaultColor:n,defaultMarkerStart:r,defaultMarkerEnd:o}){const i=new Set;return e.reduce((s,a)=>([a.markerStart||r,a.markerEnd||o].forEach(l=>{if(l&&typeof l=="object"){const u=ua(l,t);i.has(u)||(s.push({id:u,color:l.color||n,...l}),i.add(u))}}),s),[]).sort((s,a)=>s.id.localeCompare(a.id))}function _0(e,t,n,r,o){let i=.5;o==="start"?i=0:o==="end"&&(i=1);let s=[(e.x+e.width*i)*t.zoom+t.x,e.y*t.zoom+t.y-r],a=[-100*i,-100];switch(n){case be.Right:s=[(e.x+e.width)*t.zoom+t.x+r,(e.y+e.height*i)*t.zoom+t.y],a=[0,-100*i];break;case be.Bottom:s[1]=(e.y+e.height)*t.zoom+t.y+r,a[1]=0;break;case be.Left:s=[e.x*t.zoom+t.x-r,(e.y+e.height*i)*t.zoom+t.y],a=[-100,-100*i];break}return`translate(${s[0]}px, ${s[1]}px) translate(${a[0]}%, ${a[1]}%)`}const ca={nodeOrigin:[0,0],nodeExtent:ta,elevateNodesOnSelect:!0,defaults:{}},S0={...ca,checkEquality:!0};function da(e,t){const n={...e};for(const r in t)t[r]!==void 0&&(n[r]=t[r]);return n}function E0(e,t,n){const r=da(ca,n);for(const o of e.values())if(o.parentId)fa(o,e,t,r);else{const i=Oo(o,r.nodeOrigin),s=Fr(o.extent)?o.extent:r.nodeExtent,a=xr(i,s,Jn(o));o.internals.positionAbsolute=a}}function P0(e,t,n,r){const o=da(S0,r);let i=e.length>0;const s=new Map(t),a=o?.elevateNodesOnSelect?1e3:0;t.clear(),n.clear();for(const l of e){let u=s.get(l.id);if(o.checkEquality&&l===u?.internals.userNode)t.set(l.id,u);else{const d=Oo(l,o.nodeOrigin),p=Fr(l.extent)?l.extent:o.nodeExtent,f=xr(d,p,Jn(l));u={...o.defaults,...l,measured:{width:l.measured?.width,height:l.measured?.height},internals:{positionAbsolute:f,handleBounds:l.measured?u?.internals.handleBounds:void 0,z:$c(l,a),userNode:l}},t.set(l.id,u)}(u.measured===void 0||u.measured.width===void 0||u.measured.height===void 0)&&!u.hidden&&(i=!1),l.parentId&&fa(u,t,n,r)}return i}function N0(e,t){if(!e.parentId)return;const n=t.get(e.parentId);n?n.set(e.id,e):t.set(e.parentId,new Map([[e.id,e]]))}function fa(e,t,n,r){const{elevateNodesOnSelect:o,nodeOrigin:i,nodeExtent:s}=da(ca,r),a=e.parentId,l=t.get(a);if(!l){console.warn(`Parent node ${a} not found. Please make sure that parent nodes are in front of their child nodes in the nodes array.`);return}N0(e,n);const u=o?1e3:0,{x:d,y:p,z:f}=T0(e,l,i,s,u),{positionAbsolute:g}=e.internals,h=d!==g.x||p!==g.y;(h||f!==e.internals.z)&&t.set(e.id,{...e,internals:{...e.internals,positionAbsolute:h?{x:d,y:p}:g,z:f}})}function $c(e,t){return(In(e.zIndex)?e.zIndex:0)+(e.selected?t:0)}function T0(e,t,n,r,o){const{x:i,y:s}=t.internals.positionAbsolute,a=Jn(e),l=Oo(e,n),u=Fr(e.extent)?xr(l,e.extent,a):l;let d=xr({x:i+u.x,y:s+u.y},r,a);e.extent==="parent"&&(d=rc(d,a,t));const p=$c(e,o),f=t.internals.z??0;return{x:d.x,y:d.y,z:f>=p?f+1:p}}function D0(e,t,n,r=[0,0]){const o=[],i=new Map;for(const s of e){const a=t.get(s.parentId);if(!a)continue;const l=i.get(s.parentId)?.expandedRect??Yr(a),u=sc(l,s.rect);i.set(s.parentId,{expandedRect:u,parent:a})}return i.size>0&&i.forEach(({expandedRect:s,parent:a},l)=>{const u=a.internals.positionAbsolute,d=Jn(a),p=a.origin??r,f=s.x<u.x?Math.round(Math.abs(u.x-s.x)):0,g=s.y<u.y?Math.round(Math.abs(u.y-s.y)):0,h=Math.max(d.width,Math.round(s.width)),v=Math.max(d.height,Math.round(s.height)),w=(h-d.width)*p[0],b=(v-d.height)*p[1];(f>0||g>0||w||b)&&(o.push({id:l,type:"position",position:{x:a.position.x-f+w,y:a.position.y-g+b}}),n.get(l)?.forEach(x=>{e.some($=>$.id===x.id)||o.push({id:x.id,type:"position",position:{x:x.position.x+f,y:x.position.y+g}})})),(d.width<s.width||d.height<s.height||f||g)&&o.push({id:l,type:"dimensions",setAttributes:!0,dimensions:{width:h+(f?p[0]*f-w:0),height:v+(g?p[1]*g-b:0)}})}),o}function O0(e,t,n,r,o,i){const s=r?.querySelector(".xyflow__viewport");let a=!1;if(!s)return{changes:[],updatedInternals:a};const l=[],u=window.getComputedStyle(s),{m22:d}=new window.DOMMatrixReadOnly(u.transform),p=[];for(const f of e.values()){const g=t.get(f.id);if(!g)continue;if(g.hidden){t.set(g.id,{...g,internals:{...g.internals,handleBounds:void 0}}),a=!0;continue}const h=uc(f.nodeElement),v=g.measured.width!==h.width||g.measured.height!==h.height;if(h.width&&h.height&&(v||!g.internals.handleBounds||f.force)){const w=f.nodeElement.getBoundingClientRect(),b=Fr(g.extent)?g.extent:i;let{positionAbsolute:x}=g.internals;g.parentId&&g.extent==="parent"?x=rc(x,h,t.get(g.parentId)):b&&(x=xr(x,b,h));const $={...g,measured:h,internals:{...g.internals,positionAbsolute:x,handleBounds:{source:pc("source",f.nodeElement,w,d,g.id),target:pc("target",f.nodeElement,w,d,g.id)}}};t.set(g.id,$),g.parentId&&fa($,t,n,{nodeOrigin:o}),a=!0,v&&(l.push({id:g.id,type:"dimensions",dimensions:h}),g.expandParent&&g.parentId&&p.push({id:g.id,parentId:g.parentId,rect:Yr($,o)}))}}if(p.length>0){const f=D0(p,t,n,o);l.push(...f)}return{changes:l,updatedInternals:a}}async function L0({delta:e,panZoom:t,transform:n,translateExtent:r,width:o,height:i}){if(!t||!e.x&&!e.y)return Promise.resolve(!1);const s=await t.setViewportConstrained({x:n[0]+e.x,y:n[1]+e.y,zoom:n[2]},[[0,0],[o,i]],r),a=!!s&&(s.x!==n[0]||s.y!==n[1]||s.k!==n[2]);return Promise.resolve(a)}function kc(e,t,n,r,o,i){let s=o;const a=r.get(s)||new Map;r.set(s,a.set(n,t)),s=`${o}-${e}`;const l=r.get(s)||new Map;if(r.set(s,l.set(n,t)),i){s=`${o}-${e}-${i}`;const u=r.get(s)||new Map;r.set(s,u.set(n,t))}}function M0(e,t,n){e.clear(),t.clear();for(const r of n){const{source:o,target:i,sourceHandle:s=null,targetHandle:a=null}=r,l={edgeId:r.id,source:o,target:i,sourceHandle:s,targetHandle:a},u=`${o}-${s}--${i}-${a}`,d=`${i}-${a}--${o}-${s}`;kc("source",l,d,e,o,s),kc("target",l,u,e,i,a),t.set(r.id,r)}}function H0(e,t){if(e===null||t===null)return!1;const n=Array.isArray(e)?e:[e],r=Array.isArray(t)?t:[t];if(n.length!==r.length)return!1;for(let o=0;o<n.length;o++)if(n[o].id!==r[o].id||n[o].type!==r[o].type||!Object.is(n[o].data,r[o].data))return!1;return!0}function _c(e,t){if(!e.parentId)return!1;const n=t.get(e.parentId);return n?n.selected?!0:_c(n,t):!1}function Sc(e,t,n){let r=e;do{if(r?.matches?.(t))return!0;if(r===n)return!1;r=r?.parentElement}while(r);return!1}function V0(e,t,n,r){const o=new Map;for(const[i,s]of e)if((s.selected||s.id===r)&&(!s.parentId||!_c(s,e))&&(s.draggable||t&&typeof s.draggable>"u")){const a=e.get(i);a&&o.set(i,{id:i,position:a.position||{x:0,y:0},distance:{x:n.x-a.internals.positionAbsolute.x,y:n.y-a.internals.positionAbsolute.y},extent:a.extent,parentId:a.parentId,origin:a.origin,expandParent:a.expandParent,internals:{positionAbsolute:a.internals.positionAbsolute||{x:0,y:0}},measured:{width:a.measured.width??0,height:a.measured.height??0}})}return o}function pa({nodeId:e,dragItems:t,nodeLookup:n,dragging:r=!0}){const o=[];for(const[s,a]of t){const l=n.get(s)?.internals.userNode;l&&o.push({...l,position:a.position,dragging:r})}if(!e)return[o[0],o];const i=n.get(e)?.internals.userNode;return[i?{...i,position:t.get(e)?.position||i.position,dragging:r}:o[0],o]}function z0({dragItems:e,snapGrid:t,x:n,y:r}){const o=e.values().next().value;if(!o)return null;const i={x:n-o.distance.x,y:r-o.distance.y},s=Ho(i,t);return{x:s.x-i.x,y:s.y-i.y}}function A0({onNodeMouseDown:e,getStoreItems:t,onDragStart:n,onDrag:r,onDragStop:o}){let i={x:null,y:null},s=0,a=new Map,l=!1,u={x:0,y:0},d=null,p=!1,f=null,g=!1,h=!1,v=null;function w({noDragClassName:x,handleSelector:$,domNode:S,isSelectable:E,nodeId:D,nodeClickDistance:O=0}){f=Ft(S);function q({x:_,y:k}){const{nodeLookup:C,nodeExtent:N,snapGrid:P,snapToGrid:H,nodeOrigin:Z,onNodeDrag:Y,onSelectionDrag:M,onError:X,updateNodePositions:te}=t();i={x:_,y:k};let oe=!1;const j=a.size>1,G=j&&N?ia(Lo(a)):null,F=j&&H?z0({dragItems:a,snapGrid:P,x:_,y:k}):null;for(const[se,W]of a){if(!C.has(se))continue;let ye={x:_-W.distance.x,y:k-W.distance.y};H&&(ye=F?{x:Math.round(ye.x+F.x),y:Math.round(ye.y+F.y)}:Ho(ye,P));let xe=null;if(j&&N&&!W.extent&&G){const{positionAbsolute:re}=W.internals,ge=re.x-G.x+N[0][0],he=re.x+W.measured.width-G.x2+N[1][0],le=re.y-G.y+N[0][1],Te=re.y+W.measured.height-G.y2+N[1][1];xe=[[ge,le],[he,Te]]}const{position:ie,positionAbsolute:ee}=nc({nodeId:se,nextPosition:ye,nodeLookup:C,nodeExtent:xe||N,nodeOrigin:Z,onError:X});oe=oe||W.position.x!==ie.x||W.position.y!==ie.y,W.position=ie,W.internals.positionAbsolute=ee}if(h=h||oe,!!oe&&(te(a,!0),v&&(r||Y||!D&&M))){const[se,W]=pa({nodeId:D,dragItems:a,nodeLookup:C});r?.(v,a,se,W),Y?.(v,se,W),D||M?.(v,W)}}async function K(){if(!d)return;const{transform:_,panBy:k,autoPanSpeed:C,autoPanOnNodeDrag:N}=t();if(!N){l=!1,cancelAnimationFrame(s);return}const[P,H]=ic(u,d,C);(P!==0||H!==0)&&(i.x=(i.x??0)-P/_[2],i.y=(i.y??0)-H/_[2],await k({x:P,y:H})&&q(i)),s=requestAnimationFrame(K)}function J(_){const{nodeLookup:k,multiSelectionActive:C,nodesDraggable:N,transform:P,snapGrid:H,snapToGrid:Z,selectNodesOnDrag:Y,onNodeDragStart:M,onSelectionDragStart:X,unselectNodesAndEdges:te}=t();p=!0,(!Y||!E)&&!C&&D&&(k.get(D)?.selected||te()),E&&Y&&D&&e?.(D);const oe=aa(_.sourceEvent,{transform:P,snapGrid:H,snapToGrid:Z,containerBounds:d});if(i=oe,a=V0(k,N,oe,D),a.size>0&&(n||M||!D&&X)){const[j,G]=pa({nodeId:D,dragItems:a,nodeLookup:k});n?.(_.sourceEvent,a,j,G),M?.(_.sourceEvent,j,G),D||X?.(_.sourceEvent,G)}}const A=sv().clickDistance(O).on("start",_=>{const{domNode:k,nodeDragThreshold:C,transform:N,snapGrid:P,snapToGrid:H}=t();d=k?.getBoundingClientRect()||null,g=!1,h=!1,v=_.sourceEvent,C===0&&J(_),i=aa(_.sourceEvent,{transform:N,snapGrid:P,snapToGrid:H,containerBounds:d}),u=kn(_.sourceEvent,d)}).on("drag",_=>{const{autoPanOnNodeDrag:k,transform:C,snapGrid:N,snapToGrid:P,nodeDragThreshold:H,nodeLookup:Z}=t(),Y=aa(_.sourceEvent,{transform:C,snapGrid:N,snapToGrid:P,containerBounds:d});if(v=_.sourceEvent,(_.sourceEvent.type==="touchmove"&&_.sourceEvent.touches.length>1||D&&!Z.has(D))&&(g=!0),!g){if(!l&&k&&p&&(l=!0,K()),!p){const M=Y.xSnapped-(i.x??0),X=Y.ySnapped-(i.y??0);Math.sqrt(M*M+X*X)>H&&J(_)}(i.x!==Y.xSnapped||i.y!==Y.ySnapped)&&a&&p&&(u=kn(_.sourceEvent,d),q(Y))}}).on("end",_=>{if(!(!p||g)&&(l=!1,p=!1,cancelAnimationFrame(s),a.size>0)){const{nodeLookup:k,updateNodePositions:C,onNodeDragStop:N,onSelectionDragStop:P}=t();if(h&&(C(a,!1),h=!1),o||N||!D&&P){const[H,Z]=pa({nodeId:D,dragItems:a,nodeLookup:k,dragging:!1});o?.(_.sourceEvent,a,H,Z),N?.(_.sourceEvent,H,Z),D||P?.(_.sourceEvent,Z)}}}).filter(_=>{const k=_.target;return!_.button&&(!x||!Sc(k,`.${x}`,S))&&(!$||Sc(k,$,S))});f.call(A)}function b(){f?.on(".drag",null)}return{update:w,destroy:b}}function R0(e,t,n){const r=[],o={x:e.x-n,y:e.y-n,width:n*2,height:n*2};for(const i of t.values())Mo(o,Yr(i))>0&&r.push(i);return r}const I0=250;function q0(e,t,n,r){let o=[],i=1/0;const s=R0(e,n,t+I0);for(const a of s){const l=[...a.internals.handleBounds?.source??[],...a.internals.handleBounds?.target??[]];for(const u of l){if(r.nodeId===u.nodeId&&r.type===u.type&&r.id===u.id)continue;const{x:d,y:p}=zo(a,u,u.position,!0),f=Math.sqrt(Math.pow(d-e.x,2)+Math.pow(p-e.y,2));f>t||(f<i?(o=[{...u,x:d,y:p}],i=f):f===i&&o.push({...u,x:d,y:p}))}}if(!o.length)return null;if(o.length>1){const a=r.type==="source"?"target":"source";return o.find(l=>l.type===a)??o[0]}return o[0]}function Ec(e,t,n,r,o,i=!1){const s=r.get(e);if(!s)return null;const a=o==="strict"?s.internals.handleBounds?.[t]:[...s.internals.handleBounds?.source??[],...s.internals.handleBounds?.target??[]],l=(n?a?.find(u=>u.id===n):a?.[0])??null;return l&&i?{...l,...zo(s,l,l.position,!0)}:l}function Pc(e,t){return e||(t?.classList.contains("target")?"target":t?.classList.contains("source")?"source":null)}function Z0(e,t){let n=null;return t?n=!0:e&&!t&&(n=!1),n}const Nc=()=>!0;function B0(e,{connectionMode:t,connectionRadius:n,handleId:r,nodeId:o,edgeUpdaterType:i,isTarget:s,domNode:a,nodeLookup:l,lib:u,autoPanOnConnect:d,flowId:p,panBy:f,cancelConnection:g,onConnectStart:h,onConnect:v,onConnectEnd:w,isValidConnection:b=Nc,onReconnectEnd:x,updateConnection:$,getTransform:S,getFromHandle:E,autoPanSpeed:D,dragThreshold:O=1,handleDomNode:q}){const K=cc(e.target);let J=0,A;const{x:_,y:k}=kn(e),C=Pc(i,q),N=a?.getBoundingClientRect();let P=!1;if(!N||!C)return;const H=Ec(o,C,r,l,t);if(!H)return;let Z=kn(e,N),Y=!1,M=null,X=!1,te=null;function oe(){if(!d||!N)return;const[xe,ie]=ic(Z,N,D);f({x:xe,y:ie}),J=requestAnimationFrame(oe)}const j={...H,nodeId:o,type:C,position:H.position},G=l.get(o);let F={inProgress:!0,isValid:null,from:zo(G,j,be.Left,!0),fromHandle:j,fromPosition:j.position,fromNode:G,to:Z,toHandle:null,toPosition:Qu[j.position],toNode:null};function se(){P=!0,$(F),h?.(e,{nodeId:o,handleId:r,handleType:C})}O===0&&se();function W(xe){if(!P){const{x:ge,y:he}=kn(xe),le=ge-_,Te=he-k;if(!(le*le+Te*Te>O*O))return;se()}if(!E()||!j){ye(xe);return}const ie=S();Z=kn(xe,N),A=q0(Vo(Z,ie,!1,[1,1]),n,l,j),Y||(oe(),Y=!0);const ee=Tc(xe,{handle:A,connectionMode:t,fromNodeId:o,fromHandleId:r,fromType:s?"target":"source",isValidConnection:b,doc:K,lib:u,flowId:p,nodeLookup:l});te=ee.handleDomNode,M=ee.connection,X=Z0(!!A,ee.isValid);const re={...F,isValid:X,to:ee.toHandle&&X?Ii({x:ee.toHandle.x,y:ee.toHandle.y},ie):Z,toHandle:ee.toHandle,toPosition:X&&ee.toHandle?ee.toHandle.position:Qu[j.position],toNode:ee.toHandle?l.get(ee.toHandle.nodeId):null};X&&A&&F.toHandle&&re.toHandle&&F.toHandle.type===re.toHandle.type&&F.toHandle.nodeId===re.toHandle.nodeId&&F.toHandle.id===re.toHandle.id&&F.to.x===re.to.x&&F.to.y===re.to.y||($(re),F=re)}function ye(xe){if(P){(A||te)&&M&&X&&v?.(M);const{inProgress:ie,...ee}=F,re={...ee,toPosition:F.toHandle?F.toPosition:null};w?.(xe,re),i&&x?.(xe,re)}g(),cancelAnimationFrame(J),Y=!1,X=!1,M=null,te=null,K.removeEventListener("mousemove",W),K.removeEventListener("mouseup",ye),K.removeEventListener("touchmove",W),K.removeEventListener("touchend",ye)}K.addEventListener("mousemove",W),K.addEventListener("mouseup",ye),K.addEventListener("touchmove",W),K.addEventListener("touchend",ye)}function Tc(e,{handle:t,connectionMode:n,fromNodeId:r,fromHandleId:o,fromType:i,doc:s,lib:a,flowId:l,isValidConnection:u=Nc,nodeLookup:d}){const p=i==="target",f=t?s.querySelector(`.${a}-flow__handle[data-id="${l}-${t?.nodeId}-${t?.id}-${t?.type}"]`):null,{x:g,y:h}=kn(e),v=s.elementFromPoint(g,h),w=v?.classList.contains(`${a}-flow__handle`)?v:f,b={handleDomNode:w,isValid:!1,connection:null,toHandle:null};if(w){const x=Pc(void 0,w),$=w.getAttribute("data-nodeid"),S=w.getAttribute("data-handleid"),E=w.classList.contains("connectable"),D=w.classList.contains("connectableend");if(!$||!x)return b;const O={source:p?$:r,sourceHandle:p?S:o,target:p?r:$,targetHandle:p?o:S};b.connection=O;const q=E&&D&&(n===Kr.Strict?p&&x==="source"||!p&&x==="target":$!==r||S!==o);b.isValid=q&&u(O),b.toHandle=Ec($,x,S,d,n,!0)}return b}const Dc={onPointerDown:B0,isValid:Tc};function K0({domNode:e,panZoom:t,getTransform:n,getViewScale:r}){const o=Ft(e);function i({translateExtent:a,width:l,height:u,zoomStep:d=1,pannable:p=!0,zoomable:f=!0,inversePan:g=!1}){const h=$=>{if($.sourceEvent.type!=="wheel"||!t)return;const S=n(),E=$.sourceEvent.ctrlKey&&Cr()?10:1,D=-$.sourceEvent.deltaY*($.sourceEvent.deltaMode===1?.05:$.sourceEvent.deltaMode?1:.002)*d,O=S[2]*Math.pow(2,D*E);t.scaleTo(O)};let v=[0,0];const w=$=>{($.sourceEvent.type==="mousedown"||$.sourceEvent.type==="touchstart")&&(v=[$.sourceEvent.clientX??$.sourceEvent.touches[0].clientX,$.sourceEvent.clientY??$.sourceEvent.touches[0].clientY])},b=$=>{const S=n();if($.sourceEvent.type!=="mousemove"&&$.sourceEvent.type!=="touchmove"||!t)return;const E=[$.sourceEvent.clientX??$.sourceEvent.touches[0].clientX,$.sourceEvent.clientY??$.sourceEvent.touches[0].clientY],D=[E[0]-v[0],E[1]-v[1]];v=E;const O=r()*Math.max(S[2],Math.log(S[2]))*(g?-1:1),q={x:S[0]-D[0]*O,y:S[1]-D[1]*O},K=[[0,0],[l,u]];t.setViewportConstrained({x:q.x,y:q.y,zoom:S[2]},K,a)},x=Uu().on("start",w).on("zoom",p?b:null).on("zoom.wheel",f?h:null);o.call(x,{})}function s(){o.on("zoom",null)}return{update:i,destroy:s,pointer:on}}const j0=(e,t)=>e.x!==t.x||e.y!==t.y||e.zoom!==t.k,Zi=e=>({x:e.x,y:e.y,zoom:e.k}),ga=({x:e,y:t,zoom:n})=>Hi.translate(e,t).scale(n),Wr=(e,t)=>e.target.closest(`.${t}`),Oc=(e,t)=>t===2&&Array.isArray(e)&&e.includes(2),Y0=e=>((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2,ha=(e,t=0,n=Y0,r=()=>{})=>{const o=typeof t=="number"&&t>0;return o||r(),o?e.transition().duration(t).ease(n).on("end",r):e},Lc=e=>{const t=e.ctrlKey&&Cr()?10:1;return-e.deltaY*(e.deltaMode===1?.05:e.deltaMode?1:.002)*t};function X0({zoomPanValues:e,noWheelClassName:t,d3Selection:n,d3Zoom:r,panOnScrollMode:o,panOnScrollSpeed:i,zoomOnPinch:s,onPanZoomStart:a,onPanZoom:l,onPanZoomEnd:u}){return d=>{if(Wr(d,t))return!1;d.preventDefault(),d.stopImmediatePropagation();const p=n.property("__zoom").k||1;if(d.ctrlKey&&s){const w=on(d),b=Lc(d),x=p*Math.pow(2,b);r.scaleTo(n,x,w,d);return}const f=d.deltaMode===1?20:1;let g=o===$n.Vertical?0:d.deltaX*f,h=o===$n.Horizontal?0:d.deltaY*f;!Cr()&&d.shiftKey&&o!==$n.Vertical&&(g=d.deltaY*f,h=0),r.translateBy(n,-(g/p)*i,-(h/p)*i,{internal:!0});const v=Zi(n.property("__zoom"));clearTimeout(e.panScrollTimeout),e.isPanScrolling||(e.isPanScrolling=!0,a?.(d,v)),e.isPanScrolling&&(l?.(d,v),e.panScrollTimeout=setTimeout(()=>{u?.(d,v),e.isPanScrolling=!1},150))}}function F0({noWheelClassName:e,preventScrolling:t,d3ZoomHandler:n}){return function(r,o){const i=r.type==="wheel",s=!t&&i&&!r.ctrlKey,a=Wr(r,e);if(r.ctrlKey&&i&&a&&r.preventDefault(),s||a)return null;r.preventDefault(),n.call(this,r,o)}}function W0({zoomPanValues:e,onDraggingChange:t,onPanZoomStart:n}){return r=>{if(r.sourceEvent?.internal)return;const o=Zi(r.transform);e.mouseButton=r.sourceEvent?.button||0,e.isZoomingOrPanning=!0,e.prevViewport=o,r.sourceEvent?.type==="mousedown"&&t(!0),n&&n?.(r.sourceEvent,o)}}function G0({zoomPanValues:e,panOnDrag:t,onPaneContextMenu:n,onTransformChange:r,onPanZoom:o}){return i=>{e.usedRightMouseButton=!!(n&&Oc(t,e.mouseButton??0)),i.sourceEvent?.sync||r([i.transform.x,i.transform.y,i.transform.k]),o&&!i.sourceEvent?.internal&&o?.(i.sourceEvent,Zi(i.transform))}}function U0({zoomPanValues:e,panOnDrag:t,panOnScroll:n,onDraggingChange:r,onPanZoomEnd:o,onPaneContextMenu:i}){return s=>{if(!s.sourceEvent?.internal&&(e.isZoomingOrPanning=!1,i&&Oc(t,e.mouseButton??0)&&!e.usedRightMouseButton&&s.sourceEvent&&i(s.sourceEvent),e.usedRightMouseButton=!1,r(!1),o&&j0(e.prevViewport,s.transform))){const a=Zi(s.transform);e.prevViewport=a,clearTimeout(e.timerId),e.timerId=setTimeout(()=>{o?.(s.sourceEvent,a)},n?150:0)}}}function J0({zoomActivationKeyPressed:e,zoomOnScroll:t,zoomOnPinch:n,panOnDrag:r,panOnScroll:o,zoomOnDoubleClick:i,userSelectionActive:s,noWheelClassName:a,noPanClassName:l,lib:u}){return d=>{const p=e||t,f=n&&d.ctrlKey;if(d.button===1&&d.type==="mousedown"&&(Wr(d,`${u}-flow__node`)||Wr(d,`${u}-flow__edge`)))return!0;if(!r&&!p&&!o&&!i&&!n||s||Wr(d,a)&&d.type==="wheel"||Wr(d,l)&&(d.type!=="wheel"||o&&d.type==="wheel"&&!e)||!n&&d.ctrlKey&&d.type==="wheel")return!1;if(!n&&d.type==="touchstart"&&d.touches?.length>1)return d.preventDefault(),!1;if(!p&&!o&&!f&&d.type==="wheel"||!r&&(d.type==="mousedown"||d.type==="touchstart")||Array.isArray(r)&&!r.includes(d.button)&&d.type==="mousedown")return!1;const g=Array.isArray(r)&&r.includes(d.button)||!d.button||d.button<=1;return(!d.ctrlKey||d.type==="wheel")&&g}}function Q0({domNode:e,minZoom:t,maxZoom:n,paneClickDistance:r,translateExtent:o,viewport:i,onPanZoom:s,onPanZoomStart:a,onPanZoomEnd:l,onDraggingChange:u}){const d={isZoomingOrPanning:!1,usedRightMouseButton:!1,prevViewport:{x:0,y:0,zoom:0},mouseButton:0,timerId:void 0,panScrollTimeout:void 0,isPanScrolling:!1},p=e.getBoundingClientRect(),f=Uu().clickDistance(!In(r)||r<0?0:r).scaleExtent([t,n]).translateExtent(o),g=Ft(e).call(f);$({x:i.x,y:i.y,zoom:jr(i.zoom,t,n)},[[0,0],[p.width,p.height]],o);const h=g.on("wheel.zoom"),v=g.on("dblclick.zoom");f.wheelDelta(Lc);function w(_,k){return g?new Promise(C=>{f?.interpolate(k?.interpolate==="linear"?ko:_i).transform(ha(g,k?.duration,k?.ease,()=>C(!0)),_)}):Promise.resolve(!1)}function b({noWheelClassName:_,noPanClassName:k,onPaneContextMenu:C,userSelectionActive:N,panOnScroll:P,panOnDrag:H,panOnScrollMode:Z,panOnScrollSpeed:Y,preventScrolling:M,zoomOnPinch:X,zoomOnScroll:te,zoomOnDoubleClick:oe,zoomActivationKeyPressed:j,lib:G,onTransformChange:F}){N&&!d.isZoomingOrPanning&&x();const se=P&&!j&&!N?X0({zoomPanValues:d,noWheelClassName:_,d3Selection:g,d3Zoom:f,panOnScrollMode:Z,panOnScrollSpeed:Y,zoomOnPinch:X,onPanZoomStart:a,onPanZoom:s,onPanZoomEnd:l}):F0({noWheelClassName:_,preventScrolling:M,d3ZoomHandler:h});if(g.on("wheel.zoom",se,{passive:!1}),!N){const ye=W0({zoomPanValues:d,onDraggingChange:u,onPanZoomStart:a});f.on("start",ye);const xe=G0({zoomPanValues:d,panOnDrag:H,onPaneContextMenu:!!C,onPanZoom:s,onTransformChange:F});f.on("zoom",xe);const ie=U0({zoomPanValues:d,panOnDrag:H,panOnScroll:P,onPaneContextMenu:C,onPanZoomEnd:l,onDraggingChange:u});f.on("end",ie)}const W=J0({zoomActivationKeyPressed:j,panOnDrag:H,zoomOnScroll:te,panOnScroll:P,zoomOnDoubleClick:oe,zoomOnPinch:X,userSelectionActive:N,noPanClassName:k,noWheelClassName:_,lib:G});f.filter(W),oe?g.on("dblclick.zoom",v):g.on("dblclick.zoom",null)}function x(){f.on("zoom",null)}async function $(_,k,C){const N=ga(_),P=f?.constrain()(N,k,C);return P&&await w(P),new Promise(H=>H(P))}async function S(_,k){const C=ga(_);return await w(C,k),new Promise(N=>N(C))}function E(_){if(g){const k=ga(_),C=g.property("__zoom");(C.k!==_.zoom||C.x!==_.x||C.y!==_.y)&&f?.transform(g,k,null,{sync:!0})}}function D(){const _=g?Wu(g.node()):{x:0,y:0,k:1};return{x:_.x,y:_.y,zoom:_.k}}function O(_,k){return g?new Promise(C=>{f?.interpolate(k?.interpolate==="linear"?ko:_i).scaleTo(ha(g,k?.duration,k?.ease,()=>C(!0)),_)}):Promise.resolve(!1)}function q(_,k){return g?new Promise(C=>{f?.interpolate(k?.interpolate==="linear"?ko:_i).scaleBy(ha(g,k?.duration,k?.ease,()=>C(!0)),_)}):Promise.resolve(!1)}function K(_){f?.scaleExtent(_)}function J(_){f?.translateExtent(_)}function A(_){const k=!In(_)||_<0?0:_;f?.clickDistance(k)}return{update:b,destroy:x,setViewport:S,setViewportConstrained:$,getViewport:D,scaleTo:O,scaleBy:q,setScaleExtent:K,setTranslateExtent:J,syncViewport:E,setClickDistance:A}}var Mc;(function(e){e.Line="line",e.Handle="handle"})(Mc||(Mc={}));var em=Q("<div><!></div>");function Qn(e,t){de(t,!0);let n=y(t,"id",7,null),r=y(t,"type",7,"source"),o=y(t,"position",23,()=>be.Top),i=y(t,"style",7),s=y(t,"class",7),a=y(t,"isConnectable",7),l=y(t,"isConnectableStart",7,!0),u=y(t,"isConnectableEnd",7,!0),d=y(t,"isValidConnection",7),p=y(t,"onconnect",7),f=y(t,"ondisconnect",7),g=y(t,"children",7),h=Ke(t,["$$slots","$$events","$$legacy","$$host","id","type","position","style","class","isConnectable","isConnectableStart","isConnectableEnd","isValidConnection","onconnect","ondisconnect","children"]);const v=Xn("svelteflow__node_id"),w=Xn("svelteflow__node_connectable");let b=T(()=>r()==="target"),x=T(()=>a()!==void 0?a():w.value),$=ln(),S=T(()=>$.ariaLabelConfig),E=null;$l(()=>{if(p()||f()){$.edges;let M=$.connectionLookup.get(`${v}-${r()}${n()?`-${n()}`:""}`);if(E&&!e0(M,E)){const X=M??new Map;ec(E,X,f()),ec(X,E,p())}E=new Map(M)}});let D=T(()=>{if(!$.connection.inProgress)return[!1,!1,!1,!1,null];const{fromHandle:M,toHandle:X,isValid:te}=$.connection,oe=M&&M.nodeId===v&&M.type===r()&&M.id===n(),j=X&&X.nodeId===v&&X.type===r()&&X.id===n(),G=$.connectionMode===Kr.Strict?M?.type!==r():v!==M?.nodeId||n()!==M?.id;return[!0,oe,j,G,j&&te]}),O=T(()=>io(c(D),5)),q=T(()=>c(O)[0]),K=T(()=>c(O)[1]),J=T(()=>c(O)[2]),A=T(()=>c(O)[3]),_=T(()=>c(O)[4]);function k(M){const X=$.onbeforeconnect?$.onbeforeconnect(M):M;X&&($.addEdge(X),$.onconnect?.(M))}function C(M){const X=fc(M);M.currentTarget&&(X&&M.button===0||!X)&&Dc.onPointerDown(M,{handleId:n(),nodeId:v,isTarget:c(b),connectionRadius:$.connectionRadius,domNode:$.domNode,nodeLookup:$.nodeLookup,connectionMode:$.connectionMode,lib:"svelte",autoPanOnConnect:$.autoPanOnConnect,flowId:$.flowId,isValidConnection:d()??$.isValidConnection,updateConnection:$.updateConnection,cancelConnection:$.cancelConnection,panBy:$.panBy,onConnect:k,onConnectStart:(te,oe)=>{$.onconnectstart?.(te,{nodeId:oe.nodeId,handleId:oe.handleId,handleType:oe.handleType})},onConnectEnd:(te,oe)=>{$.onconnectend?.(te,oe)},getTransform:()=>[$.viewport.x,$.viewport.y,$.viewport.zoom],getFromHandle:()=>$.connection.fromHandle,dragThreshold:$.connectionDragThreshold,handleDomNode:M.currentTarget})}function N(M){if(!v||!$.clickConnectStartHandle&&!l())return;if(!$.clickConnectStartHandle){$.onclickconnectstart?.(M,{nodeId:v,handleId:n(),handleType:r()}),$.clickConnectStartHandle={nodeId:v,type:r(),id:n()};return}const X=cc(M.target),te=d()??$.isValidConnection,{connectionMode:oe,clickConnectStartHandle:j,flowId:G,nodeLookup:F}=$,{connection:se,isValid:W}=Dc.isValid(M,{handle:{nodeId:v,id:n(),type:r()},connectionMode:oe,fromNodeId:j.nodeId,fromHandleId:j.id??null,fromType:j.type,isValidConnection:te,flowId:G,doc:X,lib:"svelte",nodeLookup:F});W&&se&&k(se);const ye=structuredClone(Ja($.connection));delete ye.inProgress,ye.toPosition=ye.toHandle?ye.toHandle.position:null,$.onclickconnectend?.(M,ye),$.clickConnectStartHandle=null}var P={get id(){return n()},set id(M=null){n(M),m()},get type(){return r()},set type(M="source"){r(M),m()},get position(){return o()},set position(M=be.Top){o(M),m()},get style(){return i()},set style(M){i(M),m()},get class(){return s()},set class(M){s(M),m()},get isConnectable(){return a()},set isConnectable(M){a(M),m()},get isConnectableStart(){return l()},set isConnectableStart(M=!0){l(M),m()},get isConnectableEnd(){return u()},set isConnectableEnd(M=!0){u(M),m()},get isValidConnection(){return d()},set isValidConnection(M){d(M),m()},get onconnect(){return p()},set onconnect(M){p(M),m()},get ondisconnect(){return f()},set ondisconnect(M){f(M),m()},get children(){return g()},set children(M){g(M),m()}},H=em(),Z=()=>{};ut(H,M=>({"data-handleid":n(),"data-nodeid":v,"data-handlepos":o(),"data-id":`${$.flowId??""}-${v??""}-${n()??"null"??""}-${r()??""}`,class:["svelte-flow__handle",`svelte-flow__handle-${o()}`,$.noDragClass,$.noPanClass,o(),s()],onmousedown:C,ontouchstart:C,onclick:$.clickConnect?N:void 0,onkeypress:Z,style:i(),role:"button","aria-label":c(S)["handle.ariaLabel"],tabindex:"-1",...h,[Un]:M}),[()=>({valid:c(_),connectingto:c(J),connectingfrom:c(K),source:!c(b),target:c(b),connectablestart:l(),connectableend:u(),connectable:c(x),connectionindicator:c(x)&&(!c(q)||c(A))&&(c(q)||$.clickConnectStartHandle?u():l())})]);var Y=I(H);return tt(Y,()=>g()??ht),R(H),L(e,H),fe(P)}ue(Qn,{id:{},type:{},position:{},style:{},class:{},isConnectable:{},isConnectableStart:{},isConnectableEnd:{},isValidConnection:{},onconnect:{},ondisconnect:{},children:{}},[],[],!0);var tm=Q("<!> <!>",1);function va(e,t){de(t,!0);let n=y(t,"data",7),r=y(t,"targetPosition",23,()=>be.Top),o=y(t,"sourcePosition",23,()=>be.Bottom);var i={get data(){return n()},set data(d){n(d),m()},get targetPosition(){return r()},set targetPosition(d=be.Top){r(d),m()},get sourcePosition(){return o()},set sourcePosition(d=be.Bottom){o(d),m()}},s=tm(),a=ae(s);Qn(a,{type:"target",get position(){return r()}});var l=V(a),u=V(l);return Qn(u,{type:"source",get position(){return o()}}),Se(()=>Xe(l,` ${n()?.label??""} `)),L(e,s),fe(i)}ue(va,{data:{},targetPosition:{},sourcePosition:{}},[],[],!0);var nm=Q(" <!>",1);function Hc(e,t){de(t,!0);let n=y(t,"data",23,()=>({label:"Node"})),r=y(t,"sourcePosition",23,()=>be.Bottom);var o={get data(){return n()},set data(l={label:"Node"}){n(l),m()},get sourcePosition(){return r()},set sourcePosition(l=be.Bottom){r(l),m()}};we();var i=nm(),s=ae(i),a=V(s);return Qn(a,{type:"source",get position(){return r()}}),Se(()=>Xe(s,`${n()?.label??""} `)),L(e,i),fe(o)}ue(Hc,{data:{},sourcePosition:{}},[],[],!0);var rm=Q(" <!>",1);function Vc(e,t){de(t,!0);let n=y(t,"data",23,()=>({label:"Node"})),r=y(t,"targetPosition",23,()=>be.Top);var o={get data(){return n()},set data(l={label:"Node"}){n(l),m()},get targetPosition(){return r()},set targetPosition(l=be.Top){r(l),m()}};we();var i=rm(),s=ae(i),a=V(s);return Qn(a,{type:"target",get position(){return r()}}),Se(()=>Xe(s,`${n()?.label??""} `)),L(e,i),fe(o)}ue(Vc,{data:{},targetPosition:{}},[],[],!0);function zc(e,t){}ue(zc,{},[],[],!0);function ma(e,t,n){if(!n||!t)return;const r=n==="root"?t:t.querySelector(`.svelte-flow__${n}`);r&&r.appendChild(e)}function Ac(e,t){const n=T(ln),r=T(()=>c(n).domNode);let o;return c(r)?ma(e,c(r),t):o=bs(()=>{et(()=>{ma(e,c(r),t),o?.()})}),{async update(i){ma(e,c(r),i)},destroy(){e.parentNode&&e.parentNode.removeChild(e),o?.()}}}function Rc(){let e=De(typeof window>"u");if(c(e)){const t=bs(()=>{et(()=>{U(e,!1),t?.()})})}return{get value(){return c(e)}}}const Ic=e=>n0(e),om=e=>tc(e);function _n(e){return e===void 0?void 0:`${e}px`}const Bi={ArrowUp:{x:0,y:-1},ArrowDown:{x:0,y:1},ArrowLeft:{x:-1,y:0},ArrowRight:{x:1,y:0}};var im=Q("<div><!></div>");const sm={hash:"svelte-w2n27y",code:".transparent.svelte-w2n27y {background:transparent;}"};function qc(e,t){de(t,!0),qe(e,sm);let n=y(t,"x",7,0),r=y(t,"y",7,0),o=y(t,"width",7),i=y(t,"height",7),s=y(t,"selectEdgeOnClick",7,!1),a=y(t,"transparent",7,!1),l=y(t,"class",7),u=y(t,"children",7),d=Ke(t,["$$slots","$$events","$$legacy","$$host","x","y","width","height","selectEdgeOnClick","transparent","class","children"]);const p=ln(),f=Xn("svelteflow__edge_id");let g=T(()=>p.visible.edges.get(f)?.zIndex);var h={get x(){return n()},set x(x=0){n(x),m()},get y(){return r()},set y(x=0){r(x),m()},get width(){return o()},set width(x){o(x),m()},get height(){return i()},set height(x){i(x),m()},get selectEdgeOnClick(){return s()},set selectEdgeOnClick(x=!1){s(x),m()},get transparent(){return a()},set transparent(x=!1){a(x),m()},get class(){return l()},set class(x){l(x),m()},get children(){return u()},set children(x){u(x),m()}},v=im(),w=()=>{s()&&f&&p.handleEdgeSelection(f)};ut(v,x=>({class:["svelte-flow__edge-label",{transparent:a()},l()],tabindex:"-1",onclick:w,...d,[yn]:x}),[()=>({display:Rc().value?"none":void 0,cursor:s()?"pointer":void 0,transform:`translate(-50%, -50%) translate(${n()??""}px,${r()??""}px)`,"pointer-events":"all",width:_n(o()),height:_n(i()),"z-index":c(g)})],void 0,"svelte-w2n27y");var b=I(v);return tt(b,()=>u()??ht),R(v),$t(v,(x,$)=>Ac?.(x,$),()=>"edge-labels"),L(e,v),fe(h)}ue(qc,{x:{},y:{},width:{},height:{},selectEdgeOnClick:{},transparent:{},class:{},children:{}},[],[],!0);var am=me("<path></path>"),lm=me('<path fill="none"></path><!><!>',1);function Ao(e,t){de(t,!0);let n=y(t,"id",7),r=y(t,"path",7),o=y(t,"label",7),i=y(t,"labelX",7),s=y(t,"labelY",7),a=y(t,"labelStyle",7),l=y(t,"markerStart",7),u=y(t,"markerEnd",7),d=y(t,"style",7),p=y(t,"interactionWidth",7,20),f=y(t,"class",7),g=Ke(t,["$$slots","$$events","$$legacy","$$host","id","path","label","labelX","labelY","labelStyle","markerStart","markerEnd","style","interactionWidth","class"]);var h={get id(){return n()},set id(E){n(E),m()},get path(){return r()},set path(E){r(E),m()},get label(){return o()},set label(E){o(E),m()},get labelX(){return i()},set labelX(E){i(E),m()},get labelY(){return s()},set labelY(E){s(E),m()},get labelStyle(){return a()},set labelStyle(E){a(E),m()},get markerStart(){return l()},set markerStart(E){l(E),m()},get markerEnd(){return u()},set markerEnd(E){u(E),m()},get style(){return d()},set style(E){d(E),m()},get interactionWidth(){return p()},set interactionWidth(E=20){p(E),m()},get class(){return f()},set class(E){f(E),m()}},v=lm(),w=ae(v),b=V(w);{var x=E=>{var D=am();ut(D,()=>({d:r(),"stroke-opacity":0,"stroke-width":p(),fill:"none",class:"svelte-flow__edge-interaction",...g})),L(E,D)};ce(b,E=>{p()>0&&E(x)})}var $=V(b);{var S=E=>{qc(E,{get x(){return i()},get y(){return s()},get style(){return a()},selectEdgeOnClick:!0,children:(D,O)=>{we();var q=Ee();Se(()=>Xe(q,o())),L(D,q)},$$slots:{default:!0}})};ce($,E=>{o()&&E(S)})}return Se(()=>{Ce(w,"id",n()),Ce(w,"d",r()),Mt(w,0,Hn(["svelte-flow__edge-path",f()])),Ce(w,"marker-start",l()),Ce(w,"marker-end",u()),mt(w,d())}),L(e,v),fe(h)}ue(Ao,{id:{},path:{},label:{},labelX:{},labelY:{},labelStyle:{},markerStart:{},markerEnd:{},style:{},interactionWidth:{},class:{}},[],[],!0);function ya(e,t){de(t,!0);let n=y(t,"id",7),r=y(t,"interactionWidth",7),o=y(t,"label",7),i=y(t,"labelStyle",7),s=y(t,"markerEnd",7),a=y(t,"markerStart",7),l=y(t,"pathOptions",7),u=y(t,"sourcePosition",7),d=y(t,"sourceX",7),p=y(t,"sourceY",7),f=y(t,"style",7),g=y(t,"targetPosition",7),h=y(t,"targetX",7),v=y(t,"targetY",7),w=T(()=>hc({sourceX:d(),sourceY:p(),targetX:h(),targetY:v(),sourcePosition:u(),targetPosition:g(),curvature:l()?.curvature})),b=T(()=>io(c(w),3)),x=T(()=>c(b)[0]),$=T(()=>c(b)[1]),S=T(()=>c(b)[2]);var E={get id(){return n()},set id(D){n(D),m()},get interactionWidth(){return r()},set interactionWidth(D){r(D),m()},get label(){return o()},set label(D){o(D),m()},get labelStyle(){return i()},set labelStyle(D){i(D),m()},get markerEnd(){return s()},set markerEnd(D){s(D),m()},get markerStart(){return a()},set markerStart(D){a(D),m()},get pathOptions(){return l()},set pathOptions(D){l(D),m()},get sourcePosition(){return u()},set sourcePosition(D){u(D),m()},get sourceX(){return d()},set sourceX(D){d(D),m()},get sourceY(){return p()},set sourceY(D){p(D),m()},get style(){return f()},set style(D){f(D),m()},get targetPosition(){return g()},set targetPosition(D){g(D),m()},get targetX(){return h()},set targetX(D){h(D),m()},get targetY(){return v()},set targetY(D){v(D),m()}};return Ao(e,{get id(){return n()},get path(){return c(x)},get labelX(){return c($)},get labelY(){return c(S)},get label(){return o()},get labelStyle(){return i()},get markerStart(){return a()},get markerEnd(){return s()},get interactionWidth(){return r()},get style(){return f()}}),fe(E)}ue(ya,{id:{},interactionWidth:{},label:{},labelStyle:{},markerEnd:{},markerStart:{},pathOptions:{},sourcePosition:{},sourceX:{},sourceY:{},style:{},targetPosition:{},targetX:{},targetY:{}},[],[],!0);function Zc(e,t){de(t,!0);let n=y(t,"interactionWidth",7),r=y(t,"label",7),o=y(t,"labelStyle",7),i=y(t,"style",7),s=y(t,"markerEnd",7),a=y(t,"markerStart",7),l=y(t,"sourcePosition",7),u=y(t,"sourceX",7),d=y(t,"sourceY",7),p=y(t,"targetPosition",7),f=y(t,"targetX",7),g=y(t,"targetY",7),h=T(()=>la({sourceX:u(),sourceY:d(),targetX:f(),targetY:g(),sourcePosition:l(),targetPosition:p()})),v=T(()=>io(c(h),3)),w=T(()=>c(v)[0]),b=T(()=>c(v)[1]),x=T(()=>c(v)[2]);var $={get interactionWidth(){return n()},set interactionWidth(S){n(S),m()},get label(){return r()},set label(S){r(S),m()},get labelStyle(){return o()},set labelStyle(S){o(S),m()},get style(){return i()},set style(S){i(S),m()},get markerEnd(){return s()},set markerEnd(S){s(S),m()},get markerStart(){return a()},set markerStart(S){a(S),m()},get sourcePosition(){return l()},set sourcePosition(S){l(S),m()},get sourceX(){return u()},set sourceX(S){u(S),m()},get sourceY(){return d()},set sourceY(S){d(S),m()},get targetPosition(){return p()},set targetPosition(S){p(S),m()},get targetX(){return f()},set targetX(S){f(S),m()},get targetY(){return g()},set targetY(S){g(S),m()}};return Ao(e,{get path(){return c(w)},get labelX(){return c(b)},get labelY(){return c(x)},get label(){return r()},get labelStyle(){return o()},get markerStart(){return a()},get markerEnd(){return s()},get interactionWidth(){return n()},get style(){return i()}}),fe($)}ue(Zc,{interactionWidth:{},label:{},labelStyle:{},style:{},markerEnd:{},markerStart:{},sourcePosition:{},sourceX:{},sourceY:{},targetPosition:{},targetX:{},targetY:{}},[],[],!0);function Bc(e,t){de(t,!0);let n=y(t,"sourceX",7),r=y(t,"sourceY",7),o=y(t,"targetX",7),i=y(t,"targetY",7),s=y(t,"label",7),a=y(t,"labelStyle",7),l=y(t,"markerStart",7),u=y(t,"markerEnd",7),d=y(t,"interactionWidth",7),p=y(t,"style",7),f=T(()=>mc({sourceX:n(),sourceY:r(),targetX:o(),targetY:i()})),g=T(()=>io(c(f),3)),h=T(()=>c(g)[0]),v=T(()=>c(g)[1]),w=T(()=>c(g)[2]);var b={get sourceX(){return n()},set sourceX(x){n(x),m()},get sourceY(){return r()},set sourceY(x){r(x),m()},get targetX(){return o()},set targetX(x){o(x),m()},get targetY(){return i()},set targetY(x){i(x),m()},get label(){return s()},set label(x){s(x),m()},get labelStyle(){return a()},set labelStyle(x){a(x),m()},get markerStart(){return l()},set markerStart(x){l(x),m()},get markerEnd(){return u()},set markerEnd(x){u(x),m()},get interactionWidth(){return d()},set interactionWidth(x){d(x),m()},get style(){return p()},set style(x){p(x),m()}};return Ao(e,{get path(){return c(h)},get labelX(){return c(v)},get labelY(){return c(w)},get label(){return s()},get labelStyle(){return a()},get markerStart(){return l()},get markerEnd(){return u()},get interactionWidth(){return d()},get style(){return p()}}),fe(b)}ue(Bc,{sourceX:{},sourceY:{},targetX:{},targetY:{},label:{},labelStyle:{},markerStart:{},markerEnd:{},interactionWidth:{},style:{}},[],[],!0);function Kc(e,t){de(t,!0);let n=y(t,"sourceX",7),r=y(t,"sourceY",7),o=y(t,"sourcePosition",7),i=y(t,"targetX",7),s=y(t,"targetY",7),a=y(t,"targetPosition",7),l=y(t,"label",7),u=y(t,"labelStyle",7),d=y(t,"markerStart",7),p=y(t,"markerEnd",7),f=y(t,"interactionWidth",7),g=y(t,"style",7),h=T(()=>la({sourceX:n(),sourceY:r(),targetX:i(),targetY:s(),sourcePosition:o(),targetPosition:a(),borderRadius:0})),v=T(()=>io(c(h),3)),w=T(()=>c(v)[0]),b=T(()=>c(v)[1]),x=T(()=>c(v)[2]);var $={get sourceX(){return n()},set sourceX(S){n(S),m()},get sourceY(){return r()},set sourceY(S){r(S),m()},get sourcePosition(){return o()},set sourcePosition(S){o(S),m()},get targetX(){return i()},set targetX(S){i(S),m()},get targetY(){return s()},set targetY(S){s(S),m()},get targetPosition(){return a()},set targetPosition(S){a(S),m()},get label(){return l()},set label(S){l(S),m()},get labelStyle(){return u()},set labelStyle(S){u(S),m()},get markerStart(){return d()},set markerStart(S){d(S),m()},get markerEnd(){return p()},set markerEnd(S){p(S),m()},get interactionWidth(){return f()},set interactionWidth(S){f(S),m()},get style(){return g()},set style(S){g(S),m()}};return Ao(e,{get path(){return c(w)},get labelX(){return c(b)},get labelY(){return c(x)},get label(){return l()},get labelStyle(){return u()},get markerStart(){return d()},get markerEnd(){return p()},get interactionWidth(){return f()},get style(){return g()}}),fe($)}ue(Kc,{sourceX:{},sourceY:{},sourcePosition:{},targetX:{},targetY:{},targetPosition:{},label:{},labelStyle:{},markerStart:{},markerEnd:{},interactionWidth:{},style:{}},[],[],!0);class um{#t;#e;constructor(t,n){this.#t=t,this.#e=xp(n)}get current(){return this.#e(),this.#t()}}const cm=/\(.+\)/,dm=new Set(["all","print","screen","and","or","not","only"]);class fm extends um{constructor(t,n){let r=cm.test(t)||t.split(/[\s,]+/).some(i=>dm.has(i.trim()))?t:`(${t})`;const o=window.matchMedia(r);super(()=>o.matches,i=>Ss(o,"change",i))}}function pm(e,t,n,r){const o=new Map;return oa(e,{x:0,y:0,width:n,height:r},t,!0).forEach(i=>{o.set(i.id,i)}),o}function jc(e){const{edges:t,defaultEdgeOptions:n,nodeLookup:r,previousEdges:o,connectionMode:i,onerror:s,onlyRenderVisible:a,elevateEdgesOnSelect:l}=e,u=new Map;for(const d of t){const p=r.get(d.source),f=r.get(d.target);if(!p||!f)continue;if(a){const{visibleNodes:v,transform:w,width:b,height:x}=e;if(v0({sourceNode:p,targetNode:f,width:b,height:x,transform:w}))v.set(p.id,p),v.set(f.id,f);else continue}const g=o.get(d.id);if(g&&d===g.edge&&p==g.sourceNode&&f==g.targetNode){u.set(d.id,g);continue}const h=$0({id:d.id,sourceNode:p,targetNode:f,sourceHandle:d.sourceHandle||null,targetHandle:d.targetHandle||null,connectionMode:i,onError:s});h&&u.set(d.id,{...n,...d,...h,zIndex:h0({selected:d.selected,zIndex:d.zIndex??n.zIndex,sourceNode:p,targetNode:f,elevateOnSelect:l}),sourceNode:p,targetNode:f,edge:d})}return u}const Yc={input:Hc,output:Vc,default:va,group:zc},Xc={straight:Bc,smoothstep:Zc,default:ya,step:Kc};function gm(e,t,n,r,o,i){if(t&&!n&&r&&o){const s=Lo(i,{filter:a=>!!((a.width||a.initialWidth)&&(a.height||a.initialHeight))});return sa(s,r,o,.5,2,.1)}else return n??{x:0,y:0,zoom:1}}function hm(e){class t{#t=T(()=>e.props.id??"1");get flowId(){return c(this.#t)}set flowId(r){U(this.#t,r)}#e=De(null);get domNode(){return c(this.#e)}set domNode(r){U(this.#e,r)}#n=De(null);get panZoom(){return c(this.#n)}set panZoom(r){U(this.#n,r)}#o=De(e.width??0);get width(){return c(this.#o)}set width(r){U(this.#o,r)}#c=De(e.height??0);get height(){return c(this.#c)}set height(r){U(this.#c,r)}#i=T(()=>{const r=P0(e.nodes,this.nodeLookup,this.parentLookup,{nodeExtent:this.nodeExtent,nodeOrigin:this.nodeOrigin,elevateNodesOnSelect:e.props.elevateNodesOnSelect??!0,checkEquality:!0});return this.fitViewQueued&&r&&(this.fitViewOptions?.duration?this.resolveFitView():queueMicrotask(()=>{this.resolveFitView()})),r});get nodesInitialized(){return c(this.#i)}set nodesInitialized(r){U(this.#i,r)}#a=T(()=>this.panZoom!==null);get viewportInitialized(){return c(this.#a)}set viewportInitialized(r){U(this.#a,r)}#s=T(()=>(M0(this.connectionLookup,this.edgeLookup,e.edges),e.edges));get _edges(){return c(this.#s)}set _edges(r){U(this.#s,r)}get nodes(){return this.nodesInitialized,e.nodes}set nodes(r){e.nodes=r}get edges(){return this._edges}set edges(r){e.edges=r}_prevSelectedNodes=[];_prevSelectedNodeIds=new Set;#r=T(()=>{const r=this._prevSelectedNodeIds.size,o=new Set,i=this.nodes.filter(s=>(s.selected&&(o.add(s.id),this._prevSelectedNodeIds.delete(s.id)),s.selected));return(r!==o.size||this._prevSelectedNodeIds.size>0)&&(this._prevSelectedNodes=i),this._prevSelectedNodeIds=o,this._prevSelectedNodes});get selectedNodes(){return c(this.#r)}set selectedNodes(r){U(this.#r,r)}_prevSelectedEdges=[];_prevSelectedEdgeIds=new Set;#l=T(()=>{const r=this._prevSelectedEdgeIds.size,o=new Set,i=this.edges.filter(s=>(s.selected&&(o.add(s.id),this._prevSelectedEdgeIds.delete(s.id)),s.selected));return(r!==o.size||this._prevSelectedEdgeIds.size>0)&&(this._prevSelectedEdges=i),this._prevSelectedEdgeIds=o,this._prevSelectedEdges});get selectedEdges(){return c(this.#l)}set selectedEdges(r){U(this.#l,r)}selectionChangeHandlers=new Map;nodeLookup=new Map;parentLookup=new Map;connectionLookup=new Map;edgeLookup=new Map;_prevVisibleEdges=new Map;#d=T(()=>{const{nodes:r,_edges:o,_prevVisibleEdges:i,nodeLookup:s,connectionMode:a,onerror:l,onlyRenderVisibleElements:u,defaultEdgeOptions:d}=this;let p,f;const g={edges:o,defaultEdgeOptions:d,previousEdges:i,nodeLookup:s,connectionMode:a,elevateEdgesOnSelect:e.props.elevateEdgesOnSelect??!0,onerror:l};if(u){const{viewport:h,width:v,height:w}=this,b=[h.x,h.y,h.zoom];p=pm(s,b,v,w),f=jc({...g,onlyRenderVisible:!0,visibleNodes:p,transform:b,width:v,height:w})}else p=this.nodeLookup,f=jc(g);return{nodes:p,edges:f}});get visible(){return c(this.#d)}set visible(r){U(this.#d,r)}#f=T(()=>e.props.nodesDraggable??!0);get nodesDraggable(){return c(this.#f)}set nodesDraggable(r){U(this.#f,r)}#g=T(()=>e.props.nodesConnectable??!0);get nodesConnectable(){return c(this.#g)}set nodesConnectable(r){U(this.#g,r)}#u=T(()=>e.props.elementsSelectable??!0);get elementsSelectable(){return c(this.#u)}set elementsSelectable(r){U(this.#u,r)}#p=T(()=>e.props.nodesFocusable??!0);get nodesFocusable(){return c(this.#p)}set nodesFocusable(r){U(this.#p,r)}#h=T(()=>e.props.edgesFocusable??!0);get edgesFocusable(){return c(this.#h)}set edgesFocusable(r){U(this.#h,r)}#v=T(()=>e.props.disableKeyboardA11y??!1);get disableKeyboardA11y(){return c(this.#v)}set disableKeyboardA11y(r){U(this.#v,r)}#m=T(()=>e.props.minZoom??.5);get minZoom(){return c(this.#m)}set minZoom(r){U(this.#m,r)}#y=T(()=>e.props.maxZoom??2);get maxZoom(){return c(this.#y)}set maxZoom(r){U(this.#y,r)}#w=T(()=>e.props.nodeOrigin??[0,0]);get nodeOrigin(){return c(this.#w)}set nodeOrigin(r){U(this.#w,r)}#b=T(()=>e.props.nodeExtent??ta);get nodeExtent(){return c(this.#b)}set nodeExtent(r){U(this.#b,r)}#x=T(()=>e.props.translateExtent??ta);get translateExtent(){return c(this.#x)}set translateExtent(r){U(this.#x,r)}#C=T(()=>e.props.defaultEdgeOptions??{});get defaultEdgeOptions(){return c(this.#C)}set defaultEdgeOptions(r){U(this.#C,r)}#$=T(()=>e.props.nodeDragThreshold??1);get nodeDragThreshold(){return c(this.#$)}set nodeDragThreshold(r){U(this.#$,r)}#k=T(()=>e.props.autoPanOnNodeDrag??!0);get autoPanOnNodeDrag(){return c(this.#k)}set autoPanOnNodeDrag(r){U(this.#k,r)}#_=T(()=>e.props.autoPanOnConnect??!0);get autoPanOnConnect(){return c(this.#_)}set autoPanOnConnect(r){U(this.#_,r)}#S=T(()=>e.props.autoPanOnNodeFocus??!0);get autoPanOnNodeFocus(){return c(this.#S)}set autoPanOnNodeFocus(r){U(this.#S,r)}#E=T(()=>e.props.connectionDragThreshold??1);get connectionDragThreshold(){return c(this.#E)}set connectionDragThreshold(r){U(this.#E,r)}fitViewQueued=e.props.fitView??!1;fitViewOptions=e.props.fitViewOptions;fitViewResolver=null;#P=T(()=>e.props.snapGrid??null);get snapGrid(){return c(this.#P)}set snapGrid(r){U(this.#P,r)}#N=De(!1);get dragging(){return c(this.#N)}set dragging(r){U(this.#N,r)}#T=De(null);get selectionRect(){return c(this.#T)}set selectionRect(r){U(this.#T,r)}#D=De(!1);get selectionKeyPressed(){return c(this.#D)}set selectionKeyPressed(r){U(this.#D,r)}#O=De(!1);get multiselectionKeyPressed(){return c(this.#O)}set multiselectionKeyPressed(r){U(this.#O,r)}#L=De(!1);get deleteKeyPressed(){return c(this.#L)}set deleteKeyPressed(r){U(this.#L,r)}#M=De(!1);get panActivationKeyPressed(){return c(this.#M)}set panActivationKeyPressed(r){U(this.#M,r)}#H=De(!1);get zoomActivationKeyPressed(){return c(this.#H)}set zoomActivationKeyPressed(r){U(this.#H,r)}#V=De(null);get selectionRectMode(){return c(this.#V)}set selectionRectMode(r){U(this.#V,r)}#z=De("");get ariaLiveMessage(){return c(this.#z)}set ariaLiveMessage(r){U(this.#z,r)}#A=T(()=>e.props.selectionMode??Vi.Partial);get selectionMode(){return c(this.#A)}set selectionMode(r){U(this.#A,r)}#R=T(()=>({...Yc,...e.props.nodeTypes}));get nodeTypes(){return c(this.#R)}set nodeTypes(r){U(this.#R,r)}#I=T(()=>({...Xc,...e.props.edgeTypes}));get edgeTypes(){return c(this.#I)}set edgeTypes(r){U(this.#I,r)}#q=T(()=>e.props.noPanClass??"nopan");get noPanClass(){return c(this.#q)}set noPanClass(r){U(this.#q,r)}#Z=T(()=>e.props.noDragClass??"nodrag");get noDragClass(){return c(this.#Z)}set noDragClass(r){U(this.#Z,r)}#B=T(()=>e.props.noWheelClass??"nowheel");get noWheelClass(){return c(this.#B)}set noWheelClass(r){U(this.#B,r)}#K=T(()=>f0(e.props.ariaLabelConfig));get ariaLabelConfig(){return c(this.#K)}set ariaLabelConfig(r){U(this.#K,r)}#j=De(gm(this.nodesInitialized,e.props.fitView,e.props.initialViewport,this.width,this.height,this.nodeLookup));get _viewport(){return c(this.#j)}set _viewport(r){U(this.#j,r)}get viewport(){return e.viewport??this._viewport}set viewport(r){e.viewport&&(e.viewport=r),this._viewport=r}#Y=De(na);get _connection(){return c(this.#Y)}set _connection(r){U(this.#Y,r)}#X=T(()=>this._connection.inProgress?{...this._connection,to:Vo(this._connection.to,[this.viewport.x,this.viewport.y,this.viewport.zoom])}:this._connection);get connection(){return c(this.#X)}set connection(r){U(this.#X,r)}#F=T(()=>e.props.connectionMode??Kr.Strict);get connectionMode(){return c(this.#F)}set connectionMode(r){U(this.#F,r)}#W=T(()=>e.props.connectionRadius??20);get connectionRadius(){return c(this.#W)}set connectionRadius(r){U(this.#W,r)}#G=T(()=>e.props.isValidConnection??(()=>!0));get isValidConnection(){return c(this.#G)}set isValidConnection(r){U(this.#G,r)}#U=T(()=>e.props.selectNodesOnDrag??!0);get selectNodesOnDrag(){return c(this.#U)}set selectNodesOnDrag(r){U(this.#U,r)}#J=T(()=>e.props.defaultMarkerColor===void 0?"#b1b1b7":e.props.defaultMarkerColor);get defaultMarkerColor(){return c(this.#J)}set defaultMarkerColor(r){U(this.#J,r)}#Q=T(()=>k0(e.edges,{defaultColor:this.defaultMarkerColor,id:this.flowId,defaultMarkerStart:this.defaultEdgeOptions.markerStart,defaultMarkerEnd:this.defaultEdgeOptions.markerEnd}));get markers(){return c(this.#Q)}set markers(r){U(this.#Q,r)}#ee=T(()=>e.props.onlyRenderVisibleElements??!1);get onlyRenderVisibleElements(){return c(this.#ee)}set onlyRenderVisibleElements(r){U(this.#ee,r)}#te=T(()=>e.props.onflowerror??l0);get onerror(){return c(this.#te)}set onerror(r){U(this.#te,r)}#ne=T(()=>e.props.ondelete);get ondelete(){return c(this.#ne)}set ondelete(r){U(this.#ne,r)}#re=T(()=>e.props.onbeforedelete);get onbeforedelete(){return c(this.#re)}set onbeforedelete(r){U(this.#re,r)}#oe=T(()=>e.props.onbeforeconnect);get onbeforeconnect(){return c(this.#oe)}set onbeforeconnect(r){U(this.#oe,r)}#ie=T(()=>e.props.onconnect);get onconnect(){return c(this.#ie)}set onconnect(r){U(this.#ie,r)}#se=T(()=>e.props.onconnectstart);get onconnectstart(){return c(this.#se)}set onconnectstart(r){U(this.#se,r)}#ae=T(()=>e.props.onconnectend);get onconnectend(){return c(this.#ae)}set onconnectend(r){U(this.#ae,r)}#le=T(()=>e.props.onbeforereconnect);get onbeforereconnect(){return c(this.#le)}set onbeforereconnect(r){U(this.#le,r)}#ue=T(()=>e.props.onreconnect);get onreconnect(){return c(this.#ue)}set onreconnect(r){U(this.#ue,r)}#ce=T(()=>e.props.onreconnectstart);get onreconnectstart(){return c(this.#ce)}set onreconnectstart(r){U(this.#ce,r)}#de=T(()=>e.props.onreconnectend);get onreconnectend(){return c(this.#de)}set onreconnectend(r){U(this.#de,r)}#fe=T(()=>e.props.clickConnect??!0);get clickConnect(){return c(this.#fe)}set clickConnect(r){U(this.#fe,r)}#pe=T(()=>e.props.onclickconnectstart);get onclickconnectstart(){return c(this.#pe)}set onclickconnectstart(r){U(this.#pe,r)}#ge=T(()=>e.props.onclickconnectend);get onclickconnectend(){return c(this.#ge)}set onclickconnectend(r){U(this.#ge,r)}#he=De(null);get clickConnectStartHandle(){return c(this.#he)}set clickConnectStartHandle(r){U(this.#he,r)}#ve=T(()=>e.props.onselectiondrag);get onselectiondrag(){return c(this.#ve)}set onselectiondrag(r){U(this.#ve,r)}#me=T(()=>e.props.onselectiondragstart);get onselectiondragstart(){return c(this.#me)}set onselectiondragstart(r){U(this.#me,r)}#ye=T(()=>e.props.onselectiondragstop);get onselectiondragstop(){return c(this.#ye)}set onselectiondragstop(r){U(this.#ye,r)}resolveFitView=async()=>{this.panZoom&&(await s0({nodes:this.nodeLookup,width:this.width,height:this.height,panZoom:this.panZoom,minZoom:this.minZoom,maxZoom:this.maxZoom},this.fitViewOptions),this.fitViewResolver?.resolve(!0),this.fitViewQueued=!1,this.fitViewOptions=void 0,this.fitViewResolver=null)};_prefersDark=new fm("(prefers-color-scheme: dark)",e.props.colorModeSSR==="dark");#we=T(()=>e.props.colorMode==="system"?this._prefersDark.current?"dark":"light":e.props.colorMode??"light");get colorMode(){return c(this.#we)}set colorMode(r){U(this.#we,r)}constructor(){}resetStoreValues(){this.dragging=!1,this.selectionRect=null,this.selectionRectMode=null,this.selectionKeyPressed=!1,this.multiselectionKeyPressed=!1,this.deleteKeyPressed=!1,this.panActivationKeyPressed=!1,this.zoomActivationKeyPressed=!1,this._connection=na,this.clickConnectStartHandle=null,this.viewport=e.props.initialViewport??{x:0,y:0,zoom:1},this.ariaLiveMessage=""}}return new t}function ln(){const e=Xn(Ki);if(!e)throw new Error("To call useStore outside of <SvelteFlow /> you need to wrap your component in a <SvelteFlowProvider />");return e.getStore()}const Ki=Symbol();function Fc(e){const t=hm(e);function n(A){t.nodeTypes={...Yc,...A}}function r(A){t.edgeTypes={...Xc,...A}}function o(A){t.edges=w0(A,t.edges)}const i=(A,_=!1)=>{t.nodes=t.nodes.map(k=>{const C=A.get(k.id);return C?{...k,position:C.position,dragging:_}:k})};function s(A){const{changes:_,updatedInternals:k}=O0(A,t.nodeLookup,t.parentLookup,t.domNode,t.nodeOrigin);if(!k)return;E0(t.nodeLookup,t.parentLookup,{nodeOrigin:t.nodeOrigin,nodeExtent:t.nodeExtent}),t.fitViewQueued&&t.resolveFitView();const C=new Map;for(const N of _){const P=t.nodeLookup.get(N.id)?.internals.userNode;if(!P)continue;const H={...P};switch(N.type){case"dimensions":{const Z={...H.measured,...N.dimensions};N.setAttributes&&(H.width=N.dimensions?.width??H.width,H.height=N.dimensions?.height??H.height),H.measured=Z;break}case"position":H.position=N.position??H.position;break}C.set(N.id,H)}t.nodes=t.nodes.map(N=>C.get(N.id)??N)}function a(A){const _=t.fitViewResolver??Promise.withResolvers();return t.fitViewQueued=!0,t.fitViewOptions=A,t.fitViewResolver=_,t.nodes=[...t.nodes],_.promise}async function l(A,_,k){const C=typeof k?.zoom<"u"?k.zoom:t.maxZoom,N=t.panZoom;return N?(await N.setViewport({x:t.width/2-A*C,y:t.height/2-_*C,zoom:C},{duration:k?.duration,ease:k?.ease,interpolate:k?.interpolate}),Promise.resolve(!0)):Promise.resolve(!1)}function u(A,_){const k=t.panZoom;return k?k.scaleBy(A,_):Promise.resolve(!1)}function d(A){return u(1.2,A)}function p(A){return u(1/1.2,A)}function f(A){const _=t.panZoom;_&&(_.setScaleExtent([A,t.maxZoom]),t.minZoom=A)}function g(A){const _=t.panZoom;_&&(_.setScaleExtent([t.minZoom,A]),t.maxZoom=A)}function h(A){const _=t.panZoom;_&&(_.setTranslateExtent(A),t.translateExtent=A)}function v(A){t.panZoom?.setClickDistance(A)}function w(A,_=null){let k=!1;const C=A.map(N=>(!_||_.has(N.id))&&N.selected?(k=!0,{...N,selected:!1}):N);return[k,C]}function b(A){const _=A?.nodes?new Set(A.nodes.map(Z=>Z.id)):null,[k,C]=w(t.nodes,_);k&&(t.nodes=C);const N=A?.edges?new Set(A.edges.map(Z=>Z.id)):null,[P,H]=w(t.edges,N);P&&(t.edges=H)}function x(A){const _=t.multiselectionKeyPressed;t.nodes=t.nodes.map(k=>{const C=A.includes(k.id),N=_&&k.selected||C;if(k.selected!==N){const P=t.nodeLookup.get(k.id);return P&&(P.selected=N),k.selected=N,{...k}}return k}),_||b({nodes:[]})}function $(A){const _=t.multiselectionKeyPressed;t.edges=t.edges.map(k=>{const C=A.includes(k.id),N=_&&k.selected||C;return k.selected!==N?{...k,selected:N}:k}),_||b({edges:[]})}function S(A,_,k){const C=t.nodeLookup.get(A);if(!C){console.warn("012",To.error012(A));return}t.selectionRect=null,t.selectionRectMode=null,C.selected?(_||C.selected&&t.multiselectionKeyPressed)&&(b({nodes:[C],edges:[]}),requestAnimationFrame(()=>k?.blur())):x([A])}function E(A){const _=t.edgeLookup.get(A);if(!_){console.warn("012",To.error012(A));return}(_.selectable||t.elementsSelectable&&typeof _.selectable>"u")&&(t.selectionRect=null,t.selectionRectMode=null,_.selected?_.selected&&t.multiselectionKeyPressed&&b({nodes:[],edges:[_]}):$([A]))}function D(A,_){const{nodeExtent:k,snapGrid:C,nodeOrigin:N,nodeLookup:P,nodesDraggable:H,onerror:Z}=t,Y=new Map,M=C?.[0]??5,X=C?.[1]??5,te=A.x*M*_,oe=A.y*X*_;for(const j of P.values()){if(!(j.selected&&(j.draggable||H&&typeof j.draggable>"u")))continue;let G={x:j.internals.positionAbsolute.x+te,y:j.internals.positionAbsolute.y+oe};C&&(G=Ho(G,C));const{position:F,positionAbsolute:se}=nc({nodeId:j.id,nextPosition:G,nodeLookup:P,nodeExtent:k,nodeOrigin:N,onError:Z});j.position=F,j.internals.positionAbsolute=se,Y.set(j.id,j)}i(Y)}function O(A){return L0({delta:A,panZoom:t.panZoom,transform:[t.viewport.x,t.viewport.y,t.viewport.zoom],translateExtent:t.translateExtent,width:t.width,height:t.height})}const q=A=>{t._connection={...A}};function K(){t._connection=na}function J(){t.resetStoreValues(),b()}return Object.assign(t,{setNodeTypes:n,setEdgeTypes:r,addEdge:o,updateNodePositions:i,updateNodeInternals:s,zoomIn:d,zoomOut:p,fitView:a,setCenter:l,setMinZoom:f,setMaxZoom:g,setTranslateExtent:h,setPaneClickDistance:v,unselectNodesAndEdges:b,addSelectedNodes:x,addSelectedEdges:$,handleNodeSelection:S,handleEdgeSelection:E,moveSelectedNodes:D,panBy:O,updateConnection:q,cancelConnection:K,reset:J})}function vm(e,t){const{minZoom:n,maxZoom:r,initialViewport:o,onPanZoomStart:i,onPanZoom:s,onPanZoomEnd:a,translateExtent:l,paneClickDistance:u,setPanZoomInstance:d,onDraggingChange:p,onTransformChange:f}=t,g=Q0({domNode:e,minZoom:n,maxZoom:r,translateExtent:l,viewport:o,paneClickDistance:u,onPanZoom:s,onPanZoomStart:i,onPanZoomEnd:a,onDraggingChange:p}),h=g.getViewport();return(o.x!==h.x||o.y!==h.y||o.zoom!==h.zoom)&&f([h.x,h.y,h.zoom]),d(g),g.update(t),{update(v){g.update(v)}}}var mm=Q('<div class="svelte-flow__zoom svelte-flow__container"><!></div>');function Wc(e,t){de(t,!0);let n=y(t,"store",15),r=y(t,"panOnScrollMode",23,()=>$n.Free),o=y(t,"preventScrolling",7,!0),i=y(t,"zoomOnScroll",7,!0),s=y(t,"zoomOnDoubleClick",7,!0),a=y(t,"zoomOnPinch",7,!0),l=y(t,"panOnDrag",7,!0),u=y(t,"panOnScroll",7,!1),d=y(t,"paneClickDistance",7,1),p=y(t,"onmovestart",7),f=y(t,"onmove",7),g=y(t,"onmoveend",7),h=y(t,"oninit",7),v=y(t,"children",7),w=T(()=>n().panActivationKeyPressed||l()),b=T(()=>n().panActivationKeyPressed||u());const{viewport:x}=n();let $=!1;et(()=>{!$&&n().viewportInitialized&&(h()?.(),$=!0)});var S={get store(){return n()},set store(O){n(O),m()},get panOnScrollMode(){return r()},set panOnScrollMode(O=$n.Free){r(O),m()},get preventScrolling(){return o()},set preventScrolling(O=!0){o(O),m()},get zoomOnScroll(){return i()},set zoomOnScroll(O=!0){i(O),m()},get zoomOnDoubleClick(){return s()},set zoomOnDoubleClick(O=!0){s(O),m()},get zoomOnPinch(){return a()},set zoomOnPinch(O=!0){a(O),m()},get panOnDrag(){return l()},set panOnDrag(O=!0){l(O),m()},get panOnScroll(){return u()},set panOnScroll(O=!1){u(O),m()},get paneClickDistance(){return d()},set paneClickDistance(O=1){d(O),m()},get onmovestart(){return p()},set onmovestart(O){p(O),m()},get onmove(){return f()},set onmove(O){f(O),m()},get onmoveend(){return g()},set onmoveend(O){g(O),m()},get oninit(){return h()},set oninit(O){h(O),m()},get children(){return v()},set children(O){v(O),m()}},E=mm(),D=I(E);return tt(D,v),R(E),$t(E,(O,q)=>vm?.(O,q),()=>({viewport:n().viewport,minZoom:n().minZoom,maxZoom:n().maxZoom,initialViewport:x,onDraggingChange:O=>{n(n().dragging=O,!0)},setPanZoomInstance:O=>{n(n().panZoom=O,!0)},onPanZoomStart:p(),onPanZoom:f(),onPanZoomEnd:g(),zoomOnScroll:i(),zoomOnDoubleClick:s(),zoomOnPinch:a(),panOnScroll:c(b),panOnDrag:c(w),panOnScrollSpeed:.5,panOnScrollMode:r()||$n.Free,zoomActivationKeyPressed:n().zoomActivationKeyPressed,preventScrolling:typeof o()=="boolean"?o():!0,noPanClassName:n().noPanClass,noWheelClassName:n().noWheelClass,userSelectionActive:!!n().selectionRect,translateExtent:n().translateExtent,lib:"svelte",paneClickDistance:d(),onTransformChange:O=>{n(n().viewport={x:O[0],y:O[1],zoom:O[2]},!0)}})),L(e,E),fe(S)}ue(Wc,{store:{},panOnScrollMode:{},preventScrolling:{},zoomOnScroll:{},zoomOnDoubleClick:{},zoomOnPinch:{},panOnDrag:{},panOnScroll:{},paneClickDistance:{},onmovestart:{},onmove:{},onmoveend:{},oninit:{},children:{}},[],[],!0);function Gc(e,t){return n=>{n.target===t&&e?.(n)}}function Uc(e){return t=>{const n=e.has(t.id);return!!t.selected!==n?{...t,selected:n}:t}}function Jc(e,t){if(e.size!==t.size)return!1;for(const n of e)if(!t.has(n))return!1;return!0}var ym=Q("<div><!></div>");function Qc(e,t){de(t,!0);let n=y(t,"store",15),r=y(t,"panOnDrag",7,!0),o=y(t,"selectionOnDrag",7),i=y(t,"onpaneclick",7),s=y(t,"onpanecontextmenu",7),a=y(t,"onselectionstart",7),l=y(t,"onselectionend",7),u=y(t,"children",7),d,p=null,f=new Set,g=new Set,h=T(()=>n().panActivationKeyPressed||r()),v=T(()=>n().selectionKeyPressed||n().selectionRect||o()&&c(h)!==!0),w=T(()=>n().elementsSelectable&&(c(v)||n().selectionRectMode==="user")),b=!1;function x(k){if(b||n().connection.inProgress){b=!1;return}i()?.({event:k}),n().unselectNodesAndEdges(),n(n().selectionRectMode=null,!0)}function $(k){if(p=d?.getBoundingClientRect(),!n().elementsSelectable||!c(v)||k.button!==0||k.target!==d||!p)return;k.target?.setPointerCapture?.(k.pointerId);const{x:C,y:N}=kn(k,p);n().unselectNodesAndEdges(),n(n().selectionRect={width:0,height:0,startX:C,startY:N,x:C,y:N},!0),a()?.(k)}function S(k){if(!c(v)||!p||!n().selectionRect)return;b=!0;const C=kn(k,p),{startX:N=0,startY:P=0}=n().selectionRect,H={...n().selectionRect,x:C.x<N?C.x:N,y:C.y<P?C.y:P,width:Math.abs(C.x-N),height:Math.abs(C.y-P)},Z=f,Y=g;f=new Set(oa(n().nodeLookup,H,[n().viewport.x,n().viewport.y,n().viewport.zoom],n().selectionMode===Vi.Partial,!0).map(X=>X.id));const M=n().defaultEdgeOptions.selectable??!0;g=new Set;for(const X of f){const te=n().connectionLookup.get(X);if(te)for(const{edgeId:oe}of te.values()){const j=n().edgeLookup.get(oe);j&&(j.selectable??M)&&g.add(oe)}}Jc(Z,f)||n(n().nodes=n().nodes.map(Uc(f)),!0),Jc(Y,g)||n(n().edges=n().edges.map(Uc(g)),!0),n(n().selectionRectMode="user",!0),n(n().selectionRect=H,!0)}function E(k){k.button===0&&(k.target?.releasePointerCapture?.(k.pointerId),!c(v)&&n().selectionRectMode==="user"&&k.target===d&&x?.(k),n(n().selectionRect=null,!0),f.size>0&&n(n().selectionRectMode="nodes",!0),n().selectionKeyPressed&&(b=!1),l()?.(k))}const D=k=>{if(Array.isArray(c(h))&&c(h).includes(2)){k.preventDefault();return}s()?.({event:k})};var O={get store(){return n()},set store(k){n(k),m()},get panOnDrag(){return r()},set panOnDrag(k=!0){r(k),m()},get selectionOnDrag(){return o()},set selectionOnDrag(k){o(k),m()},get onpaneclick(){return i()},set onpaneclick(k){i(k),m()},get onpanecontextmenu(){return s()},set onpanecontextmenu(k){s(k),m()},get onselectionstart(){return a()},set onselectionstart(k){a(k),m()},get onselectionend(){return l()},set onselectionend(k){l(k),m()},get children(){return u()},set children(k){u(k),m()}},q=ym();let K;var J=T(()=>c(w)?void 0:Gc(x,d));q.__click=function(...k){c(J)?.apply(this,k)},q.__pointerdown=function(...k){(c(w)?$:void 0)?.apply(this,k)},q.__pointermove=function(...k){(c(w)?S:void 0)?.apply(this,k)},q.__pointerup=function(...k){(c(w)?E:void 0)?.apply(this,k)};var A=T(()=>Gc(D,d));q.__contextmenu=function(...k){c(A)?.apply(this,k)};var _=I(q);return tt(_,u),R(q),qt(q,k=>d=k,()=>d),Se(k=>K=Mt(q,1,"svelte-flow__pane svelte-flow__container",null,K,k),[()=>({draggable:r()===!0||Array.isArray(r())&&r().includes(0),dragging:n().dragging,selection:c(v)})]),L(e,q),fe(O)}Mn(["click","pointerdown","pointermove","pointerup","contextmenu"]),ue(Qc,{store:{},panOnDrag:{},selectionOnDrag:{},onpaneclick:{},onpanecontextmenu:{},onselectionstart:{},onselectionend:{},children:{}},[],[],!0);var wm=Q('<div class="svelte-flow__viewport xyflow__viewport svelte-flow__container"><!></div>');function ed(e,t){de(t,!0);let n=y(t,"store",15),r=y(t,"children",7);var o={get store(){return n()},set store(l){n(l),m()},get children(){return r()},set children(l){r(l),m()}},i=wm();let s;var a=I(i);return tt(a,r),R(i),Se(l=>s=mt(i,"",s,l),[()=>({transform:`translate(${n().viewport.x??""}px, ${n().viewport.y??""}px) scale(${n().viewport.zoom??""})`})]),L(e,i),fe(o)}ue(ed,{store:{},children:{}},[],[],!0);function td(e,t){const{store:n,onDrag:r,onDragStart:o,onDragStop:i,onNodeMouseDown:s}=t,a=A0({onDrag:r,onDragStart:o,onDragStop:i,onNodeMouseDown:s,getStoreItems:()=>{const{snapGrid:u,viewport:d}=n;return{nodes:n.nodes,nodeLookup:n.nodeLookup,edges:n.edges,nodeExtent:n.nodeExtent,snapGrid:u||[0,0],snapToGrid:!!u,nodeOrigin:n.nodeOrigin,multiSelectionActive:n.multiselectionKeyPressed,domNode:n.domNode,transform:[d.x,d.y,d.zoom],autoPanOnNodeDrag:n.autoPanOnNodeDrag,nodesDraggable:n.nodesDraggable,selectNodesOnDrag:n.selectNodesOnDrag,nodeDragThreshold:n.nodeDragThreshold,unselectNodesAndEdges:n.unselectNodesAndEdges,updateNodePositions:n.updateNodePositions,onSelectionDrag:n.onselectiondrag,onSelectionDragStart:n.onselectiondragstart,onSelectionDragStop:n.onselectiondragstop,panBy:n.panBy}}});function l(u,d){if(d.disabled){a.destroy();return}a.update({domNode:u,noDragClassName:d.noDragClass,handleSelector:d.handleSelector,nodeId:d.nodeId,isSelectable:d.isSelectable,nodeClickDistance:d.nodeClickDistance})}return l(e,t),{update(u){l(e,u)},destroy(){a.destroy()}}}var bm=Q('<div aria-live="assertive" aria-atomic="true" class="a11y-live-msg svelte-62ze0y"> </div>'),xm=Q('<div class="a11y-hidden svelte-62ze0y"> </div> <div class="a11y-hidden svelte-62ze0y"> </div> <!>',1);const Cm={hash:"svelte-62ze0y",code:".a11y-hidden.svelte-62ze0y {display:none;}.a11y-live-msg.svelte-62ze0y {position:absolute;width:1px;height:1px;margin:-1px;border:0;padding:0;overflow:hidden;clip:rect(0px, 0px, 0px, 0px);clip-path:inset(100%);}"};function nd(e,t){de(t,!0),qe(e,Cm);let n=y(t,"store",7);var r={get store(){return n()},set store(p){n(p),m()}},o=xm(),i=ae(o),s=I(i,!0);R(i);var a=V(i,2),l=I(a,!0);R(a);var u=V(a,2);{var d=p=>{var f=bm(),g=I(f,!0);R(f),Se(()=>{Ce(f,"id",`${$m}-${n().flowId}`),Xe(g,n().ariaLiveMessage)}),L(p,f)};ce(u,p=>{n().disableKeyboardA11y||p(d)})}return Se(()=>{Ce(i,"id",`${rd}-${n().flowId}`),Xe(s,n().disableKeyboardA11y?n().ariaLabelConfig["node.a11yDescription.default"]:n().ariaLabelConfig["node.a11yDescription.keyboardDisabled"]),Ce(a,"id",`${od}-${n().flowId}`),Xe(l,n().ariaLabelConfig["edge.a11yDescription.default"])}),L(e,o),fe(r)}ue(nd,{store:{}},[],[],!0);const rd="svelte-flow__node-desc",od="svelte-flow__edge-desc",$m="svelte-flow__aria-live";var km=Q("<div><!></div>");function id(e,t){de(t,!0);let n=y(t,"store",15),r=y(t,"node",7),o=y(t,"resizeObserver",7),i=y(t,"nodeClickDistance",7),s=y(t,"onnodeclick",7),a=y(t,"onnodedrag",7),l=y(t,"onnodedragstart",7),u=y(t,"onnodedragstop",7),d=y(t,"onnodepointerenter",7),p=y(t,"onnodepointerleave",7),f=y(t,"onnodepointermove",7),g=y(t,"onnodecontextmenu",7),h=T(()=>_t(r().data,()=>({}),!0)),v=T(()=>_t(r().selected,!1)),w=T(()=>r().draggable),b=T(()=>r().selectable),x=T(()=>_t(r().deletable,!0)),$=T(()=>r().connectable),S=T(()=>r().focusable),E=T(()=>_t(r().hidden,!1)),D=T(()=>_t(r().dragging,!1)),O=T(()=>_t(r().style,"")),q=T(()=>r().class),K=T(()=>_t(r().type,"default")),J=T(()=>r().parentId),A=T(()=>r().sourcePosition),_=T(()=>r().targetPosition),k=T(()=>_t(r().measured,()=>({width:0,height:0}),!0).width),C=T(()=>_t(r().measured,()=>({width:0,height:0}),!0).height),N=T(()=>r().initialWidth),P=T(()=>r().initialHeight),H=T(()=>r().width),Z=T(()=>r().height),Y=T(()=>r().dragHandle),M=T(()=>_t(r().internals.z,0)),X=T(()=>r().internals.positionAbsolute.x),te=T(()=>r().internals.positionAbsolute.y),oe=T(()=>r().internals.userNode),{id:j}=r(),G=T(()=>c(w)??n().nodesDraggable),F=T(()=>c(b)??n().elementsSelectable),se=T(()=>c($)??n().nodesConnectable),W=T(()=>lc(r())),ye=T(()=>!!r().internals.handleBounds),xe=T(()=>c(W)&&c(ye)),ie=T(()=>c(S)??n().nodesFocusable);function ee(ve){return n().parentLookup.has(ve)}let re=T(()=>ee(j)),ge=De(null),he=null,le=c(K),Te=c(A),ke=c(_),B=T(()=>n().nodeTypes[c(K)]??va),ct=T(()=>n().ariaLabelConfig);Lr("svelteflow__node_connectable",{get value(){return c(se)}}),Lr("svelteflow__node_id",j);let Ae=T(()=>{const ve=c(k)===void 0?c(H)??c(N):c(H),Ye=c(C)===void 0?c(Z)??c(P):c(Z);if(!(ve===void 0&&Ye===void 0&&c(O)===void 0))return`${c(O)};${ve?`width:${_n(ve)};`:""}${Ye?`height:${_n(Ye)};`:""}`});et(()=>{(c(K)!==le||c(A)!==Te||c(_)!==ke)&&c(ge)!==null&&requestAnimationFrame(()=>{c(ge)!==null&&n().updateNodeInternals(new Map([[j,{id:j,nodeElement:c(ge),force:!0}]]))}),le=c(K),Te=c(A),ke=c(_)}),et(()=>{o()&&(!c(xe)||c(ge)!==he)&&(he&&o().unobserve(he),c(ge)&&o().observe(c(ge)),he=c(ge))}),ui(()=>{he&&o()?.unobserve(he)});function Ze(ve){c(F)&&(!n().selectNodesOnDrag||!c(G)||n().nodeDragThreshold>0)&&n().handleNodeSelection(j),s()?.({node:c(oe),event:ve})}function Re(ve){if(!(dc(ve)||n().disableKeyboardA11y))if(Ju.includes(ve.key)&&c(F)){const Ye=ve.key==="Escape";n().handleNodeSelection(j,Ye,c(ge))}else c(G)&&r().selected&&Object.prototype.hasOwnProperty.call(Bi,ve.key)&&(ve.preventDefault(),n(n().ariaLiveMessage=c(ct)["node.a11yDescription.ariaLiveMessage"]({direction:ve.key.replace("Arrow","").toLowerCase(),x:~~r().internals.positionAbsolute.x,y:~~r().internals.positionAbsolute.y}),!0),n().moveSelectedNodes(Bi[ve.key],ve.shiftKey?4:1))}const dt=()=>{if(n().disableKeyboardA11y||!n().autoPanOnNodeFocus||!c(ge)?.matches(":focus-visible"))return;const{width:ve,height:Ye,viewport:ft}=n();oa(new Map([[j,r()]]),{x:0,y:0,width:ve,height:Ye},[ft.x,ft.y,ft.zoom],!0).length>0||n().setCenter(r().position.x+(r().measured.width??0)/2,r().position.y+(r().measured.height??0)/2,{zoom:ft.zoom})};var it={get store(){return n()},set store(ve){n(ve),m()},get node(){return r()},set node(ve){r(ve),m()},get resizeObserver(){return o()},set resizeObserver(ve){o(ve),m()},get nodeClickDistance(){return i()},set nodeClickDistance(ve){i(ve),m()},get onnodeclick(){return s()},set onnodeclick(ve){s(ve),m()},get onnodedrag(){return a()},set onnodedrag(ve){a(ve),m()},get onnodedragstart(){return l()},set onnodedragstart(ve){l(ve),m()},get onnodedragstop(){return u()},set onnodedragstop(ve){u(ve),m()},get onnodepointerenter(){return d()},set onnodepointerenter(ve){d(ve),m()},get onnodepointerleave(){return p()},set onnodepointerleave(ve){p(ve),m()},get onnodepointermove(){return f()},set onnodepointermove(ve){f(ve),m()},get onnodecontextmenu(){return g()},set onnodecontextmenu(ve){g(ve),m()}},Pt=Ne(),Be=ae(Pt);{var Qe=ve=>{var Ye=km();ut(Ye,(st,Nt)=>({"data-id":j,class:["svelte-flow__node",`svelte-flow__node-${c(K)}`,c(q)],style:c(Ae),onclick:Ze,onpointerenter:d()?Je=>d()({node:c(oe),event:Je}):void 0,onpointerleave:p()?Je=>p()({node:c(oe),event:Je}):void 0,onpointermove:f()?Je=>f()({node:c(oe),event:Je}):void 0,oncontextmenu:g()?Je=>g()({node:c(oe),event:Je}):void 0,onkeydown:c(ie)?Re:void 0,onfocus:c(ie)?dt:void 0,tabIndex:c(ie)?0:void 0,role:r().ariaRole??(c(ie)?"group":void 0),"aria-roledescription":"node","aria-describedby":n().disableKeyboardA11y?void 0:`${rd}-${n().flowId}`,...r().domAttributes,[Un]:st,[yn]:Nt}),[()=>({dragging:c(D),selected:c(v),draggable:c(G),connectable:c(se),selectable:c(F),nopan:c(G),parent:c(re)}),()=>({"z-index":c(M),transform:`translate(${c(X)??""}px, ${c(te)??""}px)`,visibility:c(W)?"visible":"hidden"})]);var ft=I(Ye);Ds(ft,()=>c(B),(st,Nt)=>{Nt(st,{get data(){return c(h)},get id(){return j},get selected(){return c(v)},get selectable(){return c(F)},get deletable(){return c(x)},get sourcePosition(){return c(A)},get targetPosition(){return c(_)},get zIndex(){return c(M)},get dragging(){return c(D)},get draggable(){return c(G)},get dragHandle(){return c(Y)},get parentId(){return c(J)},get type(){return c(K)},get isConnectable(){return c(se)},get positionAbsoluteX(){return c(X)},get positionAbsoluteY(){return c(te)},get width(){return c(H)},get height(){return c(Z)}})}),R(Ye),$t(Ye,(st,Nt)=>td?.(st,Nt),()=>({nodeId:j,isSelectable:c(F),disabled:!c(G),handleSelector:c(Y),noDragClass:n().noDragClass,nodeClickDistance:i(),onNodeMouseDown:n().handleNodeSelection,onDrag:(st,Nt,Je,kt)=>{a()?.({event:st,targetNode:Je,nodes:kt})},onDragStart:(st,Nt,Je,kt)=>{l()?.({event:st,targetNode:Je,nodes:kt})},onDragStop:(st,Nt,Je,kt)=>{u()?.({event:st,targetNode:Je,nodes:kt})},store:n()})),qt(Ye,st=>U(ge,st),()=>c(ge)),L(ve,Ye)};ce(Be,ve=>{c(E)||ve(Qe)})}return L(e,Pt),fe(it)}ue(id,{store:{},node:{},resizeObserver:{},nodeClickDistance:{},onnodeclick:{},onnodedrag:{},onnodedragstart:{},onnodedragstop:{},onnodepointerenter:{},onnodepointerleave:{},onnodepointermove:{},onnodecontextmenu:{}},[],[],!0);var _m=Q('<div class="svelte-flow__nodes"></div>');function sd(e,t){de(t,!0);let n=y(t,"store",15),r=y(t,"nodeClickDistance",7),o=y(t,"onnodeclick",7),i=y(t,"onnodecontextmenu",7),s=y(t,"onnodepointerenter",7),a=y(t,"onnodepointermove",7),l=y(t,"onnodepointerleave",7),u=y(t,"onnodedrag",7),d=y(t,"onnodedragstart",7),p=y(t,"onnodedragstop",7);const f=typeof ResizeObserver>"u"?null:new ResizeObserver(v=>{const w=new Map;v.forEach(b=>{const x=b.target.getAttribute("data-id");w.set(x,{id:x,nodeElement:b.target,force:!0})}),n().updateNodeInternals(w)});ui(()=>{f?.disconnect()});var g={get store(){return n()},set store(v){n(v),m()},get nodeClickDistance(){return r()},set nodeClickDistance(v){r(v),m()},get onnodeclick(){return o()},set onnodeclick(v){o(v),m()},get onnodecontextmenu(){return i()},set onnodecontextmenu(v){i(v),m()},get onnodepointerenter(){return s()},set onnodepointerenter(v){s(v),m()},get onnodepointermove(){return a()},set onnodepointermove(v){a(v),m()},get onnodepointerleave(){return l()},set onnodepointerleave(v){l(v),m()},get onnodedrag(){return u()},set onnodedrag(v){u(v),m()},get onnodedragstart(){return d()},set onnodedragstart(v){d(v),m()},get onnodedragstop(){return p()},set onnodedragstop(v){p(v),m()}},h=_m();return Ct(h,21,()=>n().visible.nodes.values(),v=>v.id,(v,w)=>{id(v,{get node(){return c(w)},get resizeObserver(){return f},get nodeClickDistance(){return r()},get onnodeclick(){return o()},get onnodepointerenter(){return s()},get onnodepointermove(){return a()},get onnodepointerleave(){return l()},get onnodedrag(){return u()},get onnodedragstart(){return d()},get onnodedragstop(){return p()},get onnodecontextmenu(){return i()},get store(){return n()},set store(b){n(b)}})}),R(h),L(e,h),fe(g)}ue(sd,{store:{},nodeClickDistance:{},onnodeclick:{},onnodecontextmenu:{},onnodepointerenter:{},onnodepointermove:{},onnodepointerleave:{},onnodedrag:{},onnodedragstart:{},onnodedragstop:{}},[],[],!0);var Sm=me('<svg class="svelte-flow__edge-wrapper"><g><!></g></svg>');function ad(e,t){de(t,!0);const n=y(t,"edge",7),r=y(t,"store",15),o=y(t,"onedgeclick",7),i=y(t,"onedgecontextmenu",7),s=y(t,"onedgepointerenter",7),a=y(t,"onedgepointerleave",7);let l=T(()=>n().source),u=T(()=>n().target),d=T(()=>n().sourceX),p=T(()=>n().sourceY),f=T(()=>n().targetX),g=T(()=>n().targetY),h=T(()=>n().sourcePosition),v=T(()=>n().targetPosition),w=T(()=>_t(n().animated,!1)),b=T(()=>_t(n().selected,!1)),x=T(()=>n().label),$=T(()=>n().labelStyle),S=T(()=>_t(n().data,()=>({}),!0)),E=T(()=>n().style),D=T(()=>n().interactionWidth),O=T(()=>_t(n().type,"default")),q=T(()=>n().sourceHandle),K=T(()=>n().targetHandle),J=T(()=>n().markerStart),A=T(()=>n().markerEnd),_=T(()=>n().selectable),k=T(()=>n().focusable),C=T(()=>_t(n().deletable,!0)),N=T(()=>n().hidden),P=T(()=>n().zIndex),H=T(()=>n().class),Z=T(()=>n().ariaLabel),Y=null;const{id:M}=n();Lr("svelteflow__edge_id",M);let X=T(()=>c(_)??r().elementsSelectable),te=T(()=>c(k)??r().edgesFocusable),oe=T(()=>r().edgeTypes[c(O)]??ya),j=T(()=>c(J)?`url('#${ua(c(J),r().flowId)}')`:void 0),G=T(()=>c(A)?`url('#${ua(c(A),r().flowId)}')`:void 0);function F(re){const ge=r().edgeLookup.get(M);ge&&(c(X)&&r().handleEdgeSelection(M),o()?.({event:re,edge:ge}))}function se(re,ge){const he=r().edgeLookup.get(M);he&&ge({event:re,edge:he})}function W(re){if(!r().disableKeyboardA11y&&Ju.includes(re.key)&&c(X)){const{unselectNodesAndEdges:ge,addSelectedEdges:he}=r();re.key==="Escape"?(Y?.blur(),ge({edges:[n()]})):he([M])}}var ye={get edge(){return n()},set edge(re){n(re),m()},get store(){return r()},set store(re){r(re),m()},get onedgeclick(){return o()},set onedgeclick(re){o(re),m()},get onedgecontextmenu(){return i()},set onedgecontextmenu(re){i(re),m()},get onedgepointerenter(){return s()},set onedgepointerenter(re){s(re),m()},get onedgepointerleave(){return a()},set onedgepointerleave(re){a(re),m()}},xe=Ne(),ie=ae(xe);{var ee=re=>{var ge=Sm();let he;var le=I(ge);ut(le,ke=>({class:["svelte-flow__edge",c(H)],"data-id":M,onclick:F,oncontextmenu:i()?B=>{se(B,i())}:void 0,onpointerenter:s()?B=>{se(B,s())}:void 0,onpointerleave:a()?B=>{se(B,a())}:void 0,"aria-label":c(Z)===null?void 0:c(Z)?c(Z):`Edge from ${c(l)} to ${c(u)}`,"aria-describedby":c(te)?`${od}-${r().flowId}`:void 0,role:n().ariaRole??(c(te)?"group":"img"),"aria-roledescription":"edge",onkeydown:c(te)?W:void 0,tabindex:c(te)?0:void 0,...n().domAttributes,[Un]:ke}),[()=>({animated:c(w),selected:c(b),selectable:c(X)})]);var Te=I(le);Ds(Te,()=>c(oe),(ke,B)=>{B(ke,{get id(){return M},get source(){return c(l)},get target(){return c(u)},get sourceX(){return c(d)},get sourceY(){return c(p)},get targetX(){return c(f)},get targetY(){return c(g)},get sourcePosition(){return c(h)},get targetPosition(){return c(v)},get animated(){return c(w)},get selected(){return c(b)},get label(){return c(x)},get labelStyle(){return c($)},get data(){return c(S)},get style(){return c(E)},get interactionWidth(){return c(D)},get selectable(){return c(X)},get deletable(){return c(C)},get type(){return c(O)},get sourceHandleId(){return c(q)},get targetHandleId(){return c(K)},get markerStart(){return c(j)},get markerEnd(){return c(G)}})}),R(le),qt(le,ke=>Y=ke,()=>Y),R(ge),Se(ke=>he=mt(ge,"",he,ke),[()=>({"z-index":c(P)})]),L(re,ge)};ce(ie,re=>{c(N)||re(ee)})}return L(e,xe),fe(ye)}ue(ad,{edge:{},store:{},onedgeclick:{},onedgecontextmenu:{},onedgepointerenter:{},onedgepointerleave:{}},[],[],!0),pp();var Em=me("<defs></defs>");function ld(e,t){de(t,!1);const n=ln();tu();var r=Em();Ct(r,5,()=>n.markers,o=>o.id,(o,i)=>{ud(o,Fe(()=>c(i)))}),R(r),L(e,r),fe()}ue(ld,{},[],[],!0);var Pm=me('<polyline class="arrow" fill="none" stroke-linecap="round" stroke-linejoin="round" points="-5,-4 0,0 -5,4"></polyline>'),Nm=me('<polyline class="arrowclosed" stroke-linecap="round" stroke-linejoin="round" points="-5,-4 0,0 -5,4 -5,-4"></polyline>'),Tm=me('<marker class="svelte-flow__arrowhead" viewBox="-10 -10 20 20" refX="0" refY="0"><!></marker>');function ud(e,t){de(t,!0);let n=y(t,"id",7),r=y(t,"type",7),o=y(t,"width",7,12.5),i=y(t,"height",7,12.5),s=y(t,"markerUnits",7,"strokeWidth"),a=y(t,"orient",7,"auto-start-reverse"),l=y(t,"color",7,"none"),u=y(t,"strokeWidth",7);var d={get id(){return n()},set id(v){n(v),m()},get type(){return r()},set type(v){r(v),m()},get width(){return o()},set width(v=12.5){o(v),m()},get height(){return i()},set height(v=12.5){i(v),m()},get markerUnits(){return s()},set markerUnits(v="strokeWidth"){s(v),m()},get orient(){return a()},set orient(v="auto-start-reverse"){a(v),m()},get color(){return l()},set color(v="none"){l(v),m()},get strokeWidth(){return u()},set strokeWidth(v){u(v),m()}},p=Tm(),f=I(p);{var g=v=>{var w=Pm();let b;Se(x=>{Ce(w,"stroke-width",u()),b=mt(w,"",b,x)},[()=>({stroke:l()})]),L(v,w)},h=v=>{var w=Ne(),b=ae(w);{var x=$=>{var S=Nm();let E;Se(D=>{Ce(S,"stroke-width",u()),E=mt(S,"",E,D)},[()=>({stroke:l(),fill:l()})]),L($,S)};ce(b,$=>{r()===Do.ArrowClosed&&$(x)},!0)}L(v,w)};ce(f,v=>{r()===Do.Arrow?v(g):v(h,!1)})}return R(p),Se(()=>{Ce(p,"id",n()),Ce(p,"markerWidth",`${o()}`),Ce(p,"markerHeight",`${i()}`),Ce(p,"markerUnits",s()),Ce(p,"orient",a())}),L(e,p),fe(d)}ue(ud,{id:{},type:{},width:{},height:{},markerUnits:{},orient:{},color:{},strokeWidth:{}},[],[],!0);var Dm=Q('<div class="svelte-flow__edges"><svg class="svelte-flow__marker"><!></svg> <!></div>');function cd(e,t){de(t,!0);let n=y(t,"store",15),r=y(t,"onedgeclick",7),o=y(t,"onedgecontextmenu",7),i=y(t,"onedgepointerenter",7),s=y(t,"onedgepointerleave",7);var a={get store(){return n()},set store(f){n(f),m()},get onedgeclick(){return r()},set onedgeclick(f){r(f),m()},get onedgecontextmenu(){return o()},set onedgecontextmenu(f){o(f),m()},get onedgepointerenter(){return i()},set onedgepointerenter(f){i(f),m()},get onedgepointerleave(){return s()},set onedgepointerleave(f){s(f),m()}},l=Dm(),u=I(l),d=I(u);ld(d,{}),R(u);var p=V(u,2);return Ct(p,17,()=>n().visible.edges.values(),f=>f.id,(f,g)=>{ad(f,{get edge(){return c(g)},get onedgeclick(){return r()},get onedgecontextmenu(){return o()},get onedgepointerenter(){return i()},get onedgepointerleave(){return s()},get store(){return n()},set store(h){n(h)}})}),R(l),L(e,l),fe(a)}ue(cd,{store:{},onedgeclick:{},onedgecontextmenu:{},onedgepointerenter:{},onedgepointerleave:{}},[],[],!0);var Om=Q('<div class="svelte-flow__selection svelte-1iugwpu"></div>');const Lm={hash:"svelte-1iugwpu",code:".svelte-flow__selection.svelte-1iugwpu {position:absolute;top:0;left:0;}"};function wa(e,t){de(t,!0),qe(e,Lm);let n=y(t,"x",7,0),r=y(t,"y",7,0),o=y(t,"width",7,0),i=y(t,"height",7,0),s=y(t,"isVisible",7,!0);var a={get x(){return n()},set x(p=0){n(p),m()},get y(){return r()},set y(p=0){r(p),m()},get width(){return o()},set width(p=0){o(p),m()},get height(){return i()},set height(p=0){i(p),m()},get isVisible(){return s()},set isVisible(p=!0){s(p),m()}},l=Ne(),u=ae(l);{var d=p=>{var f=Om();let g;Se(h=>g=mt(f,"",g,h),[()=>({width:typeof o()=="string"?o():_n(o()),height:typeof i()=="string"?i():_n(i()),transform:`translate(${n()}px, ${r()}px)`})]),L(p,f)};ce(u,p=>{s()&&p(d)})}return L(e,l),fe(a)}ue(wa,{x:{},y:{},width:{},height:{},isVisible:{}},[],[],!0);function Mm(e,t,n){const r=t().nodes.filter(o=>o.selected);n()?.({nodes:r,event:e})}function Hm(e,t,n){const r=t().nodes.filter(o=>o.selected);n()?.({nodes:r,event:e})}var Vm=Q("<div><!></div>");const zm={hash:"svelte-16qgzgd",code:".svelte-flow__selection-wrapper.svelte-16qgzgd {position:absolute;top:0;left:0;z-index:2000;pointer-events:all;}"};function dd(e,t){de(t,!0),qe(e,zm);let n=y(t,"store",15),r=y(t,"onnodedrag",7),o=y(t,"onnodedragstart",7),i=y(t,"onnodedragstop",7),s=y(t,"onselectionclick",7),a=y(t,"onselectioncontextmenu",7),l=De(void 0);et(()=>{n().disableKeyboardA11y||c(l)?.focus({preventScroll:!0})});let u=T(()=>n().selectionRectMode==="nodes"?(n().nodes,Lo(n().nodeLookup,{filter:v=>!!v.selected})):null);function d(v){Object.prototype.hasOwnProperty.call(Bi,v.key)&&(v.preventDefault(),n().moveSelectedNodes(Bi[v.key],v.shiftKey?4:1))}var p={get store(){return n()},set store(v){n(v),m()},get onnodedrag(){return r()},set onnodedrag(v){r(v),m()},get onnodedragstart(){return o()},set onnodedragstart(v){o(v),m()},get onnodedragstop(){return i()},set onnodedragstop(v){i(v),m()},get onselectionclick(){return s()},set onselectionclick(v){s(v),m()},get onselectioncontextmenu(){return a()},set onselectioncontextmenu(v){a(v),m()}},f=Ne(),g=ae(f);{var h=v=>{var w=Vm();w.__contextmenu=[Mm,n,a],w.__click=[Hm,n,s],w.__keydown=function(...$){(n().disableKeyboardA11y?void 0:d)?.apply(this,$)};let b;var x=I(w);wa(x,{width:"100%",height:"100%",x:0,y:0}),R(w),$t(w,($,S)=>td?.($,S),()=>({disabled:!1,store:n(),onDrag:($,S,E,D)=>{r()?.({event:$,targetNode:null,nodes:D})},onDragStart:($,S,E,D)=>{o()?.({event:$,targetNode:null,nodes:D})},onDragStop:($,S,E,D)=>{i()?.({event:$,targetNode:null,nodes:D})}})),qt(w,$=>U(l,$),()=>c(l)),Se($=>{Mt(w,1,Hn(["svelte-flow__selection-wrapper",n().noPanClass]),"svelte-16qgzgd"),Ce(w,"role",n().disableKeyboardA11y?void 0:"button"),Ce(w,"tabindex",n().disableKeyboardA11y?void 0:-1),b=mt(w,"",b,$)},[()=>({width:_n(c(u).width),height:_n(c(u).height),transform:`translate(${c(u).x??""}px, ${c(u).y??""}px)`})]),L(v,w)};ce(g,v=>{n().selectionRectMode==="nodes"&&c(u)&&In(c(u).x)&&In(c(u).y)&&v(h)})}return L(e,f),fe(p)}Mn(["contextmenu","click","keydown"]),ue(dd,{store:{},onnodedrag:{},onnodedragstart:{},onnodedragstop:{},onselectionclick:{},onselectioncontextmenu:{}},[],[],!0);function Am(e){switch(e){case"ctrl":return 8;case"shift":return 4;case"alt":return 2;case"meta":return 1}}function Sn(e,t){let{enabled:n=!0,trigger:r,type:o="keydown"}=t;function i(a){const l=Array.isArray(r)?r:[r],u=[a.metaKey,a.altKey,a.shiftKey,a.ctrlKey].reduce((d,p,f)=>p?d|1<<f:d,0);for(const d of l){const p={preventDefault:!1,enabled:!0,...d},{modifier:f,key:g,callback:h,preventDefault:v,enabled:w}=p;if(w){if(a.key!==g)continue;if(f===null||f===!1){if(u!==0)continue}else if(f!==void 0&&f?.[0]?.length>0){const x=Array.isArray(f)?f:[f];let $=!1;for(const S of x)if((Array.isArray(S)?S:[S]).reduce((E,D)=>E|Am(D),0)===u){$=!0;break}if(!$)continue}v&&a.preventDefault();const b={node:e,trigger:p,originalEvent:a};e.dispatchEvent(new CustomEvent("shortcut",{detail:b})),h?.(b)}}}let s;return n&&(s=Ss(e,o,i)),{update:a=>{const{enabled:l=!0,type:u="keydown"}=a;n&&(!l||o!==u)?s?.():!n&&l&&(s=Ss(e,u,i)),n=l,o=u,r=a.trigger},destroy:()=>{s?.()}}}function yt(){const e=T(ln),t=i=>{const s=Ic(i)?i:c(e).nodeLookup.get(i.id),a=s.parentId?d0(s.position,s.measured,s.parentId,c(e).nodeLookup,c(e).nodeOrigin):s.position,l={...s,position:a,width:s.measured?.width??s.width,height:s.measured?.height??s.height};return Yr(l)};function n(i,s,a={replace:!1}){c(e).nodes=vt(()=>c(e).nodes).map(l=>{if(l.id===i){const u=typeof s=="function"?s(l):s;return a?.replace&&Ic(u)?u:{...l,...u}}return l})}function r(i,s,a={replace:!1}){c(e).edges=vt(()=>c(e).edges).map(l=>{if(l.id===i){const u=typeof s=="function"?s(l):s;return a.replace&&om(u)?u:{...l,...u}}return l})}const o=i=>c(e).nodeLookup.get(i);return{zoomIn:c(e).zoomIn,zoomOut:c(e).zoomOut,getInternalNode:o,getNode:i=>o(i)?.internals.userNode,getNodes:i=>i===void 0?c(e).nodes:fd(c(e).nodeLookup,i),getEdge:i=>c(e).edgeLookup.get(i),getEdges:i=>i===void 0?c(e).edges:fd(c(e).edgeLookup,i),setZoom:(i,s)=>{const a=c(e).panZoom;return a?a.scaleTo(i,{duration:s?.duration}):Promise.resolve(!1)},getZoom:()=>c(e).viewport.zoom,setViewport:async(i,s)=>{const a=c(e).viewport;return c(e).panZoom?(await c(e).panZoom.setViewport({x:i.x??a.x,y:i.y??a.y,zoom:i.zoom??a.zoom},s),Promise.resolve(!0)):Promise.resolve(!1)},getViewport:()=>Ja(c(e).viewport),setCenter:async(i,s,a)=>c(e).setCenter(i,s,a),fitView:i=>c(e).fitView(i),fitBounds:async(i,s)=>{if(!c(e).panZoom)return Promise.resolve(!1);const a=sa(i,c(e).width,c(e).height,c(e).minZoom,c(e).maxZoom,s?.padding??.1);return await c(e).panZoom.setViewport(a,{duration:s?.duration,ease:s?.ease,interpolate:s?.interpolate}),Promise.resolve(!0)},getIntersectingNodes:(i,s=!0,a)=>{const l=ac(i),u=l?i:t(i);return u?(a||c(e).nodes).filter(d=>{const p=c(e).nodeLookup.get(d.id);if(!p||!l&&d.id===i.id)return!1;const f=Yr(p),g=Mo(f,u);return s&&g>0||g>=f.width*f.height||g>=u.width*u.height}):[]},isNodeIntersecting:(i,s,a=!0)=>{const l=ac(i)?i:t(i);if(!l)return!1;const u=Mo(l,s);return a&&u>0||u>=l.width*l.height},deleteElements:async({nodes:i=[],edges:s=[]})=>{const{nodes:a,edges:l}=await a0({nodesToRemove:i,edgesToRemove:s,nodes:c(e).nodes,edges:c(e).edges,onBeforeDelete:c(e).onbeforedelete});return a&&(c(e).nodes=vt(()=>c(e).nodes).filter(u=>!a.some(({id:d})=>d===u.id))),l&&(c(e).edges=vt(()=>c(e).edges).filter(u=>!l.some(({id:d})=>d===u.id))),(a.length>0||l.length>0)&&c(e).ondelete?.({nodes:a,edges:l}),{deletedNodes:a,deletedEdges:l}},screenToFlowPosition:(i,s={snapToGrid:!0})=>{if(!c(e).domNode)return i;const a=s.snapToGrid?c(e).snapGrid:!1,{x:l,y:u,zoom:d}=c(e).viewport,{x:p,y:f}=c(e).domNode.getBoundingClientRect(),g={x:i.x-p,y:i.y-f};return Vo(g,[l,u,d],a!==null,a||[1,1])},flowToScreenPosition:i=>{if(!c(e).domNode)return i;const{x:s,y:a,zoom:l}=c(e).viewport,{x:u,y:d}=c(e).domNode.getBoundingClientRect(),p=Ii(i,[s,a,l]);return{x:p.x+u,y:p.y+d}},toObject:()=>structuredClone({nodes:[...c(e).nodes],edges:[...c(e).edges],viewport:{...c(e).viewport}}),updateNode:n,updateNodeData:(i,s,a)=>{const l=c(e).nodeLookup.get(i)?.internals.userNode;if(!l)return;const u=typeof s=="function"?s(l):s;n(i,d=>({...d,data:a?.replace?u:{...d.data,...u}}))},updateEdge:r,getNodesBounds:i=>r0(i,{nodeLookup:c(e).nodeLookup,nodeOrigin:c(e).nodeOrigin}),getHandleConnections:({type:i,id:s,nodeId:a})=>Array.from(c(e).connectionLookup.get(`${a}-${i}-${s??null}`)?.values()??[])}}function fd(e,t){const n=[];for(const r of t){const o=e.get(r);if(o){const i="internals"in o?o.internals?.userNode:o;n.push(i)}}return n}function pd(e,t){de(t,!0);let n=y(t,"store",15),r=y(t,"selectionKey",7,"Shift"),o=y(t,"multiSelectionKey",23,()=>Cr()?"Meta":"Control"),i=y(t,"deleteKey",7,"Backspace"),s=y(t,"panActivationKey",7," "),a=y(t,"zoomActivationKey",23,()=>Cr()?"Meta":"Control"),{deleteElements:l}=yt();function u(w){return w!==null&&typeof w=="object"}function d(w){return u(w)?w.modifier||[]:[]}function p(w){return w==null?"":u(w)?w.key:w}function f(w,b){return(Array.isArray(w)?w:[w]).map(x=>{const $=p(x);return{key:$,modifier:d(x),enabled:$!==null,callback:b}})}function g(){n(n().selectionRect=null,!0),n(n().selectionKeyPressed=!1,!0),n(n().multiselectionKeyPressed=!1,!0),n(n().deleteKeyPressed=!1,!0),n(n().panActivationKeyPressed=!1,!0),n(n().zoomActivationKeyPressed=!1,!0)}function h(){const w=n().nodes.filter(x=>x.selected),b=n().edges.filter(x=>x.selected);l({nodes:w,edges:b})}var v={get store(){return n()},set store(w){n(w),m()},get selectionKey(){return r()},set selectionKey(w="Shift"){r(w),m()},get multiSelectionKey(){return o()},set multiSelectionKey(w=Cr()?"Meta":"Control"){o(w),m()},get deleteKey(){return i()},set deleteKey(w="Backspace"){i(w),m()},get panActivationKey(){return s()},set panActivationKey(w=" "){s(w),m()},get zoomActivationKey(){return a()},set zoomActivationKey(w=Cr()?"Meta":"Control"){a(w),m()}};return Il("blur",Dt,g),Il("contextmenu",Dt,g),$t(Dt,(w,b)=>Sn?.(w,b),()=>({trigger:f(r(),()=>n(n().selectionKeyPressed=!0,!0)),type:"keydown"})),$t(Dt,(w,b)=>Sn?.(w,b),()=>({trigger:f(r(),()=>n(n().selectionKeyPressed=!1,!0)),type:"keyup"})),$t(Dt,(w,b)=>Sn?.(w,b),()=>({trigger:f(o(),()=>{n(n().multiselectionKeyPressed=!0,!0)}),type:"keydown"})),$t(Dt,(w,b)=>Sn?.(w,b),()=>({trigger:f(o(),()=>n(n().multiselectionKeyPressed=!1,!0)),type:"keyup"})),$t(Dt,(w,b)=>Sn?.(w,b),()=>({trigger:f(i(),w=>{!(w.originalEvent.ctrlKey||w.originalEvent.metaKey||w.originalEvent.shiftKey)&&!dc(w.originalEvent)&&(n(n().deleteKeyPressed=!0,!0),h())}),type:"keydown"})),$t(Dt,(w,b)=>Sn?.(w,b),()=>({trigger:f(i(),()=>n(n().deleteKeyPressed=!1,!0)),type:"keyup"})),$t(Dt,(w,b)=>Sn?.(w,b),()=>({trigger:f(s(),()=>n(n().panActivationKeyPressed=!0,!0)),type:"keydown"})),$t(Dt,(w,b)=>Sn?.(w,b),()=>({trigger:f(s(),()=>n(n().panActivationKeyPressed=!1,!0)),type:"keyup"})),$t(Dt,(w,b)=>Sn?.(w,b),()=>({trigger:f(a(),()=>n(n().zoomActivationKeyPressed=!0,!0)),type:"keydown"})),$t(Dt,(w,b)=>Sn?.(w,b),()=>({trigger:f(a(),()=>n(n().zoomActivationKeyPressed=!1,!0)),type:"keyup"})),fe(v)}ue(pd,{store:{},selectionKey:{},multiSelectionKey:{},deleteKey:{},panActivationKey:{},zoomActivationKey:{}},[],[],!0);var Rm=me('<path fill="none" class="svelte-flow__connection-path"></path>'),Im=me('<svg class="svelte-flow__connectionline"><g><!></g></svg>');function gd(e,t){de(t,!0);let n=y(t,"store",15),r=y(t,"type",7),o=y(t,"containerStyle",7),i=y(t,"style",7),s=y(t,"LineComponent",7),a=T(()=>{if(!n().connection.inProgress)return"";const f={sourceX:n().connection.from.x,sourceY:n().connection.from.y,sourcePosition:n().connection.fromPosition,targetX:n().connection.to.x,targetY:n().connection.to.y,targetPosition:n().connection.toPosition};switch(r()){case Rn.Bezier:{const[g]=hc(f);return g}case Rn.Straight:{const[g]=mc(f);return g}case Rn.Step:case Rn.SmoothStep:{const[g]=la({...f,borderRadius:r()===Rn.Step?0:void 0});return g}}});var l={get store(){return n()},set store(f){n(f),m()},get type(){return r()},set type(f){r(f),m()},get containerStyle(){return o()},set containerStyle(f){o(f),m()},get style(){return i()},set style(f){i(f),m()},get LineComponent(){return s()},set LineComponent(f){s(f),m()}},u=Ne(),d=ae(u);{var p=f=>{var g=Im(),h=I(g),v=I(h);{var w=x=>{var $=Ne(),S=ae($);Ds(S,s,(E,D)=>{D(E,{})}),L(x,$)},b=x=>{var $=Rm();Se(()=>{Ce($,"d",c(a)),mt($,i())}),L(x,$)};ce(v,x=>{s()?x(w):x(b,!1)})}R(h),R(g),Se(x=>{Ce(g,"width",n().width),Ce(g,"height",n().height),mt(g,o()),Mt(h,0,x)},[()=>Hn(["svelte-flow__connection",t0(n().connection.isValid)])]),L(f,g)};ce(d,f=>{n().connection.inProgress&&f(p)})}return L(e,u),fe(l)}ue(gd,{store:{},type:{},containerStyle:{},style:{},LineComponent:{}},[],[],!0);var qm=Q("<div><!></div>");function Ro(e,t){de(t,!0);let n=y(t,"position",7,"top-right"),r=y(t,"style",7),o=y(t,"class",7),i=y(t,"children",7),s=Ke(t,["$$slots","$$events","$$legacy","$$host","position","style","class","children"]),a=T(()=>`${n()}`.split("-"));var l={get position(){return n()},set position(p="top-right"){n(p),m()},get style(){return r()},set style(p){r(p),m()},get class(){return o()},set class(p){o(p),m()},get children(){return i()},set children(p){i(p),m()}},u=qm();ut(u,p=>({class:p,style:r(),...s}),[()=>["svelte-flow__panel",o(),...c(a)]]);var d=I(u);return tt(d,()=>i()??ht),R(u),L(e,u),fe(l)}ue(Ro,{position:{},style:{},class:{},children:{}},[],[],!0);var Zm=Q('<a href="https://svelteflow.dev" target="_blank" rel="noopener noreferrer" aria-label="Svelte Flow attribution">Svelte Flow</a>');function hd(e,t){de(t,!0);let n=y(t,"proOptions",7),r=y(t,"position",7,"bottom-right");var o={get proOptions(){return n()},set proOptions(l){n(l),m()},get position(){return r()},set position(l="bottom-right"){r(l),m()}},i=Ne(),s=ae(i);{var a=l=>{Ro(l,{get position(){return r()},class:"svelte-flow__attribution","data-message":"Feel free to remove the attribution or check out how you could support us: https://svelteflow.dev/support-us",children:(u,d)=>{var p=Zm();L(u,p)},$$slots:{default:!0}})};ce(s,l=>{n()?.hideAttribution||l(a)})}return L(e,i),fe(o)}ue(hd,{proOptions:{},position:{}},[],[],!0);var Bm=Q("<div><!></div>");const Km={hash:"svelte-12wlba6",code:".svelte-flow.svelte-12wlba6 {width:100%;height:100%;overflow:hidden;position:relative;z-index:0;background-color:var(--background-color, var(--background-color-default));}:root {--background-color-default: #fff;--background-pattern-color-default: #ddd;--minimap-mask-color-default: rgb(240, 240, 240, 0.6);--minimap-mask-stroke-color-default: none;--minimap-mask-stroke-width-default: 1;--controls-button-background-color-default: #fefefe;--controls-button-background-color-hover-default: #f4f4f4;--controls-button-color-default: inherit;--controls-button-color-hover-default: inherit;--controls-button-border-color-default: #eee;}"};function vd(e,t){de(t,!0),qe(e,Km);let n=y(t,"width",7),r=y(t,"height",7),o=y(t,"colorMode",7),i=y(t,"domNode",15),s=y(t,"clientWidth",15),a=y(t,"clientHeight",15),l=y(t,"children",7),u=y(t,"rest",7),d=T(()=>u().class),p=T(()=>Rp(u(),["id","class","nodeTypes","edgeTypes","colorMode","isValidConnection","onmove","onmovestart","onmoveend","onflowerror","ondelete","onbeforedelete","onbeforeconnect","onconnect","onconnectstart","onconnectend","onbeforereconnect","onreconnect","onreconnectstart","onreconnectend","onclickconnectstart","onclickconnectend","oninit","onselectionchange","onselectiondragstart","onselectiondrag","onselectiondragstop","onselectionstart","onselectionend","clickConnect","fitView","fitViewOptions","nodeOrigin","nodeDragThreshold","connectionDragThreshold","minZoom","maxZoom","initialViewport","connectionRadius","connectionMode","selectionMode","selectNodesOnDrag","snapGrid","defaultMarkerColor","translateExtent","nodeExtent","onlyRenderVisibleElements","autoPanOnConnect","autoPanOnNodeDrag","colorModeSSR","style","defaultEdgeOptions","elevateNodesOnSelect","elevateEdgesOnSelect","nodesDraggable","autoPanOnNodeFocus","nodesConnectable","elementsSelectable","nodesFocusable","edgesFocusable","disableKeyboardA11y","noDragClass","noPanClass","noWheelClass","ariaLabelConfig"]));function f(w){w.currentTarget.scrollTo({top:0,left:0,behavior:"auto"}),u().onscroll&&u().onscroll(w)}var g={get width(){return n()},set width(w){n(w),m()},get height(){return r()},set height(w){r(w),m()},get colorMode(){return o()},set colorMode(w){o(w),m()},get domNode(){return i()},set domNode(w){i(w),m()},get clientWidth(){return s()},set clientWidth(w){s(w),m()},get clientHeight(){return a()},set clientHeight(w){a(w),m()},get children(){return l()},set children(w){l(w),m()},get rest(){return u()},set rest(w){u(w),m()}},h=Bm();ut(h,w=>({class:["svelte-flow","svelte-flow__container",c(d),o()],"data-testid":"svelte-flow__wrapper",role:"application",onscroll:f,...c(p),[yn]:w}),[()=>({width:_n(n()),height:_n(r())})],void 0,"svelte-12wlba6");var v=I(h);return tt(v,()=>l()??ht),R(h),qt(h,w=>i(w),()=>i()),Ql(h,"clientHeight",a),Ql(h,"clientWidth",s),L(e,h),fe(g)}ue(vd,{width:{},height:{},colorMode:{},domNode:{},clientWidth:{},clientHeight:{},children:{},rest:{}},[],[],!0);var jm=Q('<div class="svelte-flow__viewport-back svelte-flow__container"></div> <!> <div class="svelte-flow__edge-labels svelte-flow__container"></div> <!> <!> <!> <div class="svelte-flow__viewport-front svelte-flow__container"></div>',1),Ym=Q("<!> <!>",1),Xm=Q("<!> <!> <!> <!> <!>",1);function md(e,t){de(t,!0);let n=y(t,"width",7),r=y(t,"height",7),o=y(t,"proOptions",7),i=y(t,"selectionKey",7),s=y(t,"deleteKey",7),a=y(t,"panActivationKey",7),l=y(t,"multiSelectionKey",7),u=y(t,"zoomActivationKey",7),d=y(t,"paneClickDistance",7,1),p=y(t,"nodeClickDistance",7,1),f=y(t,"onmovestart",7),g=y(t,"onmoveend",7),h=y(t,"onmove",7),v=y(t,"oninit",7),w=y(t,"onnodeclick",7),b=y(t,"onnodecontextmenu",7),x=y(t,"onnodedrag",7),$=y(t,"onnodedragstart",7),S=y(t,"onnodedragstop",7),E=y(t,"onnodepointerenter",7),D=y(t,"onnodepointermove",7),O=y(t,"onnodepointerleave",7),q=y(t,"onselectionclick",7),K=y(t,"onselectioncontextmenu",7),J=y(t,"onselectionstart",7),A=y(t,"onselectionend",7),_=y(t,"onedgeclick",7),k=y(t,"onedgecontextmenu",7),C=y(t,"onedgepointerenter",7),N=y(t,"onedgepointerleave",7),P=y(t,"onpaneclick",7),H=y(t,"onpanecontextmenu",7),Z=y(t,"panOnScrollMode",23,()=>$n.Free),Y=y(t,"preventScrolling",7,!0),M=y(t,"zoomOnScroll",7,!0),X=y(t,"zoomOnDoubleClick",7,!0),te=y(t,"zoomOnPinch",7,!0),oe=y(t,"panOnScroll",7,!1),j=y(t,"panOnDrag",7,!0),G=y(t,"selectionOnDrag",7,!0),F=y(t,"connectionLineComponent",7),se=y(t,"connectionLineStyle",7),W=y(t,"connectionLineContainerStyle",7),ye=y(t,"connectionLineType",23,()=>Rn.Bezier),xe=y(t,"attributionPosition",7),ie=y(t,"children",7),ee=y(t,"nodes",31,()=>Yt([])),re=y(t,"edges",31,()=>Yt([])),ge=y(t,"viewport",31,()=>{}),he=Ke(t,["$$slots","$$events","$$legacy","$$host","width","height","proOptions","selectionKey","deleteKey","panActivationKey","multiSelectionKey","zoomActivationKey","paneClickDistance","nodeClickDistance","onmovestart","onmoveend","onmove","oninit","onnodeclick","onnodecontextmenu","onnodedrag","onnodedragstart","onnodedragstop","onnodepointerenter","onnodepointermove","onnodepointerleave","onselectionclick","onselectioncontextmenu","onselectionstart","onselectionend","onedgeclick","onedgecontextmenu","onedgepointerenter","onedgepointerleave","onpaneclick","onpanecontextmenu","panOnScrollMode","preventScrolling","zoomOnScroll","zoomOnDoubleClick","zoomOnPinch","panOnScroll","panOnDrag","selectionOnDrag","connectionLineComponent","connectionLineStyle","connectionLineContainerStyle","connectionLineType","attributionPosition","children","nodes","edges","viewport"]),le=Fc({props:he,width:n(),height:r(),get nodes(){return ee()},set nodes(B){ee(B)},get edges(){return re()},set edges(B){re(B)},get viewport(){return ge()},set viewport(B){ge(B)}});const Te=Xn(Ki);Te&&Te.setStore&&Te.setStore(le),Lr(Ki,{provider:!1,getStore(){return le}}),et(()=>{const B={nodes:le.selectedNodes,edges:le.selectedEdges};vt(()=>t.onselectionchange)?.(B);for(const ct of le.selectionChangeHandlers.values())ct(B)}),ui(()=>{le.reset()});var ke={get width(){return n()},set width(B){n(B),m()},get height(){return r()},set height(B){r(B),m()},get proOptions(){return o()},set proOptions(B){o(B),m()},get selectionKey(){return i()},set selectionKey(B){i(B),m()},get deleteKey(){return s()},set deleteKey(B){s(B),m()},get panActivationKey(){return a()},set panActivationKey(B){a(B),m()},get multiSelectionKey(){return l()},set multiSelectionKey(B){l(B),m()},get zoomActivationKey(){return u()},set zoomActivationKey(B){u(B),m()},get paneClickDistance(){return d()},set paneClickDistance(B=1){d(B),m()},get nodeClickDistance(){return p()},set nodeClickDistance(B=1){p(B),m()},get onmovestart(){return f()},set onmovestart(B){f(B),m()},get onmoveend(){return g()},set onmoveend(B){g(B),m()},get onmove(){return h()},set onmove(B){h(B),m()},get oninit(){return v()},set oninit(B){v(B),m()},get onnodeclick(){return w()},set onnodeclick(B){w(B),m()},get onnodecontextmenu(){return b()},set onnodecontextmenu(B){b(B),m()},get onnodedrag(){return x()},set onnodedrag(B){x(B),m()},get onnodedragstart(){return $()},set onnodedragstart(B){$(B),m()},get onnodedragstop(){return S()},set onnodedragstop(B){S(B),m()},get onnodepointerenter(){return E()},set onnodepointerenter(B){E(B),m()},get onnodepointermove(){return D()},set onnodepointermove(B){D(B),m()},get onnodepointerleave(){return O()},set onnodepointerleave(B){O(B),m()},get onselectionclick(){return q()},set onselectionclick(B){q(B),m()},get onselectioncontextmenu(){return K()},set onselectioncontextmenu(B){K(B),m()},get onselectionstart(){return J()},set onselectionstart(B){J(B),m()},get onselectionend(){return A()},set onselectionend(B){A(B),m()},get onedgeclick(){return _()},set onedgeclick(B){_(B),m()},get onedgecontextmenu(){return k()},set onedgecontextmenu(B){k(B),m()},get onedgepointerenter(){return C()},set onedgepointerenter(B){C(B),m()},get onedgepointerleave(){return N()},set onedgepointerleave(B){N(B),m()},get onpaneclick(){return P()},set onpaneclick(B){P(B),m()},get onpanecontextmenu(){return H()},set onpanecontextmenu(B){H(B),m()},get panOnScrollMode(){return Z()},set panOnScrollMode(B=$n.Free){Z(B),m()},get preventScrolling(){return Y()},set preventScrolling(B=!0){Y(B),m()},get zoomOnScroll(){return M()},set zoomOnScroll(B=!0){M(B),m()},get zoomOnDoubleClick(){return X()},set zoomOnDoubleClick(B=!0){X(B),m()},get zoomOnPinch(){return te()},set zoomOnPinch(B=!0){te(B),m()},get panOnScroll(){return oe()},set panOnScroll(B=!1){oe(B),m()},get panOnDrag(){return j()},set panOnDrag(B=!0){j(B),m()},get selectionOnDrag(){return G()},set selectionOnDrag(B=!0){G(B),m()},get connectionLineComponent(){return F()},set connectionLineComponent(B){F(B),m()},get connectionLineStyle(){return se()},set connectionLineStyle(B){se(B),m()},get connectionLineContainerStyle(){return W()},set connectionLineContainerStyle(B){W(B),m()},get connectionLineType(){return ye()},set connectionLineType(B=Rn.Bezier){ye(B),m()},get attributionPosition(){return xe()},set attributionPosition(B){xe(B),m()},get children(){return ie()},set children(B){ie(B),m()},get nodes(){return ee()},set nodes(B=[]){ee(B),m()},get edges(){return re()},set edges(B=[]){re(B),m()},get viewport(){return ge()},set viewport(B=void 0){ge(B),m()}};return vd(e,{get colorMode(){return le.colorMode},get width(){return n()},get height(){return r()},get rest(){return he},get domNode(){return le.domNode},set domNode(B){le.domNode=B},get clientWidth(){return le.width},set clientWidth(B){le.width=B},get clientHeight(){return le.height},set clientHeight(B){le.height=B},children:(B,ct)=>{var Ae=Xm(),Ze=ae(Ae);pd(Ze,{get selectionKey(){return i()},get deleteKey(){return s()},get panActivationKey(){return a()},get multiSelectionKey(){return l()},get zoomActivationKey(){return u()},get store(){return le},set store(Be){le=Be}});var Re=V(Ze,2);Wc(Re,{get panOnScrollMode(){return Z()},get preventScrolling(){return Y()},get zoomOnScroll(){return M()},get zoomOnDoubleClick(){return X()},get zoomOnPinch(){return te()},get panOnScroll(){return oe()},get panOnDrag(){return j()},get paneClickDistance(){return d()},get onmovestart(){return f()},get onmove(){return h()},get onmoveend(){return g()},get oninit(){return v()},get store(){return le},set store(Be){le=Be},children:(Be,Qe)=>{Qc(Be,{get onpaneclick(){return P()},get onpanecontextmenu(){return H()},get onselectionstart(){return J()},get onselectionend(){return A()},get panOnDrag(){return j()},get selectionOnDrag(){return G()},get store(){return le},set store(ve){le=ve},children:(ve,Ye)=>{var ft=Ym(),st=ae(ft);ed(st,{get store(){return le},set store(Je){le=Je},children:(Je,kt)=>{var Gt=jm(),Nn=V(ae(Gt),2);cd(Nn,{get onedgeclick(){return _()},get onedgecontextmenu(){return k()},get onedgepointerenter(){return C()},get onedgepointerleave(){return N()},get store(){return le},set store(fn){le=fn}});var or=V(Nn,4);gd(or,{get type(){return ye()},get LineComponent(){return F()},get containerStyle(){return W()},get style(){return se()},get store(){return le},set store(fn){le=fn}});var Fo=V(or,2);sd(Fo,{get nodeClickDistance(){return p()},get onnodeclick(){return w()},get onnodecontextmenu(){return b()},get onnodepointerenter(){return E()},get onnodepointermove(){return D()},get onnodepointerleave(){return O()},get onnodedrag(){return x()},get onnodedragstart(){return $()},get onnodedragstop(){return S()},get store(){return le},set store(fn){le=fn}});var ir=V(Fo,2);dd(ir,{get onselectionclick(){return q()},get onselectioncontextmenu(){return K()},get onnodedrag(){return x()},get onnodedragstart(){return $()},get onnodedragstop(){return S()},get store(){return le},set store(fn){le=fn}}),we(2),L(Je,Gt)},$$slots:{default:!0}});var Nt=V(st,2);{let Je=T(()=>!!(le.selectionRect&&le.selectionRectMode==="user")),kt=T(()=>le.selectionRect?.width),Gt=T(()=>le.selectionRect?.height),Nn=T(()=>le.selectionRect?.x),or=T(()=>le.selectionRect?.y);wa(Nt,{get isVisible(){return c(Je)},get width(){return c(kt)},get height(){return c(Gt)},get x(){return c(Nn)},get y(){return c(or)}})}L(ve,ft)},$$slots:{default:!0}})},$$slots:{default:!0}});var dt=V(Re,2);hd(dt,{get proOptions(){return o()},get position(){return xe()}});var it=V(dt,2);nd(it,{get store(){return le}});var Pt=V(it,2);tt(Pt,()=>ie()??ht),L(B,Ae)},$$slots:{default:!0}}),fe(ke)}ue(md,{width:{},height:{},proOptions:{},selectionKey:{},deleteKey:{},panActivationKey:{},multiSelectionKey:{},zoomActivationKey:{},paneClickDistance:{},nodeClickDistance:{},onmovestart:{},onmoveend:{},onmove:{},oninit:{},onnodeclick:{},onnodecontextmenu:{},onnodedrag:{},onnodedragstart:{},onnodedragstop:{},onnodepointerenter:{},onnodepointermove:{},onnodepointerleave:{},onselectionclick:{},onselectioncontextmenu:{},onselectionstart:{},onselectionend:{},onedgeclick:{},onedgecontextmenu:{},onedgepointerenter:{},onedgepointerleave:{},onpaneclick:{},onpanecontextmenu:{},panOnScrollMode:{},preventScrolling:{},zoomOnScroll:{},zoomOnDoubleClick:{},zoomOnPinch:{},panOnScroll:{},panOnDrag:{},selectionOnDrag:{},connectionLineComponent:{},connectionLineStyle:{},connectionLineContainerStyle:{},connectionLineType:{},attributionPosition:{},children:{},nodes:{},edges:{},viewport:{}},[],[],!0);function yd(e,t){de(t,!0);let n=y(t,"children",7),r=De(Fc({props:{},nodes:[],edges:[]}));Lr(Ki,{provider:!0,getStore(){return c(r)},setStore:a=>{U(r,a)}}),ui(()=>{c(r).reset()});var o={get children(){return n()},set children(a){n(a),m()}},i=Ne(),s=ae(i);return tt(s,()=>n()??ht),L(e,i),fe(o)}ue(yd,{children:{}},[],[],!0);var Fm=Q("<button><!></button>");function Io(e,t){de(t,!0);let n=y(t,"class",7),r=y(t,"bgColor",7),o=y(t,"bgColorHover",7),i=y(t,"color",7),s=y(t,"colorHover",7),a=y(t,"borderColor",7),l=y(t,"onclick",7),u=y(t,"children",7),d=Ke(t,["$$slots","$$events","$$legacy","$$host","class","bgColor","bgColorHover","color","colorHover","borderColor","onclick","children"]);var p={get class(){return n()},set class(h){n(h),m()},get bgColor(){return r()},set bgColor(h){r(h),m()},get bgColorHover(){return o()},set bgColorHover(h){o(h),m()},get color(){return i()},set color(h){i(h),m()},get colorHover(){return s()},set colorHover(h){s(h),m()},get borderColor(){return a()},set borderColor(h){a(h),m()},get onclick(){return l()},set onclick(h){l(h),m()},get children(){return u()},set children(h){u(h),m()}},f=Fm();ut(f,h=>({type:"button",onclick:l(),class:["svelte-flow__controls-button",n()],...d,[yn]:h}),[()=>({"--xy-controls-button-background-color-props":r(),"--xy-controls-button-background-color-hover-props":o(),"--xy-controls-button-color-props":i(),"--xy-controls-button-color-hover-props":s(),"--xy-controls-button-border-color-props":a()})]);var g=I(f);return tt(g,()=>u()??ht),R(f),L(e,f),fe(p)}ue(Io,{class:{},bgColor:{},bgColorHover:{},color:{},colorHover:{},borderColor:{},onclick:{},children:{}},[],[],!0);var Wm=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32"><path d="M32 18.133H18.133V32h-4.266V18.133H0v-4.266h13.867V0h4.266v13.867H32z"></path></svg>');function wd(e){var t=Wm();L(e,t)}ue(wd,{},[],[],!0);var Gm=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 5"><path d="M0 0h32v4.2H0z"></path></svg>');function bd(e){var t=Gm();L(e,t)}ue(bd,{},[],[],!0);var Um=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 30"><path d="M3.692 4.63c0-.53.4-.938.939-.938h5.215V0H4.708C2.13 0 0 2.054 0 4.63v5.216h3.692V4.631zM27.354 0h-5.2v3.692h5.17c.53 0 .984.4.984.939v5.215H32V4.631A4.624 4.624 0 0027.354 0zm.954 24.83c0 .532-.4.94-.939.94h-5.215v3.768h5.215c2.577 0 4.631-2.13 4.631-4.707v-5.139h-3.692v5.139zm-23.677.94c-.531 0-.939-.4-.939-.94v-5.138H0v5.139c0 2.577 2.13 4.707 4.708 4.707h5.138V25.77H4.631z"></path></svg>');function xd(e){var t=Um();L(e,t)}ue(xd,{},[],[],!0);var Jm=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 25 32"><path d="M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0 8 0 4.571 3.429 4.571 7.619v3.048H3.048A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047zm4.724-13.866H7.467V7.619c0-2.59 2.133-4.724 4.723-4.724 2.591 0 4.724 2.133 4.724 4.724v3.048z"></path></svg>');function Cd(e){var t=Jm();L(e,t)}ue(Cd,{},[],[],!0);var Qm=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 25 32"><path d="M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0c-4.114 1.828-1.37 2.133.305 2.438 1.676.305 4.42 2.59 4.42 5.181v3.048H3.047A3.056 3.056 0 000 13.714v15.238A3.056 3.056 0 003.048 32h18.285a3.056 3.056 0 003.048-3.048V13.714a3.056 3.056 0 00-3.048-3.047zM12.19 24.533a3.056 3.056 0 01-3.047-3.047 3.056 3.056 0 013.047-3.048 3.056 3.056 0 013.048 3.048 3.056 3.056 0 01-3.048 3.047z"></path></svg>');function $d(e){var t=Qm();L(e,t)}ue($d,{},[],[],!0);var e2=Q("<!> <!>",1),t2=Q("<!> <!> <!> <!> <!> <!>",1);function kd(e,t){de(t,!0);let n=y(t,"position",7,"bottom-left"),r=y(t,"orientation",7,"vertical"),o=y(t,"showZoom",7,!0),i=y(t,"showFitView",7,!0),s=y(t,"showLock",7,!0),a=y(t,"style",7),l=y(t,"class",7),u=y(t,"buttonBgColor",7),d=y(t,"buttonBgColorHover",7),p=y(t,"buttonColor",7),f=y(t,"buttonColorHover",7),g=y(t,"buttonBorderColor",7),h=y(t,"fitViewOptions",7),v=y(t,"children",7),w=y(t,"before",7),b=y(t,"after",7),x=Ke(t,["$$slots","$$events","$$legacy","$$host","position","orientation","showZoom","showFitView","showLock","style","class","buttonBgColor","buttonBgColorHover","buttonColor","buttonColorHover","buttonBorderColor","fitViewOptions","children","before","after"]),$=T(ln);const S={bgColor:u(),bgColorHover:d(),color:p(),colorHover:f(),borderColor:g()};let E=T(()=>c($).nodesDraggable||c($).nodesConnectable||c($).elementsSelectable),D=T(()=>c($).viewport.zoom<=c($).minZoom),O=T(()=>c($).viewport.zoom>=c($).maxZoom),q=T(()=>c($).ariaLabelConfig),K=T(()=>r()==="horizontal"?"horizontal":"vertical");const J=()=>{c($).zoomIn()},A=()=>{c($).zoomOut()},_=()=>{c($).fitView(h())},k=()=>{let N=!c(E);c($).nodesDraggable=N,c($).nodesConnectable=N,c($).elementsSelectable=N};var C={get position(){return n()},set position(N="bottom-left"){n(N),m()},get orientation(){return r()},set orientation(N="vertical"){r(N),m()},get showZoom(){return o()},set showZoom(N=!0){o(N),m()},get showFitView(){return i()},set showFitView(N=!0){i(N),m()},get showLock(){return s()},set showLock(N=!0){s(N),m()},get style(){return a()},set style(N){a(N),m()},get class(){return l()},set class(N){l(N),m()},get buttonBgColor(){return u()},set buttonBgColor(N){u(N),m()},get buttonBgColorHover(){return d()},set buttonBgColorHover(N){d(N),m()},get buttonColor(){return p()},set buttonColor(N){p(N),m()},get buttonColorHover(){return f()},set buttonColorHover(N){f(N),m()},get buttonBorderColor(){return g()},set buttonBorderColor(N){g(N),m()},get fitViewOptions(){return h()},set fitViewOptions(N){h(N),m()},get children(){return v()},set children(N){v(N),m()},get before(){return w()},set before(N){w(N),m()},get after(){return b()},set after(N){b(N),m()}};{let N=T(()=>["svelte-flow__controls",c(K),l()]);Ro(e,Fe({get class(){return c(N)},get position(){return n()},"data-testid":"svelte-flow__controls",get"aria-label"(){return c(q)["controls.ariaLabel"]},get style(){return a()}},()=>x,{children:(P,H)=>{var Z=t2(),Y=ae(Z);{var M=ie=>{var ee=Ne(),re=ae(ee);tt(re,w),L(ie,ee)};ce(Y,ie=>{w()&&ie(M)})}var X=V(Y,2);{var te=ie=>{var ee=e2(),re=ae(ee);Io(re,Fe({onclick:J,class:"svelte-flow__controls-zoomin",get title(){return c(q)["controls.zoomIn.ariaLabel"]},get"aria-label"(){return c(q)["controls.zoomIn.ariaLabel"]},get disabled(){return c(O)}},()=>S,{children:(he,le)=>{wd(he)},$$slots:{default:!0}}));var ge=V(re,2);Io(ge,Fe({onclick:A,class:"svelte-flow__controls-zoomout",get title(){return c(q)["controls.zoomOut.ariaLabel"]},get"aria-label"(){return c(q)["controls.zoomOut.ariaLabel"]},get disabled(){return c(D)}},()=>S,{children:(he,le)=>{bd(he)},$$slots:{default:!0}})),L(ie,ee)};ce(X,ie=>{o()&&ie(te)})}var oe=V(X,2);{var j=ie=>{Io(ie,Fe({class:"svelte-flow__controls-fitview",onclick:_,get title(){return c(q)["controls.fitView.ariaLabel"]},get"aria-label"(){return c(q)["controls.fitView.ariaLabel"]}},()=>S,{children:(ee,re)=>{xd(ee)},$$slots:{default:!0}}))};ce(oe,ie=>{i()&&ie(j)})}var G=V(oe,2);{var F=ie=>{Io(ie,Fe({class:"svelte-flow__controls-interactive",onclick:k,get title(){return c(q)["controls.interactive.ariaLabel"]},get"aria-label"(){return c(q)["controls.interactive.ariaLabel"]}},()=>S,{children:(ee,re)=>{var ge=Ne(),he=ae(ge);{var le=ke=>{$d(ke)},Te=ke=>{Cd(ke)};ce(he,ke=>{c(E)?ke(le):ke(Te,!1)})}L(ee,ge)},$$slots:{default:!0}}))};ce(G,ie=>{s()&&ie(F)})}var se=V(G,2);{var W=ie=>{var ee=Ne(),re=ae(ee);tt(re,v),L(ie,ee)};ce(se,ie=>{v()&&ie(W)})}var ye=V(se,2);{var xe=ie=>{var ee=Ne(),re=ae(ee);tt(re,b),L(ie,ee)};ce(ye,ie=>{b()&&ie(xe)})}L(P,Z)},$$slots:{default:!0}}))}return fe(C)}ue(kd,{position:{},orientation:{},showZoom:{},showFitView:{},showLock:{},style:{},class:{},buttonBgColor:{},buttonBgColorHover:{},buttonColor:{},buttonColorHover:{},buttonBorderColor:{},fitViewOptions:{},children:{},before:{},after:{}},[],[],!0);var qn;(function(e){e.Lines="lines",e.Dots="dots",e.Cross="cross"})(qn||(qn={}));var n2=me("<circle></circle>");function _d(e,t){de(t,!0);let n=y(t,"radius",7),r=y(t,"class",7);var o={get radius(){return n()},set radius(s){n(s),m()},get class(){return r()},set class(s){r(s),m()}},i=n2();return Se(()=>{Ce(i,"cx",n()),Ce(i,"cy",n()),Ce(i,"r",n()),Mt(i,0,Hn(["svelte-flow__background-pattern","dots",r()]))}),L(e,i),fe(o)}ue(_d,{radius:{},class:{}},[],[],!0);var r2=me("<path></path>");function Sd(e,t){de(t,!0);let n=y(t,"lineWidth",7),r=y(t,"dimensions",7),o=y(t,"variant",7),i=y(t,"class",7);var s={get lineWidth(){return n()},set lineWidth(l){n(l),m()},get dimensions(){return r()},set dimensions(l){r(l),m()},get variant(){return o()},set variant(l){o(l),m()},get class(){return i()},set class(l){i(l),m()}},a=r2();return Se(()=>{Ce(a,"stroke-width",n()),Ce(a,"d",`M${r()[0]/2} 0 V${r()[1]} M0 ${r()[1]/2} H${r()[0]}`),Mt(a,0,Hn(["svelte-flow__background-pattern",o(),i()]))}),L(e,a),fe(s)}ue(Sd,{lineWidth:{},dimensions:{},variant:{},class:{}},[],[],!0);const o2={[qn.Dots]:1,[qn.Lines]:1,[qn.Cross]:6};var i2=me('<svg data-testid="svelte-flow__background"><pattern patternUnits="userSpaceOnUse"><!></pattern><rect x="0" y="0" width="100%" height="100%"></rect></svg>');function Ed(e,t){de(t,!0);let n=y(t,"id",7),r=y(t,"variant",23,()=>qn.Dots),o=y(t,"gap",7,20),i=y(t,"size",7),s=y(t,"lineWidth",7,1),a=y(t,"bgColor",7),l=y(t,"patternColor",7),u=y(t,"patternClass",7),d=y(t,"class",7),p=T(ln),f=T(()=>r()===qn.Dots),g=T(()=>r()===qn.Cross),h=T(()=>Array.isArray(o())?o():[o(),o()]),v=T(()=>`background-pattern-${c(p).flowId}-${n()??""}`),w=T(()=>[c(h)[0]*c(p).viewport.zoom||1,c(h)[1]*c(p).viewport.zoom||1]),b=T(()=>(i()??o2[r()])*c(p).viewport.zoom),x=T(()=>c(g)?[c(b),c(b)]:c(w)),$=T(()=>c(f)?[c(b)/2,c(b)/2]:[c(x)[0]/2,c(x)[1]/2]);var S={get id(){return n()},set id(_){n(_),m()},get variant(){return r()},set variant(_=qn.Dots){r(_),m()},get gap(){return o()},set gap(_=20){o(_),m()},get size(){return i()},set size(_){i(_),m()},get lineWidth(){return s()},set lineWidth(_=1){s(_),m()},get bgColor(){return a()},set bgColor(_){a(_),m()},get patternColor(){return l()},set patternColor(_){l(_),m()},get patternClass(){return u()},set patternClass(_){u(_),m()},get class(){return d()},set class(_){d(_),m()}},E=i2();let D;var O=I(E),q=I(O);{var K=_=>{{let k=T(()=>c(b)/2);_d(_,{get radius(){return c(k)},get class(){return u()}})}},J=_=>{Sd(_,{get dimensions(){return c(x)},get variant(){return r()},get lineWidth(){return s()},get class(){return u()}})};ce(q,_=>{c(f)?_(K):_(J,!1)})}R(O);var A=V(O);return R(E),Se(_=>{Mt(E,0,Hn(["svelte-flow__background","svelte-flow__container",d()])),D=mt(E,"",D,_),Ce(O,"id",c(v)),Ce(O,"x",c(p).viewport.x%c(w)[0]),Ce(O,"y",c(p).viewport.y%c(w)[1]),Ce(O,"width",c(w)[0]),Ce(O,"height",c(w)[1]),Ce(O,"patternTransform",`translate(-${c($)[0]},-${c($)[1]})`),Ce(A,"fill",`url(#${c(v)})`)},[()=>({"--xy-background-color-props":a(),"--xy-background-pattern-color-props":l()})]),L(e,E),fe(S)}ue(Ed,{id:{},variant:{},gap:{},size:{},lineWidth:{},bgColor:{},patternColor:{},patternClass:{},class:{}},[],[],!0);var s2=me("<rect></rect>");function Pd(e,t){de(t,!0);let n=y(t,"x",7),r=y(t,"y",7),o=y(t,"width",7),i=y(t,"height",7),s=y(t,"borderRadius",7,5),a=y(t,"color",7),l=y(t,"shapeRendering",7),u=y(t,"strokeColor",7),d=y(t,"strokeWidth",7,2),p=y(t,"selected",7),f=y(t,"class",7);var g={get x(){return n()},set x(b){n(b),m()},get y(){return r()},set y(b){r(b),m()},get width(){return o()},set width(b){o(b),m()},get height(){return i()},set height(b){i(b),m()},get borderRadius(){return s()},set borderRadius(b=5){s(b),m()},get color(){return a()},set color(b){a(b),m()},get shapeRendering(){return l()},set shapeRendering(b){l(b),m()},get strokeColor(){return u()},set strokeColor(b){u(b),m()},get strokeWidth(){return d()},set strokeWidth(b=2){d(b),m()},get selected(){return p()},set selected(b){p(b),m()},get class(){return f()},set class(b){f(b),m()}},h=s2();let v,w;return Se((b,x)=>{v=Mt(h,0,Hn(["svelte-flow__minimap-node",f()]),null,v,b),Ce(h,"x",n()),Ce(h,"y",r()),Ce(h,"rx",s()),Ce(h,"ry",s()),Ce(h,"width",o()),Ce(h,"height",i()),Ce(h,"shape-rendering",l()),w=mt(h,"",w,x)},[()=>({selected:p()}),()=>({fill:a(),stroke:u(),"stroke-width":d()})]),L(e,h),fe(g)}ue(Pd,{x:{},y:{},width:{},height:{},borderRadius:{},color:{},shapeRendering:{},strokeColor:{},strokeWidth:{},selected:{},class:{}},[],[],!0);function a2(e,t){const n=K0({domNode:e,panZoom:t.panZoom,getTransform:()=>{const{viewport:o}=t.store;return[o.x,o.y,o.zoom]},getViewScale:t.getViewScale});n.update({translateExtent:t.translateExtent,width:t.width,height:t.height,inversePan:t.inversePan,zoomStep:t.zoomStep,pannable:t.pannable,zoomable:t.zoomable});function r(o){n.update({translateExtent:o.translateExtent,width:o.width,height:o.height,inversePan:o.inversePan,zoomStep:o.zoomStep,pannable:o.pannable,zoomable:o.zoomable})}return{update:r,destroy(){n.destroy()}}}const ba=e=>e instanceof Function?e:()=>e;var l2=me("<title> </title>"),u2=me('<svg class="svelte-flow__minimap-svg" role="img"><!><!><path class="svelte-flow__minimap-mask" fill-rule="evenodd" pointer-events="none"></path></svg>'),c2=Q('<svelte-css-wrapper style="display: contents"><!></svelte-css-wrapper>',1);function Nd(e,t){de(t,!0);let n=y(t,"position",7,"bottom-right"),r=y(t,"ariaLabel",7),o=y(t,"nodeStrokeColor",7,"transparent"),i=y(t,"nodeColor",7),s=y(t,"nodeClass",7,""),a=y(t,"nodeBorderRadius",7,5),l=y(t,"nodeStrokeWidth",7,2),u=y(t,"bgColor",7),d=y(t,"maskColor",7),p=y(t,"maskStrokeColor",7),f=y(t,"maskStrokeWidth",7),g=y(t,"width",7,200),h=y(t,"height",7,150),v=y(t,"pannable",7,!0),w=y(t,"zoomable",7,!0),b=y(t,"inversePan",7),x=y(t,"zoomStep",7),$=y(t,"class",7),S=Ke(t,["$$slots","$$events","$$legacy","$$host","position","ariaLabel","nodeStrokeColor","nodeColor","nodeClass","nodeBorderRadius","nodeStrokeWidth","bgColor","maskColor","maskStrokeColor","maskStrokeWidth","width","height","pannable","zoomable","inversePan","zoomStep","class"]),E=T(ln),D=T(()=>c(E).ariaLabelConfig);const O=i()===void 0?void 0:ba(i()),q=ba(o()),K=ba(s()),J=typeof window>"u"||window.chrome?"crispEdges":"geometricPrecision";let A=T(()=>`svelte-flow__minimap-desc-${c(E).flowId}`),_=T(()=>({x:-c(E).viewport.x/c(E).viewport.zoom,y:-c(E).viewport.y/c(E).viewport.zoom,width:c(E).width/c(E).viewport.zoom,height:c(E).height/c(E).viewport.zoom})),k=T(()=>c(E).nodeLookup.size>0?sc(Lo(c(E).nodeLookup,{filter:W=>!W.hidden}),c(_)):c(_)),C=T(()=>c(k).width/g()),N=T(()=>c(k).height/h()),P=T(()=>Math.max(c(C),c(N))),H=T(()=>c(P)*g()),Z=T(()=>c(P)*h()),Y=T(()=>5*c(P)),M=T(()=>c(k).x-(c(H)-c(k).width)/2-c(Y)),X=T(()=>c(k).y-(c(Z)-c(k).height)/2-c(Y)),te=T(()=>c(H)+c(Y)*2),oe=T(()=>c(Z)+c(Y)*2);const j=()=>c(P);var G={get position(){return n()},set position(W="bottom-right"){n(W),m()},get ariaLabel(){return r()},set ariaLabel(W){r(W),m()},get nodeStrokeColor(){return o()},set nodeStrokeColor(W="transparent"){o(W),m()},get nodeColor(){return i()},set nodeColor(W){i(W),m()},get nodeClass(){return s()},set nodeClass(W=""){s(W),m()},get nodeBorderRadius(){return a()},set nodeBorderRadius(W=5){a(W),m()},get nodeStrokeWidth(){return l()},set nodeStrokeWidth(W=2){l(W),m()},get bgColor(){return u()},set bgColor(W){u(W),m()},get maskColor(){return d()},set maskColor(W){d(W),m()},get maskStrokeColor(){return p()},set maskStrokeColor(W){p(W),m()},get maskStrokeWidth(){return f()},set maskStrokeWidth(W){f(W),m()},get width(){return g()},set width(W=200){g(W),m()},get height(){return h()},set height(W=150){h(W),m()},get pannable(){return v()},set pannable(W=!0){v(W),m()},get zoomable(){return w()},set zoomable(W=!0){w(W),m()},get inversePan(){return b()},set inversePan(W){b(W),m()},get zoomStep(){return x()},set zoomStep(W){x(W),m()},get class(){return $()},set class(W){$(W),m()}},F=c2(),se=ae(F);{let W=T(()=>["svelte-flow__minimap",$()]);Qp(se,()=>({"--xy-minimap-background-color-props":u()})),Ro(se.lastChild,Fe({get position(){return n()},get class(){return c(W)},"data-testid":"svelte-flow__minimap"},()=>S,{children:(ye,xe)=>{var ie=Ne(),ee=ae(ie);{var re=ge=>{var he=u2();let le;var Te=I(he);{var ke=Ae=>{var Ze=l2(),Re=I(Ze,!0);R(Ze),Se(()=>{Ce(Ze,"id",c(A)),Xe(Re,r()??c(D)["minimap.ariaLabel"])}),L(Ae,Ze)};ce(Te,Ae=>{(r()??c(D)["minimap.ariaLabel"])&&Ae(ke)})}var B=V(Te);Ct(B,17,()=>c(E).nodes,Ae=>Ae.id,(Ae,Ze)=>{const Re=T(()=>c(E).nodeLookup.get(c(Ze).id));var dt=Ne(),it=ae(dt);{var Pt=Be=>{const Qe=T(()=>Jn(c(Re)));{let ve=T(()=>O?.(c(Re))),Ye=T(()=>q(c(Re))),ft=T(()=>K(c(Re)));Pd(Be,Fe({get x(){return c(Re).internals.positionAbsolute.x},get y(){return c(Re).internals.positionAbsolute.y}},()=>c(Qe),{get selected(){return c(Re).selected},get color(){return c(ve)},get borderRadius(){return a()},get strokeColor(){return c(Ye)},get strokeWidth(){return l()},get shapeRendering(){return J},get class(){return c(ft)}}))}};ce(it,Be=>{c(Re)&&lc(c(Re))&&Be(Pt)})}L(Ae,dt)});var ct=V(B);R(he),$t(he,(Ae,Ze)=>a2?.(Ae,Ze),()=>({store:c(E),panZoom:c(E).panZoom,getViewScale:j,translateExtent:c(E).translateExtent,width:c(E).width,height:c(E).height,inversePan:b(),zoomStep:x(),pannable:v(),zoomable:w()})),Se(Ae=>{Ce(he,"width",g()),Ce(he,"height",h()),Ce(he,"viewBox",`${c(M)??""} ${c(X)??""} ${c(te)??""} ${c(oe)??""}`),Ce(he,"aria-labelledby",c(A)),le=mt(he,"",le,Ae),Ce(ct,"d",`M${c(M)-c(Y)},${c(X)-c(Y)}h${c(te)+c(Y)*2}v${c(oe)+c(Y)*2}h${-c(te)-c(Y)*2}z
      M${c(_).x??""},${c(_).y??""}h${c(_).width??""}v${c(_).height??""}h${-c(_).width}z`)},[()=>({"--xy-minimap-mask-background-color-props":d(),"--xy-minimap-mask-stroke-color-props":p(),"--xy-minimap-mask-stroke-width-props":f()?f()*c(P):void 0})]),L(ge,he)};ce(ee,ge=>{c(E).panZoom&&ge(re)})}L(ye,ie)},$$slots:{default:!0}})),R(se)}return L(e,F),fe(G)}ue(Nd,{position:{},ariaLabel:{},nodeStrokeColor:{},nodeColor:{},nodeClass:{},nodeBorderRadius:{},nodeStrokeWidth:{},bgColor:{},maskColor:{},maskStrokeColor:{},maskStrokeWidth:{},width:{},height:{},pannable:{},zoomable:{},inversePan:{},zoomStep:{},class:{}},[],[],!0);var d2=Q("<div><!></div>");function Td(e,t){de(t,!0);let n=y(t,"nodeId",7),r=y(t,"position",23,()=>be.Top),o=y(t,"align",7,"center"),i=y(t,"offset",7,10),s=y(t,"isVisible",7),a=y(t,"children",7),l=Ke(t,["$$slots","$$events","$$legacy","$$host","nodeId","position","align","offset","isVisible","children"]);const u=ln(),{getNodesBounds:d}=yt(),p=Xn("svelteflow__node_id");let f=T(()=>(u.nodes,(Array.isArray(n())?n():[n()??p]).reduce((E,D)=>{const O=u.nodeLookup.get(D);return O&&E.push(O),E},[]))),g=T(()=>{const E=d(c(f));return E?_0(E,u.viewport,r(),i(),o()):""}),h=T(()=>c(f).length===0?1:Math.max(...c(f).map(E=>(E.internals.z||5)+1))),v=T(()=>u.nodes.filter(E=>E.selected).length),w=T(()=>typeof s()=="boolean"?s():c(f).length===1&&c(f)[0].selected&&c(v)===1);var b={get nodeId(){return n()},set nodeId(E){n(E),m()},get position(){return r()},set position(E=be.Top){r(E),m()},get align(){return o()},set align(E="center"){o(E),m()},get offset(){return i()},set offset(E=10){i(E),m()},get isVisible(){return s()},set isVisible(E){s(E),m()},get children(){return a()},set children(E){a(E),m()}},x=Ne(),$=ae(x);{var S=E=>{var D=d2();ut(D,(q,K)=>({class:"svelte-flow__node-toolbar","data-id":q,...l,[yn]:K}),[()=>c(f).reduce((q,K)=>`${q}${K.id} `,"").trim(),()=>({display:Rc().value?"none":void 0,position:"absolute",transform:c(g),"z-index":c(h)})]);var O=I(D);tt(O,()=>a()??ht),R(D),$t(D,(q,K)=>Ac?.(q,K),()=>"root"),L(E,D)};ce($,E=>{u.domNode&&c(w)&&c(f)&&E(S)})}return L(e,x),fe(b)}ue(Td,{nodeId:{},position:{},align:{},offset:{},isVisible:{},children:{}},[],[],!0);function Zn(e){const t=T(ln),n=T(()=>c(t).nodes),r=T(()=>c(t).nodeLookup);let o=[],i=!0;const s=T(()=>{c(n);const a=[],l=Array.isArray(e),u=l?e:[e];for(const d of u){const p=c(r).get(d)?.internals.userNode;p&&a.push({id:p.id,type:p.type,data:p.data})}return(!H0(a,o)||i)&&(o=a,i=!1),l?o:o[0]??null});return{get current(){return c(s)}}}const Dd="tinyflow-component",f2=[{value:"String",label:"String"},{value:"Number",label:"Number"},{value:"Boolean",label:"Boolean"},{value:"File",label:"File"},{value:"Object",label:"Object"},{value:"Array",label:"Array"}],p2=[{value:"ref",label:"引用"},{value:"fixed",label:"固定值"}],xa=[{label:"文字",value:"text"},{label:"图片",value:"image"},{label:"视频",value:"video"},{label:"音频",value:"audio"},{label:"文件",value:"file"},{label:"其他",value:"other"}],g2=[{label:"当行输入框",value:"input"},{label:"多行输入框",value:"textarea"},{label:"下拉菜单",value:"select"},{label:"单选",value:"radio"},{label:"多选",value:"checkbox"}],h2=[{label:"单选",value:"radio"},{label:"多选",value:"checkbox"}];class v2{options;rootEl;svelteFlowInstance;constructor(t){if(typeof t.element!="string"&&!(t.element instanceof Element))throw new Error("element must be a string or Element");this._setOptions(t),this._init()}_init(){if(typeof this.options.element=="string"){if(this.rootEl=document.querySelector(this.options.element),!this.rootEl)throw new Error(`element not found by document.querySelector('${this.options.element}')`)}else if(this.options.element instanceof Element)this.rootEl=this.options.element;else throw new Error("element must be a string or Element");const t=document.createElement(Dd);t.style.display="block",t.style.width="100%",t.style.height="100%",t.classList.add("tf-theme-light"),t.options=this.options,t.onInit=n=>{this.svelteFlowInstance=n},this.rootEl.appendChild(t)}_setOptions(t){this.options={...t}}getOptions(){return this.options}getData(){return this.svelteFlowInstance.toObject()}setData(t){this.options.data=t;const n=document.createElement(Dd);n.style.display="block",n.style.width="100%",n.style.height="100%",n.classList.add("tf-theme-light"),n.options=this.options,n.onInit=r=>{this.svelteFlowInstance=r},this.destroy(),this.rootEl.appendChild(n)}destroy(){for(;this.rootEl.firstChild;)this.rootEl.removeChild(this.rootEl.firstChild)}}const m2=()=>{let e=De([]),t=De([]),n=De({x:250,y:100,zoom:1});return{init:(r,o)=>{U(e,r),U(t,o)},getNodes:()=>c(e),setNodes:r=>{U(e,r)},getEdges:()=>c(t),setEdges:r=>{U(t,r)},getViewport:()=>c(n),setViewport:r=>{U(n,r)},getNode:r=>c(e).find(o=>o.id===r),addNode:r=>{U(e,[...c(e),r])},removeNode:r=>{U(e,c(e).filter(o=>o.id!==r))},updateNode:(r,o)=>{U(e,c(e).map(i=>i.id===r?{...i,...o}:i))},updateNodes:r=>{U(e,r(c(e)))},updateNodeData:(r,o)=>{U(e,c(e).map(i=>i.id===r?{...i,data:{...i.data,...o}}:i))},selectNodeOnly:r=>{U(e,c(e).map(o=>o.id===r?{...o,selected:!0}:{...o,selected:!1}))},getEdge:r=>c(t).find(o=>o.id===r),addEdge:r=>{U(t,[...c(t),r])},removeEdge:r=>{U(t,c(t).filter(o=>o.id!==r))},updateEdge:(r,o)=>{U(t,c(t).map(i=>i.id===r?{...i,...o}:i))},updateEdges:r=>{U(t,r(c(t)))},updateEdgeData:(r,o)=>{U(t,c(t).map(i=>i.id===r?{...i,data:{...i.data,...o}}:i))}}},Ue=m2();var y2=Q("<button><!></button>");function Me(e,t){de(t,!0);const n=y(t,"children",7),r=y(t,"primary",7),o=Ke(t,["$$slots","$$events","$$legacy","$$host","children","primary"]);var i={get children(){return n()},set children(l){n(l),m()},get primary(){return r()},set primary(l){r(l),m()}},s=y2();ut(s,()=>({type:"button",...o,class:`tf-btn ${r()?"tf-btn-primary":""} nopan nodrag ${t.class??""}`}));var a=I(s);return tt(a,()=>n()??ht),R(s),L(e,s),fe(i)}ue(Me,{children:{},primary:{}},[],[],!0);var w2=Q("<input/>");function Od(e,t){de(t,!0);const n=Ke(t,["$$slots","$$events","$$legacy","$$host"]);var r=w2();wn(r),ut(r,()=>({type:"checkbox",...n,class:`tf-checkbox nopan nodrag ${t.class??""}`})),L(e,r),fe()}ue(Od,{},[],[],!0);var b2=Q('<div><input type="hidden"/> <!> <!></div>');const x2={hash:"svelte-1swt2gg",code:".tf-chosen.svelte-1swt2gg {display:flex;flex-direction:row;align-items:center;justify-content:space-between;gap:5px;}"};function Ld(e,t){de(t,!0),qe(e,x2);const n=y(t,"placeholder",7),r=y(t,"label",7),o=y(t,"value",7),i=y(t,"buttonText",7,"选择..."),s=y(t,"onChosen",7),a=Ke(t,["$$slots","$$events","$$legacy","$$host","placeholder","label","value","buttonText","onChosen"]);var l={get placeholder(){return n()},set placeholder(g){n(g),m()},get label(){return r()},set label(g){r(g),m()},get value(){return o()},set value(g){o(g),m()},get buttonText(){return i()},set buttonText(g="选择..."){i(g),m()},get onChosen(){return s()},set onChosen(g){s(g),m()}},u=b2();ut(u,()=>({...a,class:`tf-chosen nopan nodrag ${t.class??""}`}),void 0,void 0,"svelte-1swt2gg");var d=I(u);wn(d);var p=V(d,2);ot(p,{get value(){return r()},get placeholder(){return n()},style:"flex-grow: 1;",disabled:!0});var f=V(p,2);return Me(f,{onclick:g=>{s()?.(o(),r(),g)},style:"padding: 3px",children:(g,h)=>{we();var v=Ee();Se(()=>Xe(v,i())),L(g,v)},$$slots:{default:!0}}),R(u),Se(()=>ci(d,o())),L(e,u),fe(l)}ue(Ld,{placeholder:{},label:{},value:{},buttonText:{},onChosen:{}},[],[],!0);var C2=Q("<input/>");function ot(e,t){de(t,!0);const n=Ke(t,["$$slots","$$events","$$legacy","$$host"]);var r=C2();wn(r),ut(r,()=>({type:"text",spellcheck:"false",...n,class:`tf-input  nopan nodrag ${t.class??""}`})),L(e,r),fe()}ue(ot,{},[],[],!0);var $2=Q("<textarea></textarea>");function We(e,t){de(t,!0);const n=y(t,"value",7),r=Ke(t,["$$slots","$$events","$$legacy","$$host","value"]);var o={get value(){return n()},set value(s){n(s),m()}},i=$2();return Np(i),ut(i,()=>({spellcheck:"false",...r,class:`tf-textarea nodrag nowheel ${t.class??""}`,value:n()||""})),L(e,i),fe(o)}ue(We,{value:{}},[],[],!0);var k2=Q('<div role="button"><!></div>'),_2=Q("<div></div>");function Md(e,t){const n=ru(t,["children","$$slots","$$events","$$legacy","$$host"]),r=ru(n,["items","onChange","activeIndex"]);de(t,!1);let o=y(t,"items",28,()=>[]),i=y(t,"onChange",12,()=>{}),s=y(t,"activeIndex",12,0);function a(d,p){s(p),i()?.(d,p)}var l={get items(){return o()},set items(d){o(d),m()},get onChange(){return i()},set onChange(d){i(d),m()},get activeIndex(){return s()},set activeIndex(d){s(d),m()}};tu();var u=_2();return ut(u,()=>({...r,class:`tf-tabs ${Cs(r),vt(()=>r.class)??""}`})),Ct(u,5,o,Rr,(d,p,f)=>{var g=k2();Ce(g,"tabindex",f),g.__click=()=>a(c(p),f),g.__keydown=b=>{(b.key==="Enter"||b.key===" ")&&(b.preventDefault(),a(c(p),f))};var h=I(g);{var v=b=>{var x=Ee();Se(()=>Xe(x,(c(p),vt(()=>c(p).label)))),L(b,x)},w=b=>{var x=Ne(),$=ae(x);tt($,()=>(c(p),vt(()=>c(p).label)??ht)),L(b,x)};ce(h,b=>{c(p),vt(()=>typeof c(p).label=="string")?b(v):b(w,!1)})}R(g),Se(()=>Mt(g,1,`tf-tabs-item ${f===s()?"active":""}`)),L(d,g)}),R(u),L(e,u),fe(l)}Mn(["click","keydown"]),ue(Md,{items:{},onChange:{},activeIndex:{}},[],[],!0);var S2=(e,t,n)=>t(c(n)),E2=(e,t,n)=>{(e.key==="Enter"||e.key===" ")&&(e.preventDefault(),t(c(n)))},P2=Q('<span class="tf-collapse-item-title-icon"><!></span>'),N2=Q('<div class="tf-collapse-item-description"><!></div>'),T2=Q('<div class="tf-collapse-item-content"><!></div>'),D2=Q('<div class="tf-collapse-item"><div class="tf-collapse-item-title" role="button"><!> <!> <span><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M13.1717 12.0007L8.22192 7.05093L9.63614 5.63672L16.0001 12.0007L9.63614 18.3646L8.22192 16.9504L13.1717 12.0007Z"></path></svg></span></div> <!> <!></div>'),O2=Q("<div></div>");const L2={hash:"svelte-1jfktzw",code:`
    /* 定义旋转的 CSS 类 */.rotate-90.svelte-1jfktzw {transform:rotate(90deg);transition:transform 0.3s ease;}`};function Hd(e,t){de(t,!0),qe(e,L2);let n=y(t,"items",7),r=y(t,"onChange",7),o=y(t,"activeKeys",31,()=>Yt([]));function i(l){o().includes(l.key)?o(o().filter(u=>u!==l.key)):(o().push(l.key),o(o())),r()?.(l,o())}var s={get items(){return n()},set items(l){n(l),m()},get onChange(){return r()},set onChange(l){r(l),m()},get activeKeys(){return o()},set activeKeys(l=[]){o(l),m()}},a=O2();return Ct(a,21,n,Rr,(l,u,d)=>{var p=D2(),f=I(p);Ce(f,"tabindex",d),f.__click=[S2,i,u],f.__keydown=[E2,i,u];var g=I(f);{var h=E=>{var D=P2(),O=I(D);er(O,{get target(){return c(u).icon}}),R(D),L(E,D)};ce(g,E=>{c(u).icon&&E(h)})}var v=V(g,2);er(v,{get target(){return c(u).title}});var w=V(v,2);R(f);var b=V(f,2);{var x=E=>{var D=N2(),O=I(D);er(O,{get target(){return c(u).description}}),R(D),L(E,D)};ce(b,E=>{c(u).description&&E(x)})}var $=V(b,2);{var S=E=>{var D=T2(),O=I(D);er(O,{get target(){return c(u).content}}),R(D),L(E,D)};ce($,E=>{o().includes(c(u).key)&&E(S)})}R(p),Se(E=>Mt(w,1,`tf-collapse-item-title-arrow ${E??""}`,"svelte-1jfktzw"),[()=>o().includes(c(u).key)?"rotate-90":""]),L(l,p)}),R(a),Se(()=>{mt(a,t.style),Mt(a,1,`tf-collapse ${t.class??""}`,"svelte-1jfktzw")}),L(e,a),fe(s)}Mn(["click","keydown"]),ue(Hd,{items:{},onChange:{},activeKeys:{}},[],[],!0);function er(e,t){de(t,!0);let n=y(t,"target",7);typeof n()>"u"&&n("undefined");var r={get target(){return n()},set target(l){n(l),m()}},o=Ne(),i=ae(o);{var s=l=>{var u=Ne(),d=ae(u);tt(d,()=>n()??ht),L(l,u)},a=l=>{var u=Ne(),d=ae(u);Ts(d,n),L(l,u)};ce(i,l=>{typeof n()=="function"?l(s):l(a,!1)})}return L(e,o),fe(r)}ue(er,{target:{}},[],[],!0);var M2=(e,t,n)=>t(c(n)),H2=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M12 14L8 10H16L12 14Z"></path></svg>'),V2=Q('<div class="tf-select-content-children"><!></div>'),z2=Q('<button class="tf-select-content-item"><span><!></span> <!></button> <!>',1),A2=Q('<div class="tf-select-content nopan nodrag"><!></div>'),R2=Q("<!> <!>",1),I2=Q('<div class="tf-select-input-placeholder"> </div>'),q2=Q('<button><div class="tf-select-input-value"></div> <div class="tf-select-input-arrow"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11.9999 13.1714L16.9497 8.22168L18.3639 9.63589L11.9999 15.9999L5.63599 9.63589L7.0502 8.22168L11.9999 13.1714Z"></path></svg></div></button>'),Z2=Q("<div><!></div>");function wt(e,t){de(t,!0);const n=(x,$=ht)=>{var S=Ne(),E=ae(S);Ct(E,19,$,(D,O)=>`${O}_${D.value}`,(D,O)=>{var q=z2(),K=ae(q);K.__click=[M2,h,O];var J=I(K),A=I(J);{var _=P=>{var H=H2();L(P,H)};ce(A,P=>{c(O).children&&c(O).children.length>0&&P(_)})}R(J);var k=V(J,2);er(k,{get target(){return c(O).label}}),R(K);var C=V(K,2);{var N=P=>{var H=V2(),Z=I(H);n(Z,()=>c(O).children),R(H),L(P,H)};ce(C,P=>{c(O).children&&c(O).children.length>0&&(a()||u().includes(c(O).value))&&P(N)})}L(D,q)}),L(x,S)};let r=y(t,"items",7),o=y(t,"onSelect",7),i=y(t,"value",23,()=>[]),s=y(t,"defaultValue",23,()=>[]),a=y(t,"expandAll",7,!0),l=y(t,"multiple",7,!1),u=y(t,"expandValue",23,()=>[]),d=y(t,"placeholder",7),p=Ke(t,["$$slots","$$events","$$legacy","$$host","items","onSelect","value","defaultValue","expandAll","multiple","expandValue","placeholder"]),f=T(()=>{const x=[],$=S=>{for(let E of S)i().length>0?i().includes(E.value)&&x.push(E):s().includes(E.value)&&x.push(E),E.children&&E.children.length>0&&$(E.children)};return $(r()),x}),g;function h(x){g?.hide(),o()?.(x)}var v={get items(){return r()},set items(x){r(x),m()},get onSelect(){return o()},set onSelect(x){o(x),m()},get value(){return i()},set value(x=[]){i(x),m()},get defaultValue(){return s()},set defaultValue(x=[]){s(x),m()},get expandAll(){return a()},set expandAll(x=!0){a(x),m()},get multiple(){return l()},set multiple(x=!1){l(x),m()},get expandValue(){return u()},set expandValue(x=[]){u(x),m()},get placeholder(){return d()},set placeholder(x){d(x),m()}},w=Z2();ut(w,()=>({...p,class:`tf-select ${p.class??""}`}));var b=I(w);return qt(kr(b,{floating:x=>{var $=A2(),S=I($);n(S,r),R($),L(x,$)},children:(x,$)=>{var S=q2();ut(S,()=>({class:"tf-select-input nopan nodrag",...p}));var E=I(S);Ct(E,23,()=>c(f),(D,O)=>`${O}_${D.value}`,(D,O,q)=>{var K=Ne(),J=ae(K);{var A=k=>{var C=Ne(),N=ae(C);{var P=H=>{er(H,{get target(){return c(O).label}})};ce(N,H=>{c(q)===0&&H(P)})}L(k,C)},_=k=>{var C=R2(),N=ae(C);er(N,{get target(){return c(O).label}});var P=V(N,2);{var H=Z=>{var Y=Ee(",");L(Z,Y)};ce(P,Z=>{c(q)<c(f).length-1&&Z(H)})}L(k,C)};ce(J,k=>{l()?k(_,!1):k(A)})}L(D,K)},D=>{var O=I2(),q=I(O,!0);R(O),Se(()=>Xe(q,d())),L(D,O)}),R(E),we(2),R(S),L(x,S)},$$slots:{floating:!0,default:!0}}),x=>g=x,()=>g),R(w),L(e,w),fe(v)}Mn(["click"]),ue(wt,{items:{},onSelect:{},value:{},defaultValue:{},expandAll:{},multiple:{},expandValue:{},placeholder:{}},[],[],!0);const qo=Math.min,Gr=Math.max,ji=Math.round,En=e=>({x:e,y:e}),B2={left:"right",right:"left",bottom:"top",top:"bottom"},K2={start:"end",end:"start"};function Ca(e,t,n){return Gr(e,qo(t,n))}function Zo(e,t){return typeof e=="function"?e(t):e}function $r(e){return e.split("-")[0]}function Bo(e){return e.split("-")[1]}function Vd(e){return e==="x"?"y":"x"}function $a(e){return e==="y"?"height":"width"}const j2=new Set(["top","bottom"]);function tr(e){return j2.has($r(e))?"y":"x"}function ka(e){return Vd(tr(e))}function Y2(e,t,n){n===void 0&&(n=!1);const r=Bo(e),o=ka(e),i=$a(o);let s=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[i]>t.floating[i]&&(s=Yi(s)),[s,Yi(s)]}function X2(e){const t=Yi(e);return[_a(e),t,_a(t)]}function _a(e){return e.replace(/start|end/g,t=>K2[t])}const zd=["left","right"],Ad=["right","left"],F2=["top","bottom"],W2=["bottom","top"];function G2(e,t,n){switch(e){case"top":case"bottom":return n?t?Ad:zd:t?zd:Ad;case"left":case"right":return t?F2:W2;default:return[]}}function U2(e,t,n,r){const o=Bo(e);let i=G2($r(e),n==="start",r);return o&&(i=i.map(s=>s+"-"+o),t&&(i=i.concat(i.map(_a)))),i}function Yi(e){return e.replace(/left|right|bottom|top/g,t=>B2[t])}function J2(e){return{top:0,right:0,bottom:0,left:0,...e}}function Rd(e){return typeof e!="number"?J2(e):{top:e,right:e,bottom:e,left:e}}function Xi(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function Id(e,t,n){let{reference:r,floating:o}=e;const i=tr(t),s=ka(t),a=$a(s),l=$r(t),u=i==="y",d=r.x+r.width/2-o.width/2,p=r.y+r.height/2-o.height/2,f=r[a]/2-o[a]/2;let g;switch(l){case"top":g={x:d,y:r.y-o.height};break;case"bottom":g={x:d,y:r.y+r.height};break;case"right":g={x:r.x+r.width,y:p};break;case"left":g={x:r.x-o.width,y:p};break;default:g={x:r.x,y:r.y}}switch(Bo(t)){case"start":g[s]-=f*(n&&u?-1:1);break;case"end":g[s]+=f*(n&&u?-1:1);break}return g}const Q2=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:s}=n,a=i.filter(Boolean),l=await(s.isRTL==null?void 0:s.isRTL(t));let u=await s.getElementRects({reference:e,floating:t,strategy:o}),{x:d,y:p}=Id(u,r,l),f=r,g={},h=0;for(let v=0;v<a.length;v++){const{name:w,fn:b}=a[v],{x,y:$,data:S,reset:E}=await b({x:d,y:p,initialPlacement:r,placement:f,strategy:o,middlewareData:g,rects:u,platform:s,elements:{reference:e,floating:t}});d=x??d,p=$??p,g={...g,[w]:{...g[w],...S}},E&&h<=50&&(h++,typeof E=="object"&&(E.placement&&(f=E.placement),E.rects&&(u=E.rects===!0?await s.getElementRects({reference:e,floating:t,strategy:o}):E.rects),{x:d,y:p}=Id(u,f,l)),v=-1)}return{x:d,y:p,placement:f,strategy:o,middlewareData:g}};async function qd(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:i,rects:s,elements:a,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:d="viewport",elementContext:p="floating",altBoundary:f=!1,padding:g=0}=Zo(t,e),h=Rd(g),v=a[f?p==="floating"?"reference":"floating":p],w=Xi(await i.getClippingRect({element:(n=await(i.isElement==null?void 0:i.isElement(v)))==null||n?v:v.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(a.floating)),boundary:u,rootBoundary:d,strategy:l})),b=p==="floating"?{x:r,y:o,width:s.floating.width,height:s.floating.height}:s.reference,x=await(i.getOffsetParent==null?void 0:i.getOffsetParent(a.floating)),$=await(i.isElement==null?void 0:i.isElement(x))?await(i.getScale==null?void 0:i.getScale(x))||{x:1,y:1}:{x:1,y:1},S=Xi(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:b,offsetParent:x,strategy:l}):b);return{top:(w.top-S.top+h.top)/$.y,bottom:(S.bottom-w.bottom+h.bottom)/$.y,left:(w.left-S.left+h.left)/$.x,right:(S.right-w.right+h.right)/$.x}}const ey=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:i,platform:s,elements:a,middlewareData:l}=t,{element:u,padding:d=0}=Zo(e,t)||{};if(u==null)return{};const p=Rd(d),f={x:n,y:r},g=ka(o),h=$a(g),v=await s.getDimensions(u),w=g==="y",b=w?"top":"left",x=w?"bottom":"right",$=w?"clientHeight":"clientWidth",S=i.reference[h]+i.reference[g]-f[g]-i.floating[h],E=f[g]-i.reference[g],D=await(s.getOffsetParent==null?void 0:s.getOffsetParent(u));let O=D?D[$]:0;(!O||!await(s.isElement==null?void 0:s.isElement(D)))&&(O=a.floating[$]||i.floating[h]);const q=S/2-E/2,K=O/2-v[h]/2-1,J=qo(p[b],K),A=qo(p[x],K),_=J,k=O-v[h]-A,C=O/2-v[h]/2+q,N=Ca(_,C,k),P=!l.arrow&&Bo(o)!=null&&C!==N&&i.reference[h]/2-(C<_?J:A)-v[h]/2<0,H=P?C<_?C-_:C-k:0;return{[g]:f[g]+H,data:{[g]:N,centerOffset:C-N-H,...P&&{alignmentOffset:H}},reset:P}}}),ty=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:i,rects:s,initialPlacement:a,platform:l,elements:u}=t,{mainAxis:d=!0,crossAxis:p=!0,fallbackPlacements:f,fallbackStrategy:g="bestFit",fallbackAxisSideDirection:h="none",flipAlignment:v=!0,...w}=Zo(e,t);if((n=i.arrow)!=null&&n.alignmentOffset)return{};const b=$r(o),x=tr(a),$=$r(a)===a,S=await(l.isRTL==null?void 0:l.isRTL(u.floating)),E=f||($||!v?[Yi(a)]:X2(a)),D=h!=="none";!f&&D&&E.push(...U2(a,v,h,S));const O=[a,...E],q=await qd(t,w),K=[];let J=((r=i.flip)==null?void 0:r.overflows)||[];if(d&&K.push(q[b]),p){const C=Y2(o,s,S);K.push(q[C[0]],q[C[1]])}if(J=[...J,{placement:o,overflows:K}],!K.every(C=>C<=0)){var A,_;const C=(((A=i.flip)==null?void 0:A.index)||0)+1,N=O[C];if(N&&(!(p==="alignment"&&x!==tr(N))||J.every(H=>tr(H.placement)===x?H.overflows[0]>0:!0)))return{data:{index:C,overflows:J},reset:{placement:N}};let P=(_=J.filter(H=>H.overflows[0]<=0).sort((H,Z)=>H.overflows[1]-Z.overflows[1])[0])==null?void 0:_.placement;if(!P)switch(g){case"bestFit":{var k;const H=(k=J.filter(Z=>{if(D){const Y=tr(Z.placement);return Y===x||Y==="y"}return!0}).map(Z=>[Z.placement,Z.overflows.filter(Y=>Y>0).reduce((Y,M)=>Y+M,0)]).sort((Z,Y)=>Z[1]-Y[1])[0])==null?void 0:k[0];H&&(P=H);break}case"initialPlacement":P=a;break}if(o!==P)return{reset:{placement:P}}}return{}}}},ny=new Set(["left","top"]);async function ry(e,t){const{placement:n,platform:r,elements:o}=e,i=await(r.isRTL==null?void 0:r.isRTL(o.floating)),s=$r(n),a=Bo(n),l=tr(n)==="y",u=ny.has(s)?-1:1,d=i&&l?-1:1,p=Zo(t,e);let{mainAxis:f,crossAxis:g,alignmentAxis:h}=typeof p=="number"?{mainAxis:p,crossAxis:0,alignmentAxis:null}:{mainAxis:p.mainAxis||0,crossAxis:p.crossAxis||0,alignmentAxis:p.alignmentAxis};return a&&typeof h=="number"&&(g=a==="end"?h*-1:h),l?{x:g*d,y:f*u}:{x:f*u,y:g*d}}const oy=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:i,placement:s,middlewareData:a}=t,l=await ry(t,e);return s===((n=a.offset)==null?void 0:n.placement)&&(r=a.arrow)!=null&&r.alignmentOffset?{}:{x:o+l.x,y:i+l.y,data:{...l,placement:s}}}}},iy=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:s=!1,limiter:a={fn:w=>{let{x:b,y:x}=w;return{x:b,y:x}}},...l}=Zo(e,t),u={x:n,y:r},d=await qd(t,l),p=tr($r(o)),f=Vd(p);let g=u[f],h=u[p];if(i){const w=f==="y"?"top":"left",b=f==="y"?"bottom":"right",x=g+d[w],$=g-d[b];g=Ca(x,g,$)}if(s){const w=p==="y"?"top":"left",b=p==="y"?"bottom":"right",x=h+d[w],$=h-d[b];h=Ca(x,h,$)}const v=a.fn({...t,[f]:g,[p]:h});return{...v,data:{x:v.x-n,y:v.y-r,enabled:{[f]:i,[p]:s}}}}}};function Fi(){return typeof window<"u"}function Ur(e){return Zd(e)?(e.nodeName||"").toLowerCase():"#document"}function Bt(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Bn(e){var t;return(t=(Zd(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Zd(e){return Fi()?e instanceof Node||e instanceof Bt(e).Node:!1}function un(e){return Fi()?e instanceof Element||e instanceof Bt(e).Element:!1}function Pn(e){return Fi()?e instanceof HTMLElement||e instanceof Bt(e).HTMLElement:!1}function Bd(e){return!Fi()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof Bt(e).ShadowRoot}const sy=new Set(["inline","contents"]);function Ko(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=cn(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!sy.has(o)}const ay=new Set(["table","td","th"]);function ly(e){return ay.has(Ur(e))}const uy=[":popover-open",":modal"];function Wi(e){return uy.some(t=>{try{return e.matches(t)}catch{return!1}})}const cy=["transform","translate","scale","rotate","perspective"],dy=["transform","translate","scale","rotate","perspective","filter"],fy=["paint","layout","strict","content"];function Sa(e){const t=Ea(),n=un(e)?cn(e):e;return cy.some(r=>n[r]?n[r]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||dy.some(r=>(n.willChange||"").includes(r))||fy.some(r=>(n.contain||"").includes(r))}function py(e){let t=nr(e);for(;Pn(t)&&!Jr(t);){if(Sa(t))return t;if(Wi(t))return null;t=nr(t)}return null}function Ea(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const gy=new Set(["html","body","#document"]);function Jr(e){return gy.has(Ur(e))}function cn(e){return Bt(e).getComputedStyle(e)}function Gi(e){return un(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function nr(e){if(Ur(e)==="html")return e;const t=e.assignedSlot||e.parentNode||Bd(e)&&e.host||Bn(e);return Bd(t)?t.host:t}function Kd(e){const t=nr(e);return Jr(t)?e.ownerDocument?e.ownerDocument.body:e.body:Pn(t)&&Ko(t)?t:Kd(t)}function jd(e,t,n){var r;t===void 0&&(t=[]);const o=Kd(e),i=o===((r=e.ownerDocument)==null?void 0:r.body),s=Bt(o);return i?(Pa(s),t.concat(s,s.visualViewport||[],Ko(o)?o:[],[])):t.concat(o,jd(o,[]))}function Pa(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Yd(e){const t=cn(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=Pn(e),i=o?e.offsetWidth:n,s=o?e.offsetHeight:r,a=ji(n)!==i||ji(r)!==s;return a&&(n=i,r=s),{width:n,height:r,$:a}}function Xd(e){return un(e)?e:e.contextElement}function Qr(e){const t=Xd(e);if(!Pn(t))return En(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:i}=Yd(t);let s=(i?ji(n.width):n.width)/r,a=(i?ji(n.height):n.height)/o;return(!s||!Number.isFinite(s))&&(s=1),(!a||!Number.isFinite(a))&&(a=1),{x:s,y:a}}const hy=En(0);function Fd(e){const t=Bt(e);return!Ea()||!t.visualViewport?hy:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function vy(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==Bt(e)?!1:t}function jo(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),i=Xd(e);let s=En(1);t&&(r?un(r)&&(s=Qr(r)):s=Qr(e));const a=vy(i,n,r)?Fd(i):En(0);let l=(o.left+a.x)/s.x,u=(o.top+a.y)/s.y,d=o.width/s.x,p=o.height/s.y;if(i){const f=Bt(i),g=r&&un(r)?Bt(r):r;let h=f,v=Pa(h);for(;v&&r&&g!==h;){const w=Qr(v),b=v.getBoundingClientRect(),x=cn(v),$=b.left+(v.clientLeft+parseFloat(x.paddingLeft))*w.x,S=b.top+(v.clientTop+parseFloat(x.paddingTop))*w.y;l*=w.x,u*=w.y,d*=w.x,p*=w.y,l+=$,u+=S,h=Bt(v),v=Pa(h)}}return Xi({width:d,height:p,x:l,y:u})}function Ui(e,t){const n=Gi(e).scrollLeft;return t?t.left+n:jo(Bn(e)).left+n}function Wd(e,t){const n=e.getBoundingClientRect(),r=n.left+t.scrollLeft-Ui(e,n),o=n.top+t.scrollTop;return{x:r,y:o}}function my(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const i=o==="fixed",s=Bn(r),a=t?Wi(t.floating):!1;if(r===s||a&&i)return n;let l={scrollLeft:0,scrollTop:0},u=En(1);const d=En(0),p=Pn(r);if((p||!p&&!i)&&((Ur(r)!=="body"||Ko(s))&&(l=Gi(r)),Pn(r))){const g=jo(r);u=Qr(r),d.x=g.x+r.clientLeft,d.y=g.y+r.clientTop}const f=s&&!p&&!i?Wd(s,l):En(0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-l.scrollLeft*u.x+d.x+f.x,y:n.y*u.y-l.scrollTop*u.y+d.y+f.y}}function yy(e){return Array.from(e.getClientRects())}function wy(e){const t=Bn(e),n=Gi(e),r=e.ownerDocument.body,o=Gr(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=Gr(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let s=-n.scrollLeft+Ui(e);const a=-n.scrollTop;return cn(r).direction==="rtl"&&(s+=Gr(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:s,y:a}}const Gd=25;function by(e,t){const n=Bt(e),r=Bn(e),o=n.visualViewport;let i=r.clientWidth,s=r.clientHeight,a=0,l=0;if(o){i=o.width,s=o.height;const d=Ea();(!d||d&&t==="fixed")&&(a=o.offsetLeft,l=o.offsetTop)}const u=Ui(r);if(u<=0){const d=r.ownerDocument,p=d.body,f=getComputedStyle(p),g=d.compatMode==="CSS1Compat"&&parseFloat(f.marginLeft)+parseFloat(f.marginRight)||0,h=Math.abs(r.clientWidth-p.clientWidth-g);h<=Gd&&(i-=h)}else u<=Gd&&(i+=u);return{width:i,height:s,x:a,y:l}}const xy=new Set(["absolute","fixed"]);function Cy(e,t){const n=jo(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=Pn(e)?Qr(e):En(1),s=e.clientWidth*i.x,a=e.clientHeight*i.y,l=o*i.x,u=r*i.y;return{width:s,height:a,x:l,y:u}}function Ud(e,t,n){let r;if(t==="viewport")r=by(e,n);else if(t==="document")r=wy(Bn(e));else if(un(t))r=Cy(t,n);else{const o=Fd(e);r={x:t.x-o.x,y:t.y-o.y,width:t.width,height:t.height}}return Xi(r)}function Jd(e,t){const n=nr(e);return n===t||!un(n)||Jr(n)?!1:cn(n).position==="fixed"||Jd(n,t)}function $y(e,t){const n=t.get(e);if(n)return n;let r=jd(e,[]).filter(a=>un(a)&&Ur(a)!=="body"),o=null;const i=cn(e).position==="fixed";let s=i?nr(e):e;for(;un(s)&&!Jr(s);){const a=cn(s),l=Sa(s);!l&&a.position==="fixed"&&(o=null),(i?!l&&!o:!l&&a.position==="static"&&o&&xy.has(o.position)||Ko(s)&&!l&&Jd(e,s))?r=r.filter(u=>u!==s):o=a,s=nr(s)}return t.set(e,r),r}function ky(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const i=[...n==="clippingAncestors"?Wi(t)?[]:$y(t,this._c):[].concat(n),r],s=i[0],a=i.reduce((l,u)=>{const d=Ud(t,u,o);return l.top=Gr(d.top,l.top),l.right=qo(d.right,l.right),l.bottom=qo(d.bottom,l.bottom),l.left=Gr(d.left,l.left),l},Ud(t,s,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}}function _y(e){const{width:t,height:n}=Yd(e);return{width:t,height:n}}function Sy(e,t,n){const r=Pn(t),o=Bn(t),i=n==="fixed",s=jo(e,!0,i,t);let a={scrollLeft:0,scrollTop:0};const l=En(0);function u(){l.x=Ui(o)}if(r||!r&&!i)if((Ur(t)!=="body"||Ko(o))&&(a=Gi(t)),r){const g=jo(t,!0,i,t);l.x=g.x+t.clientLeft,l.y=g.y+t.clientTop}else o&&u();i&&!r&&o&&u();const d=o&&!r&&!i?Wd(o,a):En(0),p=s.left+a.scrollLeft-l.x-d.x,f=s.top+a.scrollTop-l.y-d.y;return{x:p,y:f,width:s.width,height:s.height}}function Na(e){return cn(e).position==="static"}function Qd(e,t){if(!Pn(e)||cn(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return Bn(e)===n&&(n=n.ownerDocument.body),n}function ef(e,t){const n=Bt(e);if(Wi(e))return n;if(!Pn(e)){let o=nr(e);for(;o&&!Jr(o);){if(un(o)&&!Na(o))return o;o=nr(o)}return n}let r=Qd(e,t);for(;r&&ly(r)&&Na(r);)r=Qd(r,t);return r&&Jr(r)&&Na(r)&&!Sa(r)?n:r||py(e)||n}const Ey=async function(e){const t=this.getOffsetParent||ef,n=this.getDimensions,r=await n(e.floating);return{reference:Sy(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function Py(e){return cn(e).direction==="rtl"}const Ny={convertOffsetParentRelativeRectToViewportRelativeRect:my,getDocumentElement:Bn,getClippingRect:ky,getOffsetParent:ef,getElementRects:Ey,getClientRects:yy,getDimensions:_y,getScale:Qr,isElement:un,isRTL:Py},Ty=oy,Dy=iy,Oy=ty,Ly=ey,My=(e,t,n)=>{const r=new Map,o={platform:Ny,...n},i={...o.platform,_c:r};return Q2(e,t,{...o,platform:i})},Hy=({trigger:e,triggerEvent:t,floatContent:n,placement:r="bottom",offsetOptions:o,flipOptions:i,shiftOptions:s,interactive:a,showArrow:l})=>{if(typeof e=="string"){const b=document.querySelector(e);if(b)e=b;else throw new Error("element not found by document.querySelector('"+e+"')")}let u;if(typeof n=="string"){const b=document.querySelector(n);if(b)u=b;else throw new Error("element not found by document.querySelector('"+n+"')")}else u=n;let d;l&&(d=document.createElement("div"),d.style.position="absolute",d.style.backgroundColor="#222",d.style.width="8px",d.style.height="8px",d.style.transform="rotate(45deg)",d.style.display="none",u.firstElementChild.before(d));function p(){My(e,u,{placement:r,middleware:[Ty(o),Oy(i),Dy(s),...l?[Ly({element:d})]:[]]}).then(({x:b,y:x,placement:$,middlewareData:S})=>{if(Object.assign(u.style,{left:`${b}px`,top:`${x}px`}),l){const{x:E,y:D}=S.arrow,O={top:"bottom",right:"left",bottom:"top",left:"right"}[$.split("-")[0]];Object.assign(d.style,{zIndex:-1,left:E!=null?`${E}px`:"",top:D!=null?`${D}px`:"",right:"",bottom:"",[O]:"2px"})}})}let f=!1;function g(){u.style.display="block",u.style.visibility="block",u.style.position="absolute",l&&(d.style.display="block"),f=!0,p()}function h(){u.style.display="none",l&&(d.style.display="none"),f=!1}function v(b){b.stopPropagation(),f?h():g()}function w(b){u.contains(b.target)||h()}return(!t||t.length==0)&&(t=["click"]),t.forEach(b=>{e.addEventListener(b,v)}),document.addEventListener("click",w),{destroy(){t.forEach(b=>{e.removeEventListener(b,v)}),document.removeEventListener("click",w)},hide(){h()},isVisible(){return f}}};var Vy=Q('<div style="position: relative"><div><!></div> <div style="display: none; width: 100%;z-index: 9999"><!></div></div>');function kr(e,t){de(t,!0);const n=y(t,"children",7),r=y(t,"floating",7),o=y(t,"placement",7,"bottom");let i,s,a;Gn(()=>(a=Hy({trigger:i,floatContent:s,interactive:!0,placement:o()}),()=>{a.destroy()}));function l(){a.hide()}var u={hide:l,get children(){return n()},set children(v){n(v),m()},get floating(){return r()},set floating(v){r(v),m()},get placement(){return o()},set placement(v="bottom"){o(v),m()}},d=Vy(),p=I(d),f=I(p);tt(f,n),R(p),qt(p,v=>i=v,()=>i);var g=V(p,2),h=I(g);return tt(h,r),R(g),qt(g,v=>s=v,()=>s),R(d),L(e,d),fe(u)}ue(kr,{children:{},floating:{},placement:{}},[],["hide"],!0);function ze(e,t){de(t,!0);const n=y(t,"children",7),r=y(t,"level",7,1),o=y(t,"mt",7),i=y(t,"mb",7);var s={get children(){return n()},set children(u){n(u),m()},get level(){return r()},set level(u=1){r(u),m()},get mt(){return o()},set mt(u){o(u),m()},get mb(){return i()},set mb(u){i(u),m()}},a=Ne(),l=ae(a);return rg(l,()=>`h${r()}`,!1,(u,d)=>{ut(u,()=>({class:"tf-heading",style:`margin-top:${o()||"0"};margin-bottom:${i()||"0"}`}));var p=Ne(),f=ae(p);tt(f,()=>n()??ht),L(d,p)}),L(e,a),fe(s)}ue(ze,{children:{},level:{},mt:{},mb:{}},[],[],!0);var zy=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="svelte-1rvn4a8"><path d="M4.5 10.5C3.675 10.5 3 11.175 3 12C3 12.825 3.675 13.5 4.5 13.5C5.325 13.5 6 12.825 6 12C6 11.175 5.325 10.5 4.5 10.5ZM19.5 10.5C18.675 10.5 18 11.175 18 12C18 12.825 18.675 13.5 19.5 13.5C20.325 13.5 21 12.825 21 12C21 11.175 20.325 10.5 19.5 10.5ZM12 10.5C11.175 10.5 10.5 11.175 10.5 12C10.5 12.825 11.175 13.5 12 13.5C12.825 13.5 13.5 12.825 13.5 12C13.5 11.175 12.825 10.5 12 10.5Z" class="svelte-1rvn4a8"></path></svg>');const Ay={hash:"svelte-1rvn4a8",code:".input-btn-more {border:1px solid transparent;padding:3px;&:hover {background:#eee;border:1px solid transparent;}}"};function Yo(e,t){de(t,!0),qe(e,Ay);const n=Ke(t,["$$slots","$$events","$$legacy","$$host"]);Me(e,Fe(()=>n,{get class(){return`input-btn-more ${t.class??""}`},children:(r,o)=>{var i=zy();L(r,i)},$$slots:{default:!0}})),fe()}ue(Yo,{},[],[],!0);const Ry=()=>({deleteNode:e=>{Ue.removeNode(e),Ue.updateEdges(t=>t.filter(n=>n.source!==e&&n.target!==e))}}),_r=(e=16)=>{const t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",n=new Uint8Array(e);return crypto.getRandomValues(n),Array.from(n,r=>t[r%t.length]).join("")},Iy=()=>({copyNode:e=>{const t=Ue.getNode(e);if(t){const n=_r(),r={...t,id:n,position:{x:t.position.x+50,y:t.position.y+50}};Ue.updateNodes(o=>[...o.map(i=>({...i,selected:!1})),r])}}}),nt=()=>Xn("svelteflow__node_id"),rr=()=>Xn("tinyflow_options");var qy=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M17 6H22V8H20V21C20 21.5523 19.5523 22 19 22H5C4.44772 22 4 21.5523 4 21V8H2V6H7V3C7 2.44772 7.44772 2 8 2H16C16.5523 2 17 2.44772 17 3V6ZM18 8H6V20H18V8ZM9 11H11V17H9V11ZM13 11H15V17H13V11ZM9 4V6H15V4H9Z"></path></svg>'),Zy=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M6.9998 6V3C6.9998 2.44772 7.44752 2 7.9998 2H19.9998C20.5521 2 20.9998 2.44772 20.9998 3V17C20.9998 17.5523 20.5521 18 19.9998 18H16.9998V20.9991C16.9998 21.5519 16.5499 22 15.993 22H4.00666C3.45059 22 3 21.5554 3 20.9991L3.0026 7.00087C3.0027 6.44811 3.45264 6 4.00942 6H6.9998ZM5.00242 8L5.00019 20H14.9998V8H5.00242ZM8.9998 6H16.9998V16H18.9998V4H8.9998V6Z"></path></svg>'),By=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M8 18.3915V5.60846L18.2264 12L8 18.3915ZM6 3.80421V20.1957C6 20.9812 6.86395 21.46 7.53 21.0437L20.6432 12.848C21.2699 12.4563 21.2699 11.5436 20.6432 11.152L7.53 2.95621C6.86395 2.53993 6 3.01878 6 3.80421Z"></path></svg>'),Ky=Q('<div class="input-item svelte-1jesvb7">执行条件： <!></div>'),jy=(e,t,n)=>{const r=e.target.checked;t(n,{async:r})},Yy=(e,t,n)=>{const r=e.target.checked;t(n,{loopEnable:r})},Xy=Q('<div class="input-item svelte-1jesvb7">循环间隔时间（单位：毫秒）： <!></div> <div class="input-item svelte-1jesvb7">最大循环次数（0 表示不限制）： <!></div> <div class="input-item svelte-1jesvb7">退出条件： <!></div>',1),Fy=Q('<div class="settings svelte-1jesvb7"><div class="input-item svelte-1jesvb7">节点名称： <!></div> <div class="input-item svelte-1jesvb7">参数描述： <!></div> <!> <label class="input-item-inline svelte-1jesvb7"><span>异步执行：</span> <input type="checkbox"/></label> <label class="input-item-inline svelte-1jesvb7"><span>循环执行：</span> <input type="checkbox"/></label> <!></div>'),Wy=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M3.33946 17.0002C2.90721 16.2515 2.58277 15.4702 2.36133 14.6741C3.3338 14.1779 3.99972 13.1668 3.99972 12.0002C3.99972 10.8345 3.3348 9.824 2.36353 9.32741C2.81025 7.71651 3.65857 6.21627 4.86474 4.99001C5.7807 5.58416 6.98935 5.65534 7.99972 5.072C9.01009 4.48866 9.55277 3.40635 9.4962 2.31604C11.1613 1.8846 12.8847 1.90004 14.5031 2.31862C14.4475 3.40806 14.9901 4.48912 15.9997 5.072C17.0101 5.65532 18.2187 5.58416 19.1346 4.99007C19.7133 5.57986 20.2277 6.25151 20.66 7.00021C21.0922 7.7489 21.4167 8.53025 21.6381 9.32628C20.6656 9.82247 19.9997 10.8336 19.9997 12.0002C19.9997 13.166 20.6646 14.1764 21.6359 14.673C21.1892 16.2839 20.3409 17.7841 19.1347 19.0104C18.2187 18.4163 17.0101 18.3451 15.9997 18.9284C14.9893 19.5117 14.4467 20.5941 14.5032 21.6844C12.8382 22.1158 11.1148 22.1004 9.49633 21.6818C9.55191 20.5923 9.00929 19.5113 7.99972 18.9284C6.98938 18.3451 5.78079 18.4162 4.86484 19.0103C4.28617 18.4205 3.77172 17.7489 3.33946 17.0002ZM8.99972 17.1964C10.0911 17.8265 10.8749 18.8227 11.2503 19.9659C11.7486 20.0133 12.2502 20.014 12.7486 19.9675C13.1238 18.8237 13.9078 17.8268 14.9997 17.1964C16.0916 16.5659 17.347 16.3855 18.5252 16.6324C18.8146 16.224 19.0648 15.7892 19.2729 15.334C18.4706 14.4373 17.9997 13.2604 17.9997 12.0002C17.9997 10.74 18.4706 9.5632 19.2729 8.6665C19.1688 8.4405 19.0538 8.21822 18.9279 8.00021C18.802 7.78219 18.667 7.57148 18.5233 7.36842C17.3457 7.61476 16.0911 7.43414 14.9997 6.80405C13.9083 6.17395 13.1246 5.17768 12.7491 4.03455C12.2509 3.98714 11.7492 3.98646 11.2509 4.03292C10.8756 5.17671 10.0916 6.17364 8.99972 6.80405C7.9078 7.43447 6.65245 7.61494 5.47428 7.36803C5.18485 7.77641 4.93463 8.21117 4.72656 8.66637C5.52881 9.56311 5.99972 10.74 5.99972 12.0002C5.99972 13.2604 5.52883 14.4372 4.72656 15.3339C4.83067 15.5599 4.94564 15.7822 5.07152 16.0002C5.19739 16.2182 5.3324 16.4289 5.47612 16.632C6.65377 16.3857 7.90838 16.5663 8.99972 17.1964ZM11.9997 15.0002C10.3429 15.0002 8.99972 13.6571 8.99972 12.0002C8.99972 10.3434 10.3429 9.00021 11.9997 9.00021C13.6566 9.00021 14.9997 10.3434 14.9997 12.0002C14.9997 13.6571 13.6566 15.0002 11.9997 15.0002ZM11.9997 13.0002C12.552 13.0002 12.9997 12.5525 12.9997 12.0002C12.9997 11.4479 12.552 11.0002 11.9997 11.0002C11.4474 11.0002 10.9997 11.4479 10.9997 12.0002C10.9997 12.5525 11.4474 13.0002 11.9997 13.0002Z"></path></svg>'),Gy=Q('<div class="tf-node-toolbar svelte-1jesvb7"><!> <!> <!> <!></div>'),Uy=Q('<!> <div class="tf-node-wrapper"><div class="tf-node-wrapper-title">TinyFlow.ai</div> <div class="tf-node-wrapper-body"><!></div></div> <!> <!> <!>',1);const Jy={hash:"svelte-1jesvb7",code:".tf-node-toolbar.svelte-1jesvb7 {display:flex;gap:5px;padding:5px;border-radius:5px;background:#fff;border:1px solid #eee;box-shadow:0 0 5px rgba(0, 0, 0, 0.1);}.tf-node-toolbar-item {border:1px solid transparent;}.settings.svelte-1jesvb7 {display:flex;flex-direction:column;gap:10px;padding:10px;background:#fff;border:1px solid #ddd;border-radius:5px;width:200px;box-shadow:0 0 10px 2px rgba(0, 0, 0, 0.1);}.settings.svelte-1jesvb7 .input-item:where(.svelte-1jesvb7) {display:flex;flex-direction:column;gap:3px;font-size:12px;color:#666;}.settings.svelte-1jesvb7 .input-item-inline:where(.svelte-1jesvb7) {display:flex;align-items:center;font-size:12px;color:#666;}"};function Wt(e,t){de(t,!0),qe(e,Jy);const n=y(t,"data",7),r=y(t,"id",7,""),o=y(t,"icon",7),i=y(t,"handle",7),s=y(t,"children",7),a=y(t,"allowExecute",7,!0),l=y(t,"allowCopy",7,!0),u=y(t,"allowDelete",7,!0),d=y(t,"allowSetting",7,!0),p=y(t,"allowSettingOfCondition",7,!0),f=y(t,"showSourceHandle",7,!0),g=y(t,"showTargetHandle",7,!0),h=y(t,"onCollapse",7);let v=n().expand?["key"]:[];const{updateNodeData:w,getNode:b}=yt(),x=T(()=>[{key:"key",icon:o(),title:n().title,description:n().description,content:s()}]),{deleteNode:$}=Ry(),{copyNode:S}=Iy(),E=rr(),D=()=>{E.onNodeExecute?.(b(r()))};let O=nt();var q={get data(){return n()},set data(M){n(M),m()},get id(){return r()},set id(M=""){r(M),m()},get icon(){return o()},set icon(M){o(M),m()},get handle(){return i()},set handle(M){i(M),m()},get children(){return s()},set children(M){s(M),m()},get allowExecute(){return a()},set allowExecute(M=!0){a(M),m()},get allowCopy(){return l()},set allowCopy(M=!0){l(M),m()},get allowDelete(){return u()},set allowDelete(M=!0){u(M),m()},get allowSetting(){return d()},set allowSetting(M=!0){d(M),m()},get allowSettingOfCondition(){return p()},set allowSettingOfCondition(M=!0){p(M),m()},get showSourceHandle(){return f()},set showSourceHandle(M=!0){f(M),m()},get showTargetHandle(){return g()},set showTargetHandle(M=!0){g(M),m()},get onCollapse(){return h()},set onCollapse(M){h(M),m()}},K=Uy(),J=ae(K);{var A=M=>{Td(M,{get position(){return be.Top},align:"start",children:(X,te)=>{var oe=Gy(),j=I(oe);{var G=ee=>{Me(ee,{class:"tf-node-toolbar-item",onclick:()=>{$(r())},children:(re,ge)=>{var he=qy();L(re,he)},$$slots:{default:!0}})};ce(j,ee=>{u()&&ee(G)})}var F=V(j,2);{var se=ee=>{Me(ee,{class:"tf-node-toolbar-item",onclick:()=>{S(r())},children:(re,ge)=>{var he=Zy();L(re,he)},$$slots:{default:!0}})};ce(F,ee=>{l()&&ee(se)})}var W=V(F,2);{var ye=ee=>{Me(ee,{class:"tf-node-toolbar-item",onclick:D,children:(re,ge)=>{var he=By();L(re,he)},$$slots:{default:!0}})};ce(W,ee=>{a()&&ee(ye)})}var xe=V(W,2);{var ie=ee=>{kr(ee,{placement:"bottom",floating:re=>{var ge=Fy(),he=I(ge),le=V(I(he));ot(le,{style:"width: 100%;",onchange:Be=>{const Qe=Be.target.value;w(O,{title:Qe})},get value(){return n().title}}),R(he);var Te=V(he,2),ke=V(I(Te));We(ke,{rows:3,style:"width: 100%;",onchange:Be=>{const Qe=Be.target.value;w(O,{description:Qe})},get value(){return n().description}}),R(Te);var B=V(Te,2);{var ct=Be=>{var Qe=Ky(),ve=V(I(Qe));We(ve,{rows:2,style:"width: 100%;",onchange:Ye=>{const ft=Ye.target.value;w(O,{condition:ft})},get value(){return n().condition}}),R(Qe),L(Be,Qe)};ce(B,Be=>{p()&&Be(ct)})}var Ae=V(B,2),Ze=V(I(Ae),2);wn(Ze),Ze.__change=[jy,w,O],R(Ae);var Re=V(Ae,2),dt=V(I(Re),2);wn(dt),dt.__change=[Yy,w,O],R(Re);var it=V(Re,2);{var Pt=Be=>{var Qe=Xy(),ve=ae(Qe),Ye=V(I(ve));{let kt=T(()=>n().loopIntervalMs||"1000");We(Ye,{rows:1,style:"width: 100%;",onchange:Gt=>{const Nn=Gt.target.value;w(O,{loopIntervalMs:Nn})},get value(){return c(kt)}})}R(ve);var ft=V(ve,2),st=V(I(ft));{let kt=T(()=>n().maxLoopCount||"0");We(st,{rows:1,style:"width: 100%;",onchange:Gt=>{const Nn=Gt.target.value;w(O,{maxLoopCount:Nn})},get value(){return c(kt)}})}R(ft);var Nt=V(ft,2),Je=V(I(Nt));We(Je,{rows:2,style:"width: 100%;",onchange:kt=>{const Gt=kt.target.value;w(O,{loopBreakCondition:Gt})},get value(){return n().loopBreakCondition}}),R(Nt),L(Be,Qe)};ce(it,Be=>{n().loopEnable&&Be(Pt)})}R(ge),Se(()=>{Hs(Ze,!!n().async),Hs(dt,!!n().loopEnable)}),L(re,ge)},children:(re,ge)=>{Me(re,{class:"tf-node-toolbar-item",children:(he,le)=>{var Te=Wy();L(he,Te)},$$slots:{default:!0}})},$$slots:{floating:!0,default:!0}})};ce(xe,ee=>{d()&&ee(ie)})}R(oe),L(X,oe)},$$slots:{default:!0}})};ce(J,M=>{(a()||l()||u())&&M(A)})}var _=V(J,2),k=V(I(_),2),C=I(k);Hd(C,{get items(){return c(x)},get activeKeys(){return v},onChange:(M,X)=>{w(r(),{expand:X?.includes("key")}),h()?.(X?.includes("key")?"key":"")}}),R(k),R(_);var N=V(_,2);{var P=M=>{Qn(M,{type:"target",get position(){return be.Left},style:" left: -12px;top: 20px"})};ce(N,M=>{g()&&M(P)})}var H=V(N,2);{var Z=M=>{Qn(M,{type:"source",get position(){return be.Right},style:"right: -12px;top: 20px"})};ce(H,M=>{f()&&M(Z)})}var Y=V(H,2);return tt(Y,()=>i()??ht),L(e,K),fe(q)}Mn(["change"]),ue(Wt,{data:{},id:{},icon:{},handle:{},children:{},allowExecute:{},allowCopy:{},allowDelete:{},allowSetting:{},allowSettingOfCondition:{},showSourceHandle:{},showTargetHandle:{},onCollapse:{}},[],[],!0);var Qy=Q('<div class="input-more-item svelte-2f9bnc">数据选项： <!></div>'),ew=Q('<div class="input-more-setting svelte-2f9bnc"><div class="input-more-item svelte-2f9bnc">数据内容： <!></div> <div class="input-more-item svelte-2f9bnc">输入方式： <!></div> <!> <div class="input-more-item svelte-2f9bnc">数据标题： <!></div> <div class="input-more-item svelte-2f9bnc">数据描述： <!></div> <div class="input-more-item svelte-2f9bnc">占位符： <!></div> <div class="input-more-item svelte-2f9bnc"><!></div></div>'),tw=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M4.5 10.5C3.675 10.5 3 11.175 3 12C3 12.825 3.675 13.5 4.5 13.5C5.325 13.5 6 12.825 6 12C6 11.175 5.325 10.5 4.5 10.5ZM19.5 10.5C18.675 10.5 18 11.175 18 12C18 12.825 18.675 13.5 19.5 13.5C20.325 13.5 21 12.825 21 12C21 11.175 20.325 10.5 19.5 10.5ZM12 10.5C11.175 10.5 10.5 11.175 10.5 12C10.5 12.825 11.175 13.5 12 13.5C12.825 13.5 13.5 12.825 13.5 12C13.5 11.175 12.825 10.5 12 10.5Z"></path></svg>'),nw=Q('<div class="input-item svelte-2f9bnc"><!></div> <div class="input-item svelte-2f9bnc"><!></div> <div class="input-item svelte-2f9bnc"><!></div>',1);const rw={hash:"svelte-2f9bnc",code:".input-item.svelte-2f9bnc {display:flex;align-items:center;}.input-more-setting.svelte-2f9bnc {display:flex;flex-direction:column;gap:10px;padding:10px;background:#fff;border:1px solid #ddd;border-radius:5px;width:200px;box-shadow:0 0 10px 2px rgba(0, 0, 0, 0.1);}.input-more-setting.svelte-2f9bnc .input-more-item:where(.svelte-2f9bnc) {display:flex;flex-direction:column;gap:3px;font-size:12px;color:#666;}"};function tf(e,t){de(t,!0),qe(e,rw);const n=y(t,"parameter",7),r=y(t,"index",7);let o=nt(),i=Zn(o),s=T(()=>({...n(),...i?.current?.data?.parameters[r()]}));const{updateNodeData:a}=yt(),l=(q,K)=>{a(o,J=>{let A=J.data.parameters;return A[r()][q]=K,{parameters:A}})},u=(q,K)=>{const J=K.target.value;l(q,J)},d=q=>{const K=q.target.value;l("name",K)},p=q=>{const K=q.target.checked;l("required",K)},f=q=>{const K=q.value;l("formType",K)},g=q=>{const K=q.value;l("contentType",K)};let h;const v=()=>{a(o,q=>{let K=q.data.parameters;return K.splice(r(),1),{parameters:[...K]}}),h?.hide()};var w={get parameter(){return n()},set parameter(q){n(q),m()},get index(){return r()},set index(q){r(q),m()}},b=nw(),x=ae(b),$=I(x);ot($,{style:"width: 100%;",get value(){return c(s).name},placeholder:"请输入参数名称",oninput:d}),R(x);var S=V(x,2),E=I(S);Od(E,{get checked(){return c(s).required},onchange:p}),R(S);var D=V(S,2),O=I(D);return qt(kr(O,{placement:"bottom",floating:q=>{var K=ew(),J=I(K),A=V(I(J));{let j=T(()=>c(s).contentType?[c(s).contentType]:[]);wt(A,{get items(){return xa},style:"width: 100%",defaultValue:["text"],get value(){return c(j)},onSelect:g})}R(J);var _=V(J,2),k=V(I(_));{let j=T(()=>c(s).formType?[c(s).formType]:[]);wt(k,{get items(){return g2},style:"width: 100%",defaultValue:["input"],get value(){return c(j)},onSelect:f})}R(_);var C=V(_,2);{var N=j=>{var G=Qy(),F=V(I(G));{let se=T(()=>c(s).enums?.join(`
`));We(F,{rows:3,style:"width: 100%;",onchange:W=>{l("enums",W.target?.value.trim().split(`
`))},get value(){return c(se)},placeholder:"一行一个选项"})}R(G),L(j,G)};ce(C,j=>{(c(s).formType==="radio"||c(s).formType==="checkbox"||c(s).formType==="select")&&j(N)})}var P=V(C,2),H=V(I(P));We(H,{rows:1,style:"width: 100%;",onchange:j=>{u("formLabel",j)},get value(){return c(s).formLabel}}),R(P);var Z=V(P,2),Y=V(I(Z));We(Y,{rows:2,style:"width: 100%;",onchange:j=>{u("formDescription",j)},get value(){return c(s).formDescription}}),R(Z);var M=V(Z,2),X=V(I(M));We(X,{rows:2,style:"width: 100%;",onchange:j=>{u("formPlaceholder",j)},get value(){return c(s).formPlaceholder}}),R(M);var te=V(M,2),oe=I(te);Me(oe,{onclick:v,children:(j,G)=>{we();var F=Ee("删除");L(j,F)},$$slots:{default:!0}}),R(te),R(K),L(q,K)},children:(q,K)=>{Me(q,{class:"input-btn-more",children:(J,A)=>{var _=tw();L(J,_)},$$slots:{default:!0}})},$$slots:{floating:!0,default:!0}}),q=>h=q,()=>h),R(D),L(e,b),fe(w)}ue(tf,{parameter:{},index:{}},[],[],!0);var ow=Q('<div class="input-header svelte-3n0wca">参数名称</div> <div class="input-header svelte-3n0wca">必填</div> <div class="input-header svelte-3n0wca"></div>',1),iw=Q('<div class="none-params svelte-3n0wca">无输入参数</div>'),sw=Q('<div class="input-container svelte-3n0wca"><!> <!></div>');const aw={hash:"svelte-3n0wca",code:`.input-container.svelte-3n0wca {display:grid;grid-template-columns:80% 10% 10%;row-gap:5px;column-gap:3px;}.input-container.svelte-3n0wca .none-params:where(.svelte-3n0wca) {font-size:12px;background:#f8f8f8;height:40px;display:flex;justify-content:center;align-items:center;border-radius:5px;width:calc(100% - 5px);grid-column:1 / -1;
  /* 从第一列开始到最后一列结束 */}.input-container.svelte-3n0wca .input-header:where(.svelte-3n0wca) {font-size:12px;color:#666;}`};function nf(e,t){de(t,!0),qe(e,aw);let n=nt(),r=Zn(n),o=T(()=>[...r?.current?.data?.parameters||[]]);var i=sw(),s=I(i);{var a=u=>{var d=ow();we(4),L(u,d)};ce(s,u=>{c(o).length!==0&&u(a)})}var l=V(s,2);Ct(l,19,()=>c(o),u=>u.id,(u,d,p)=>{tf(u,{get parameter(){return c(d)},get index(){return c(p)}})},u=>{var d=iw();L(u,d)}),R(i),L(e,i),fe()}ue(nf,{},[],[],!0);const Xo=e=>(!e||e.length==0||e.forEach(t=>{t.id||(t.id=_r()),Xo(t.children)}),e),dn=()=>{const{updateNodeData:e}=yt();return{addParameter:(t,n="parameters",r)=>{Array.isArray(r)?r.forEach(s=>Xo(s?.children)):Xo(r?.children);function o(s){return{name:"",dataType:"String",refType:"ref",...s,id:_r()}}const i=[];Array.isArray(r)?i.push(...r.map(o)):i.push(o(r)),e(t,s=>{let a=s.data[n];return a?a.push(...i):a=[...i],{[n]:[...a]}})}}};var lw=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22ZM12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4C7.58172 4 4 7.58172 4 12C4 16.4183 7.58172 20 12 20ZM12 15C10.3431 15 9 13.6569 9 12C9 10.3431 10.3431 9 12 9C13.6569 9 15 10.3431 15 12C15 13.6569 13.6569 15 12 15Z"></path></svg>'),uw=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),cw=Q('<div class="heading svelte-r5g35l"><!> <!></div> <!>',1);const dw={hash:"svelte-r5g35l",code:".heading.svelte-r5g35l {display:flex;margin-bottom:10px;}.input-btn-more {border:1px solid transparent;padding:3px;}.input-btn-more:hover {background:#eee;border:1px solid transparent;}"};function rf(e,t){de(t,!0),qe(e,dw);const n=y(t,"data",7),r=Ke(t,["$$slots","$$events","$$legacy","$$host","data"]),o=nt(),{addParameter:i}=dn();var s={get data(){return n()},set data(a){n(a),m()}};return Wt(e,Fe(()=>r,{get data(){return n()},allowExecute:!1,showTargetHandle:!1,allowSettingOfCondition:!1,icon:a=>{var l=lw();L(a,l)},children:(a,l)=>{var u=cw(),d=ae(u),p=I(d);ze(p,{level:3,children:(h,v)=>{we();var w=Ee("输入参数");L(h,w)},$$slots:{default:!0}});var f=V(p,2);Me(f,{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{i(o,"parameters",{refType:"input"})},children:(h,v)=>{var w=uw();L(h,w)},$$slots:{default:!0}}),R(d);var g=V(d,2);nf(g,{}),L(a,u)},$$slots:{icon:!0,default:!0}})),fe(s)}ue(rf,{data:{}},[],[],!0);const of=(e,t,n)=>{for(const r of n)r.target===t&&r.source&&(e.push(r.source),of(e,r.source,n))},sf=(e,t,n)=>!e||e.length===0?[]:e.map(r=>({label:r.name+(n?` (Array<${r.dataType||"String"}>)`:` (${r.dataType||"String"})`),value:t+"."+r.name,children:sf(r.children,t+"."+r.name,n)})),af=(e,t,n)=>{if(e.type==="startNode"){const r=e.data.parameters,o=[];if(r)for(const i of r)o.push({label:i.name+(t?` (Array<${i.dataType||"String"}>)`:` (${i.dataType||"String"})`),value:e.id+"."+i.name});return{label:e.data.title,value:e.id,children:o}}else{if(e.type==="loopNode"&&n.parentId)return{label:e.data.title,value:e.id,children:[{label:"loopItem",value:e.id+".loopItem"},{label:"index (Number)",value:e.id+".index"}]};{const r=e.data.outputDefs;if(r)return{label:e.data.title,value:e.id,children:sf(r,e.id,t)}}}},lf=(e=!1)=>{const t=nt(),n=Zn(t),r=T(ln),o=T(()=>c(r).nodes),i=T(()=>c(r).edges),s=T(()=>c(r).nodeLookup);let a=T(()=>{const l=[];if(!n.current)return[];const u=c(s).get(t);if(e)for(const d of c(o)){const p=d.parentId===n.current.id;if(p){const f=af(d,p,u);f&&l.push(f)}}else{const d=[];of(d,t,c(i));for(const p of c(o))if(d.includes(p.id)){const f=p.parentId===n.current.id,g=af(p,f,u);g&&l.push(g)}}return l});return{get current(){return c(a)}}};var fw=Q('<div class="input-more-item svelte-laou7w">数据内容： <!></div>'),pw=Q('<div class="input-more-setting svelte-laou7w"><div class="input-more-item svelte-laou7w">数据来源： <!></div> <!> <div class="input-more-item svelte-laou7w">默认值： <!></div> <div class="input-more-item svelte-laou7w">参数描述： <!></div> <div class="input-more-item svelte-laou7w"><!></div></div>'),gw=Q('<div class="input-item svelte-laou7w"><!></div> <div class="input-item svelte-laou7w"><!></div> <div class="input-item svelte-laou7w"><!></div>',1);const hw={hash:"svelte-laou7w",code:".input-item.svelte-laou7w {display:flex;align-items:center;}.input-more-setting.svelte-laou7w {display:flex;flex-direction:column;gap:10px;padding:10px;background:#fff;border:1px solid #ddd;border-radius:5px;width:200px;box-shadow:0 0 10px 2px rgba(0, 0, 0, 0.1);}.input-more-setting.svelte-laou7w .input-more-item:where(.svelte-laou7w) {display:flex;flex-direction:column;gap:3px;font-size:12px;color:#666;}"};function uf(e,t){de(t,!0),qe(e,hw),Gn(()=>{c(u).refType||h({value:"ref"})});const n=y(t,"parameter",7),r=y(t,"index",7),o=y(t,"dataKeyName",7),i=y(t,"useChildrenOnly",7),s=y(t,"showContentType",7,!1);let a=nt(),l=Zn(a),u=T(()=>({...n(),...l?.current?.data?.[o()][r()]}));const{updateNodeData:d}=yt(),p=(k,C)=>{d(a,N=>{let P=N.data?.[o()];return P[r()]={...P[r()],[k]:C},{[o()]:P}})},f=(k,C)=>{const N=C.target.value;p(k,N)},g=k=>{const C=k.value;p("ref",C)},h=k=>{const C=k.value;p("refType",C)},v=k=>{const C=k.value;p("contentType",C)};let w;const b=()=>{d(a,k=>{let C=k.data?.[o()];return C.splice(r(),1),{[o()]:[...C]}}),w?.hide()};let x=lf(i());var $={get parameter(){return n()},set parameter(k){n(k),m()},get index(){return r()},set index(k){r(k),m()},get dataKeyName(){return o()},set dataKeyName(k){o(k),m()},get useChildrenOnly(){return i()},set useChildrenOnly(k){i(k),m()},get showContentType(){return s()},set showContentType(k=!1){s(k),m()}},S=gw(),E=ae(S),D=I(E);{let k=T(()=>c(u).nameDisabled===!0);ot(D,{style:"width: 100%;",get value(){return c(u).name},placeholder:"请输入参数名称",get disabled(){return c(k)},oninput:C=>f("name",C)})}R(E);var O=V(E,2),q=I(O);{var K=k=>{ot(k,{get value(){return c(u).value},placeholder:"请输入参数值",oninput:C=>f("value",C)})},J=k=>{var C=Ne(),N=ae(C);{var P=H=>{{let Z=T(()=>[c(u).ref]);wt(H,{get items(){return x.current},style:"width: 100%",defaultValue:["ref"],get value(){return c(Z)},expandAll:!0,onSelect:g})}};ce(N,H=>{c(u).refType!=="input"&&H(P)},!0)}L(k,C)};ce(q,k=>{c(u).refType==="fixed"?k(K):k(J,!1)})}R(O);var A=V(O,2),_=I(A);return qt(kr(_,{placement:"bottom",floating:k=>{var C=pw(),N=I(C),P=V(I(N));{let G=T(()=>c(u).refType?[c(u).refType]:[]);wt(P,{get items(){return p2},style:"width: 100%",defaultValue:["ref"],get value(){return c(G)},onSelect:h})}R(N);var H=V(N,2);{var Z=G=>{var F=fw(),se=V(I(F));{let W=T(()=>c(u).contentType?[c(u).contentType]:[]);wt(se,{get items(){return xa},style:"width: 100%",defaultValue:["text"],get value(){return c(W)},onSelect:v})}R(F),L(G,F)};ce(H,G=>{s()&&G(Z)})}var Y=V(H,2),M=V(I(Y));We(M,{rows:1,style:"width: 100%;",onchange:G=>{f("defaultValue",G)},get value(){return c(u).defaultValue}}),R(Y);var X=V(Y,2),te=V(I(X));We(te,{rows:3,style:"width: 100%;",onchange:G=>{f("description",G)},get value(){return c(u).description}}),R(X);var oe=V(X,2),j=I(oe);Me(j,{onclick:b,children:(G,F)=>{we();var se=Ee("删除");L(G,se)},$$slots:{default:!0}}),R(oe),R(C),L(k,C)},children:(k,C)=>{Yo(k,{})},$$slots:{floating:!0,default:!0}}),k=>w=k,()=>w),R(A),L(e,S),fe($)}ue(uf,{parameter:{},index:{},dataKeyName:{},useChildrenOnly:{},showContentType:{}},[],[],!0);var vw=Q('<div class="input-header svelte-1sm1mgi">参数名称</div> <div class="input-header svelte-1sm1mgi">参数值</div> <div class="input-header svelte-1sm1mgi"></div>',1),mw=Q('<div class="none-params svelte-1sm1mgi"> </div>'),yw=Q('<div class="input-container svelte-1sm1mgi"><!> <!></div>');const ww={hash:"svelte-1sm1mgi",code:`.input-container.svelte-1sm1mgi {display:grid;grid-template-columns:40% 50% 10%;row-gap:5px;column-gap:3px;}.input-container.svelte-1sm1mgi .none-params:where(.svelte-1sm1mgi) {font-size:12px;background:#f8f8f8;height:40px;display:flex;justify-content:center;align-items:center;border-radius:5px;width:calc(100% - 5px);grid-column:1 / -1;
  /* 从第一列开始到最后一列结束 */}.input-container.svelte-1sm1mgi .input-header:where(.svelte-1sm1mgi) {font-size:12px;color:#666;}`};function Vt(e,t){de(t,!0),qe(e,ww);const n=y(t,"noneParameterText",7,"无输入参数"),r=y(t,"dataKeyName",7,"parameters"),o=y(t,"useChildrenOnly",7),i=y(t,"showContentType",7,!1);let s=nt(),a=Zn(s),l=T(()=>[...a?.current?.data?.[r()]||[]]);var u={get noneParameterText(){return n()},set noneParameterText(h="无输入参数"){n(h),m()},get dataKeyName(){return r()},set dataKeyName(h="parameters"){r(h),m()},get useChildrenOnly(){return o()},set useChildrenOnly(h){o(h),m()},get showContentType(){return i()},set showContentType(h=!1){i(h),m()}},d=yw(),p=I(d);{var f=h=>{var v=vw();we(4),L(h,v)};ce(p,h=>{c(l).length!==0&&h(f)})}var g=V(p,2);return Ct(g,19,()=>c(l),h=>h.id,(h,v,w)=>{uf(h,{get parameter(){return c(v)},get index(){return c(w)},get dataKeyName(){return r()},get useChildrenOnly(){return o()},get showContentType(){return i()}})},h=>{var v=mw(),w=I(v,!0);R(v),Se(()=>Xe(w,n())),L(h,v)}),R(d),L(e,d),fe(u)}ue(Vt,{noneParameterText:{},dataKeyName:{},useChildrenOnly:{},showContentType:{}},[],[],!0);var bw=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M6 5.1438V16.0002H18.3391L6 5.1438ZM4 2.932C4 2.07155 5.01456 1.61285 5.66056 2.18123L21.6501 16.2494C22.3423 16.8584 21.9116 18.0002 20.9896 18.0002H6V22H4V2.932Z"></path></svg>'),xw=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),Cw=Q('<div class="heading svelte-11h445j"><!> <!></div> <!>',1);const $w={hash:"svelte-11h445j",code:".heading.svelte-11h445j {display:flex;margin-bottom:10px;}"};function cf(e,t){de(t,!0),qe(e,$w);const n=y(t,"data",7),r=Ke(t,["$$slots","$$events","$$legacy","$$host","data"]),o=nt(),{addParameter:i}=dn();var s={get data(){return n()},set data(a){n(a),m()}};return Wt(e,Fe({get data(){return n()}},()=>r,{allowExecute:!1,showSourceHandle:!1,icon:a=>{var l=bw();L(a,l)},children:(a,l)=>{var u=Cw(),d=ae(u),p=I(d);ze(p,{level:3,children:(h,v)=>{we();var w=Ee("输出参数");L(h,w)},$$slots:{default:!0}});var f=V(p,2);Me(f,{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{i(o,"outputDefs")},children:(h,v)=>{var w=xw();L(h,w)},$$slots:{default:!0}}),R(d);var g=V(d,2);Vt(g,{noneParameterText:"无输出参数",dataKeyName:"outputDefs",showContentType:!0}),L(a,u)},$$slots:{icon:!0,default:!0}})),fe(s)}ue(cf,{data:{}},[],[],!0);const Ta=e=>JSON.parse(JSON.stringify(e));var kw=me('<svg style="transform: scaleY(-1)" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M13 8V16C13 17.6569 11.6569 19 10 19H7.82929C7.41746 20.1652 6.30622 21 5 21C3.34315 21 2 19.6569 2 18C2 16.3431 3.34315 15 5 15C6.30622 15 7.41746 15.8348 7.82929 17H10C10.5523 17 11 16.5523 11 16V8C11 6.34315 12.3431 5 14 5H17V2L22 6L17 10V7H14C13.4477 7 13 7.44772 13 8ZM5 19C5.55228 19 6 18.5523 6 18C6 17.4477 5.55228 17 5 17C4.44772 17 4 17.4477 4 18C4 18.5523 4.44772 19 5 19Z"></path></svg>'),_w=Q('<div class="input-more-item svelte-1cfeest"><!></div>'),Sw=Q('<div class="input-more-setting svelte-1cfeest"><div class="input-more-item svelte-1cfeest">默认值： <!></div> <div class="input-more-item svelte-1cfeest">参数描述： <!></div> <!></div>'),Ew=Q('<div class="input-item svelte-1cfeest"><!> <!></div> <div class="input-item svelte-1cfeest"><!> <!></div> <div class="input-item svelte-1cfeest"><!></div>',1);const Pw={hash:"svelte-1cfeest",code:".input-item.svelte-1cfeest {display:flex;align-items:center;gap:2px;}.input-more-setting.svelte-1cfeest {display:flex;flex-direction:column;gap:10px;padding:10px;background:#fff;border:1px solid #ddd;border-radius:5px;width:200px;box-shadow:0 0 10px 2px rgba(0, 0, 0, 0.1);}.input-more-setting.svelte-1cfeest .input-more-item:where(.svelte-1cfeest) {display:flex;flex-direction:column;gap:3px;font-size:12px;color:#666;}"};function df(e,t){de(t,!0),qe(e,Pw);const n=y(t,"parameter",7),r=y(t,"position",7),o=y(t,"dataKeyName",7),i=y(t,"placeholder",7,"请输入参数值");let s=nt(),a=Zn(s),l=T(()=>{let _=a?.current?.data?.[o()],k;if(_&&r().length>0){let C=_;for(let N=0;N<r().length;N++){const P=r()[N];N==r().length-1?k=C[P]:C=C[P].children}}return{...n(),...k}});const{updateNodeData:u}=yt(),d=(_,k)=>{u(s,C=>{const N=C.data?.[o()];if(N&&r().length>0){let P=N;for(let H=0;H<r().length;H++){const Z=r()[H];H==r().length-1?P[Z]={...P[Z],[_]:k}:P=P[Z].children}}return{[o()]:[...Ta(N)]}})},p=(_,k)=>{const C=k.target.value;d(_,C)},f=_=>{const k=_.value;d("dataType",k)};let g;const h=()=>{u(s,_=>{let k=_.data?.[o()];if(k&&r().length>0){let C=k;for(let N=0;N<r().length;N++){const P=r()[N];N==r().length-1?C.splice(P,1):C=C[P].children}}return{[o()]:[...Ta(k)]}}),g?.hide()},v=()=>{u(s,_=>{let k=_.data?.[o()];if(k&&r().length>0){let C=k;for(let N=0;N<r().length;N++){const P=r()[N];N==r().length-1?C[P].children?C[P].children.push({id:_r(),name:"newParam",dataType:"String"}):C[P].children=[{id:_r(),name:"newParam",dataType:"String"}]:C=C[P].children}}return{[o()]:[...Ta(k)]}})};var w={get parameter(){return n()},set parameter(_){n(_),m()},get position(){return r()},set position(_){r(_),m()},get dataKeyName(){return o()},set dataKeyName(_){o(_),m()},get placeholder(){return i()},set placeholder(_="请输入参数值"){i(_),m()}},b=Ew(),x=ae(b),$=I(x);{var S=_=>{var k=Ne(),C=ae(k);Ct(C,17,r,Rr,(N,P)=>{we();var H=Ee(" ");L(N,H)}),L(_,k)};ce($,_=>{r().length>1&&_(S)})}var E=V($,2);{let _=T(()=>c(l).nameDisabled===!0);ot(E,{style:"width: 100%;",get value(){return c(l).name},get placeholder(){return i()},oninput:k=>{p("name",k)},get disabled(){return c(_)}})}R(x);var D=V(x,2),O=I(D);{let _=T(()=>c(l).dataType?[c(l).dataType]:[]),k=T(()=>c(l).dataTypeDisabled===!0);wt(O,{get items(){return f2},style:"width: 100%",defaultValue:["String"],get value(){return c(_)},get disabled(){return c(k)},onSelect:f})}var q=V(O,2);{var K=_=>{Me(_,{class:"input-btn-more",style:"margin-left: auto",onclick:v,children:(k,C)=>{var N=kw();L(k,N)},$$slots:{default:!0}})};ce(q,_=>{(c(l).dataType==="Object"||c(l).dataType==="Array")&&c(l).addChildDisabled!==!0&&_(K)})}R(D);var J=V(D,2),A=I(J);return qt(kr(A,{placement:"bottom",floating:_=>{var k=Sw(),C=I(k),N=V(I(C));{let M=T(()=>c(l).defaultValue||"");We(N,{rows:1,style:"width: 100%;",get value(){return c(M)},onchange:X=>{p("defaultValue",X)}})}R(C);var P=V(C,2),H=V(I(P));{let M=T(()=>c(l).description||"");We(H,{rows:3,style:"width: 100%;",get value(){return c(M)},onchange:X=>{p("description",X)}})}R(P);var Z=V(P,2);{var Y=M=>{var X=_w(),te=I(X);Me(te,{onclick:h,children:(oe,j)=>{we();var G=Ee("删除");L(oe,G)},$$slots:{default:!0}}),R(X),L(M,X)};ce(Z,M=>{c(l).deleteDisabled!==!0&&M(Y)})}R(k),L(_,k)},children:(_,k)=>{Yo(_,{})},$$slots:{floating:!0,default:!0}}),_=>g=_,()=>g),R(J),L(e,b),fe(w)}ue(df,{parameter:{},position:{},dataKeyName:{},placeholder:{}},[],[],!0);var Nw=Q("<!> <!>",1),Tw=Q('<div class="none-params svelte-1sm1mgi"> </div>'),Dw=Q('<div class="input-header svelte-1sm1mgi">参数名称</div> <div class="input-header svelte-1sm1mgi">参数类型</div> <div class="input-header svelte-1sm1mgi"></div>',1),Ow=Q('<div class="input-container svelte-1sm1mgi"><!> <!></div>');const Lw={hash:"svelte-1sm1mgi",code:`.input-container.svelte-1sm1mgi {display:grid;grid-template-columns:40% 50% 10%;row-gap:5px;column-gap:3px;}.input-container.svelte-1sm1mgi .none-params:where(.svelte-1sm1mgi) {font-size:12px;background:#f8f8f8;height:40px;display:flex;justify-content:center;align-items:center;border-radius:5px;width:calc(100% - 5px);grid-column:1 / -1;
  /* 从第一列开始到最后一列结束 */}.input-container.svelte-1sm1mgi .input-header:where(.svelte-1sm1mgi) {font-size:12px;color:#666;}`};function Kn(e,t){de(t,!0),qe(e,Lw);const n=(h,v=ht,w=ht)=>{var b=Ne(),x=ae(b);Ct(x,19,v,$=>`${$.id}_${$.children?$.children.length:0}`,($,S,E)=>{var D=Nw(),O=ae(D);{let J=T(()=>[...w(),c(E)]);df(O,{get parameter(){return c(S)},get position(){return c(J)},get dataKeyName(){return o()},get placeholder(){return i()}})}var q=V(O,2);{var K=J=>{{let A=T(()=>[...w(),c(E)]);n(J,()=>c(S).children,()=>c(A))}};ce(q,J=>{c(S).children&&J(K)})}L($,D)},$=>{var S=Ne(),E=ae(S);{var D=O=>{var q=Tw(),K=I(q,!0);R(q),Se(()=>Xe(K,r())),L(O,q)};ce(E,O=>{w().length===0&&O(D)})}L($,S)}),L(h,b)},r=y(t,"noneParameterText",7,"无输出参数"),o=y(t,"dataKeyName",7,"outputDefs"),i=y(t,"placeholder",7,"请输入参数名称");let s=nt(),a=Zn(s),l=T(()=>[...a?.current?.data?.[o()]||[]]);var u={get noneParameterText(){return r()},set noneParameterText(h="无输出参数"){r(h),m()},get dataKeyName(){return o()},set dataKeyName(h="outputDefs"){o(h),m()},get placeholder(){return i()},set placeholder(h="请输入参数名称"){i(h),m()}},d=Ow(),p=I(d);{var f=h=>{var v=Dw();we(4),L(h,v)};ce(p,h=>{c(l).length!==0&&h(f)})}var g=V(p,2);return n(g,()=>c(l)||[],()=>[]),R(d),L(e,d),fe(u)}ue(Kn,{noneParameterText:{},dataKeyName:{},placeholder:{}},[],[],!0);var Mw=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M20.7134 7.12811L20.4668 7.69379C20.2864 8.10792 19.7136 8.10792 19.5331 7.69379L19.2866 7.12811C18.8471 6.11947 18.0555 5.31641 17.0677 4.87708L16.308 4.53922C15.8973 4.35653 15.8973 3.75881 16.308 3.57612L17.0252 3.25714C18.0384 2.80651 18.8442 1.97373 19.2761 0.930828L19.5293 0.319534C19.7058 -0.106511 20.2942 -0.106511 20.4706 0.319534L20.7238 0.930828C21.1558 1.97373 21.9616 2.80651 22.9748 3.25714L23.6919 3.57612C24.1027 3.75881 24.1027 4.35653 23.6919 4.53922L22.9323 4.87708C21.9445 5.31641 21.1529 6.11947 20.7134 7.12811ZM9 2C13.0675 2 16.426 5.03562 16.9337 8.96494L19.1842 12.5037C19.3324 12.7367 19.3025 13.0847 18.9593 13.2317L17 14.071V17C17 18.1046 16.1046 19 15 19H13.001L13 22H4L4.00025 18.3061C4.00033 17.1252 3.56351 16.0087 2.7555 15.0011C1.65707 13.6313 1 11.8924 1 10C1 5.58172 4.58172 2 9 2ZM9 4C5.68629 4 3 6.68629 3 10C3 11.3849 3.46818 12.6929 4.31578 13.7499C5.40965 15.114 6.00036 16.6672 6.00025 18.3063L6.00013 20H11.0007L11.0017 17H15V12.7519L16.5497 12.0881L15.0072 9.66262L14.9501 9.22118C14.5665 6.25141 12.0243 4 9 4ZM19.4893 16.9929L21.1535 18.1024C22.32 16.3562 23 14.2576 23 12.0001C23 11.317 22.9378 10.6486 22.8186 10L20.8756 10.5C20.9574 10.9878 21 11.489 21 12.0001C21 13.8471 20.4436 15.5642 19.4893 16.9929Z"></path></svg>'),Hw=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),Vw=(e,t,n)=>t(n,{temperature:parseFloat(e.target.value)}),zw=(e,t,n)=>t(n,{topP:parseFloat(e.target.value)}),Aw=(e,t,n)=>t(n,{topK:parseInt(e.target.value)}),Rw=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),Iw=Q('<div class="heading svelte-q0cqsa"><!> <!></div> <!> <!> <div class="setting-title svelte-q0cqsa">模型</div> <div class="setting-item svelte-q0cqsa"><!> <!></div> <div class="setting-title svelte-q0cqsa">采样参数</div> <div class="setting-item svelte-q0cqsa"><div class="slider-container svelte-q0cqsa"><span class="svelte-q0cqsa"> </span> <input class="nodrag svelte-q0cqsa" type="range" min="0" max="1" step="0.1"/></div></div> <div class="setting-item svelte-q0cqsa"><div class="slider-container svelte-q0cqsa"><span class="svelte-q0cqsa"> </span> <input class="nodrag svelte-q0cqsa" type="range" min="0" max="1" step="0.1"/></div></div> <div class="setting-item svelte-q0cqsa"><div class="slider-container svelte-q0cqsa"><span class="svelte-q0cqsa"> </span> <input class="nodrag svelte-q0cqsa" type="range" min="0" max="100" step="1"/></div></div> <div class="setting-title svelte-q0cqsa">系统提示词</div> <div class="setting-item svelte-q0cqsa"><!></div> <div class="setting-title svelte-q0cqsa">用户提示词</div> <div class="setting-item svelte-q0cqsa"><!></div> <div class="heading svelte-q0cqsa"><!> <!> <!></div> <!>',1);const qw={hash:"svelte-q0cqsa",code:`.heading.svelte-q0cqsa {display:flex;align-items:center;margin-bottom:10px;}.setting-title.svelte-q0cqsa {font-size:12px;color:#999;margin-bottom:4px;margin-top:10px;}.setting-item.svelte-q0cqsa {display:flex;align-items:center;justify-content:space-between;margin-bottom:10px;gap:10px;}

    /* 新增样式 */.slider-container.svelte-q0cqsa {width:100%;display:flex;flex-direction:column;gap:4px;}.slider-container.svelte-q0cqsa span:where(.svelte-q0cqsa) {font-size:12px;color:#666;display:flex;justify-content:space-between;align-items:center;}input[type="range"].svelte-q0cqsa {width:100%;height:4px;background:#ddd;border-radius:2px;outline:none;-webkit-appearance:none;}input[type="range"].svelte-q0cqsa::-webkit-slider-thumb {-webkit-appearance:none;width:14px;height:14px;background:#007bff;border-radius:50%;cursor:pointer;}`};function ff(e,t){de(t,!0),qe(e,qw);const n=y(t,"data",7),r=Ke(t,["$$slots","$$events","$$legacy","$$host","data"]),o=nt(),{addParameter:i}=dn(),s=rr();let a=De(Yt([]));Gn(async()=>{const p=await s.provider?.llm?.();c(a).push(...p||[])});const{updateNodeData:l}=yt(),u=p=>{l(o,()=>({outType:p})),p==="text"?l(o,{outputDefs:[{name:"output",dataType:"String",dataTypeDisabled:!0,deleteDisabled:!0}]}):l(o,{outputDefs:[]})};et(()=>{n().outType||u("text")});var d={get data(){return n()},set data(p){n(p),m()}};return Wt(e,Fe({get data(){return n()}},()=>r,{icon:p=>{var f=Mw();L(p,f)},children:(p,f)=>{var g=Iw(),h=ae(g),v=I(h);ze(v,{level:3,children:(ie,ee)=>{we();var re=Ee("输入参数");L(ie,re)},$$slots:{default:!0}});var w=V(v,2);Me(w,{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{i(o)},children:(ie,ee)=>{var re=Hw();L(ie,re)},$$slots:{default:!0}}),R(h);var b=V(h,2);Vt(b,{});var x=V(b,2);ze(x,{level:3,mt:"10px",children:(ie,ee)=>{we();var re=Ee("模型设置");L(ie,re)},$$slots:{default:!0}});var $=V(x,4),S=I($);{let ie=T(()=>n().llmId?[n().llmId]:[]);wt(S,{get items(){return c(a)},style:"width: 100%",placeholder:"请选择模型",onSelect:ee=>{const re=ee.value;l(o,()=>({llmId:re}))},get value(){return c(ie)}})}var E=V(S,2);Yo(E,{}),R($);var D=V($,4),O=I(D),q=I(O),K=I(q);R(q);var J=V(q,2);wn(J),J.__input=[Vw,l,o],R(O),R(D);var A=V(D,2),_=I(A),k=I(_),C=I(k);R(k);var N=V(k,2);wn(N),N.__input=[zw,l,o],R(_),R(A);var P=V(A,2),H=I(P),Z=I(H),Y=I(Z);R(Z);var M=V(Z,2);wn(M),M.__input=[Aw,l,o],R(H),R(P);var X=V(P,4),te=I(X);{let ie=T(()=>n().systemPrompt||"");We(te,{rows:5,placeholder:"请输入系统提示词",style:"width: 100%",get value(){return c(ie)},oninput:ee=>{l(o,{systemPrompt:ee.target.value})}})}R(X);var oe=V(X,4),j=I(oe);{let ie=T(()=>n().userPrompt||"");We(j,{rows:5,placeholder:"请输入用户提示词",style:"width: 100%",get value(){return c(ie)},oninput:ee=>{l(o,{userPrompt:ee.target.value})}})}R(oe);var G=V(oe,2),F=I(G);ze(F,{level:3,children:(ie,ee)=>{we();var re=Ee("输出参数");L(ie,re)},$$slots:{default:!0}});var se=V(F,2);{let ie=T(()=>n().outType?[n().outType]:[]);wt(se,{items:[{label:"文本",value:"text"},{label:"JSON",value:"json"}],style:"width: 100px;margin-left: auto",defaultValue:"text",onSelect:ee=>{u(ee.value)},get value(){return c(ie)}})}var W=V(se,2);{var ye=ie=>{Me(ie,{class:"input-btn-more",style:"margin-left: 10px",onclick:()=>{i(o,"outputDefs")},children:(ee,re)=>{var ge=Rw();L(ee,ge)},$$slots:{default:!0}})};ce(W,ie=>{n().outType==="json"&&ie(ye)})}R(G);var xe=V(G,2);Kn(xe,{}),Se(()=>{Xe(K,`Temperature: ${n().temperature??.5??""}`),ci(J,n().temperature??.5),Xe(C,`Top P: ${n().topP??.9??""}`),ci(N,n().topP??.9),Xe(Y,`Top K: ${n().topK??50??""}`),ci(M,n().topK??50)}),L(p,g)},$$slots:{icon:!0,default:!0}})),fe(d)}Mn(["input"]),ue(ff,{data:{}},[],[],!0);var Zw=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M23 12L15.9289 19.0711L14.5147 17.6569L20.1716 12L14.5147 6.34317L15.9289 4.92896L23 12ZM3.82843 12L9.48528 17.6569L8.07107 19.0711L1 12L8.07107 4.92896L9.48528 6.34317L3.82843 12Z"></path></svg>'),Bw=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),Kw=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),jw=Q('<div class="heading svelte-15t2v24"><!> <!></div> <!> <!> <div class="setting-title svelte-15t2v24">执行引擎</div> <div class="setting-item svelte-15t2v24"><!></div> <div class="setting-title svelte-15t2v24">执行代码</div> <div class="setting-item svelte-15t2v24"><!></div> <div class="heading svelte-15t2v24"><!> <!></div> <!>',1);const Yw={hash:"svelte-15t2v24",code:".heading.svelte-15t2v24 {display:flex;margin-bottom:10px;}.setting-title.svelte-15t2v24 {font-size:12px;color:#999;margin-bottom:4px;margin-top:10px;}.setting-item.svelte-15t2v24 {display:flex;align-items:center;justify-content:space-between;margin-bottom:10px;gap:10px;}"};function pf(e,t){de(t,!0),qe(e,Yw);const n=y(t,"data",7),r=Ke(t,["$$slots","$$events","$$legacy","$$host","data"]);Gn(()=>{n().engine||s(o,()=>({engine:"qlexpress"}))});const o=nt(),{addParameter:i}=dn(),{updateNodeData:s}=yt(),a=[{label:"QLExpress",value:"qlexpress"},{label:"Groovy",value:"groovy"},{label:"JavaScript",value:"js"}];var l={get data(){return n()},set data(u){n(u),m()}};return Wt(e,Fe({get data(){return n()}},()=>r,{icon:u=>{var d=Zw();L(u,d)},children:(u,d)=>{var p=jw(),f=ae(p),g=I(f);ze(g,{level:3,children:(K,J)=>{we();var A=Ee("输入参数");L(K,A)},$$slots:{default:!0}});var h=V(g,2);Me(h,{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{i(o)},children:(K,J)=>{var A=Bw();L(K,A)},$$slots:{default:!0}}),R(f);var v=V(f,2);Vt(v,{});var w=V(v,2);ze(w,{level:3,mt:"10px",children:(K,J)=>{we();var A=Ee("代码");L(K,A)},$$slots:{default:!0}});var b=V(w,4),x=I(b);{let K=T(()=>n().engine?[n().engine]:["qlexpress"]);wt(x,{get items(){return a},style:"width: 100%",placeholder:"请选择执行引擎",onSelect:J=>{const A=J.value;s(o,()=>({engine:A}))},get value(){return c(K)}})}R(b);var $=V(b,4),S=I($);{let K=T(()=>n().code||"");We(S,{rows:10,placeholder:"请输入执行代码，注：输出内容需添加到_result中，如：_result.put(key, value)",style:"width: 100%",onchange:J=>{s(o,()=>({code:J.target.value}))},get value(){return c(K)}})}R($);var E=V($,2),D=I(E);ze(D,{level:3,mt:"10px",children:(K,J)=>{we();var A=Ee("输出参数");L(K,A)},$$slots:{default:!0}});var O=V(D,2);Me(O,{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{i(o,"outputDefs")},children:(K,J)=>{var A=Kw();L(K,A)},$$slots:{default:!0}}),R(E);var q=V(E,2);Kn(q,{}),L(u,p)},$$slots:{icon:!0,default:!0}})),fe(l)}ue(pf,{data:{}},[],[],!0);var Xw=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M2 4C2 3.44772 2.44772 3 3 3H21C21.5523 3 22 3.44772 22 4V20C22 20.5523 21.5523 21 21 21H3C2.44772 21 2 20.5523 2 20V4ZM4 5V19H20V5H4ZM7 8H17V11H15V10H13V14H14.5V16H9.5V14H11V10H9V11H7V8Z"></path></svg>'),Fw=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),Ww=Q('<div class="heading svelte-15t2v24"><!> <!></div> <!> <!> <div class="setting-item svelte-15t2v24"><!></div> <div class="heading svelte-15t2v24"><!></div> <!>',1);const Gw={hash:"svelte-15t2v24",code:".heading.svelte-15t2v24 {display:flex;margin-bottom:10px;}.setting-item.svelte-15t2v24 {display:flex;align-items:center;justify-content:space-between;margin-bottom:10px;gap:10px;}"};function gf(e,t){de(t,!0),qe(e,Gw);const n=y(t,"data",7),r=Ke(t,["$$slots","$$events","$$legacy","$$host","data"]),o=nt(),{addParameter:i}=dn(),{updateNodeData:s}=yt();et(()=>{(!n().outputDefs||n().outputDefs.length===0)&&i(o,"outputDefs",{name:"output",dataType:"String",dataTypeDisabled:!0,deleteDisabled:!0})});var a={get data(){return n()},set data(l){n(l),m()}};return Wt(e,Fe({get data(){return n()}},()=>r,{icon:l=>{var u=Xw();L(l,u)},children:(l,u)=>{var d=Ww(),p=ae(d),f=I(p);ze(f,{level:3,children:(E,D)=>{we();var O=Ee("输入参数");L(E,O)},$$slots:{default:!0}});var g=V(f,2);Me(g,{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{i(o)},children:(E,D)=>{var O=Fw();L(E,O)},$$slots:{default:!0}}),R(p);var h=V(p,2);Vt(h,{});var v=V(h,2);ze(v,{level:3,mt:"10px",mb:"10px",children:(E,D)=>{we();var O=Ee("模板内容");L(E,O)},$$slots:{default:!0}});var w=V(v,2),b=I(w);{let E=T(()=>n().template||"");We(b,{rows:10,placeholder:"请输入模板内容",style:"width: 100%",onchange:D=>{s(o,()=>({template:D.target.value}))},get value(){return c(E)}})}R(w);var x=V(w,2),$=I(x);ze($,{level:3,mt:"10px",children:(E,D)=>{we();var O=Ee("输出参数");L(E,O)},$$slots:{default:!0}}),R(x);var S=V(x,2);Kn(S,{}),L(l,d)},$$slots:{icon:!0,default:!0}})),fe(a)}ue(gf,{data:{}},[],[],!0);var Uw=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M6.23509 6.45329C4.85101 7.89148 4 9.84636 4 12C4 16.4183 7.58172 20 12 20C13.0808 20 14.1116 19.7857 15.0521 19.3972C15.1671 18.6467 14.9148 17.9266 14.8116 17.6746C14.582 17.115 13.8241 16.1582 12.5589 14.8308C12.2212 14.4758 12.2429 14.2035 12.3636 13.3943L12.3775 13.3029C12.4595 12.7486 12.5971 12.4209 14.4622 12.1248C15.4097 11.9746 15.6589 12.3533 16.0043 12.8777C16.0425 12.9358 16.0807 12.9928 16.1198 13.0499C16.4479 13.5297 16.691 13.6394 17.0582 13.8064C17.2227 13.881 17.428 13.9751 17.7031 14.1314C18.3551 14.504 18.3551 14.9247 18.3551 15.8472V15.9518C18.3551 16.3434 18.3168 16.6872 18.2566 16.9859C19.3478 15.6185 20 13.8854 20 12C20 8.70089 18.003 5.8682 15.1519 4.64482C14.5987 5.01813 13.8398 5.54726 13.575 5.91C13.4396 6.09538 13.2482 7.04166 12.6257 7.11976C12.4626 7.14023 12.2438 7.12589 12.012 7.11097C11.3905 7.07058 10.5402 7.01606 10.268 7.75495C10.0952 8.2232 10.0648 9.49445 10.6239 10.1543C10.7134 10.2597 10.7307 10.4547 10.6699 10.6735C10.59 10.9608 10.4286 11.1356 10.3783 11.1717C10.2819 11.1163 10.0896 10.8931 9.95938 10.7412C9.64554 10.3765 9.25405 9.92233 8.74797 9.78176C8.56395 9.73083 8.36166 9.68867 8.16548 9.64736C7.6164 9.53227 6.99443 9.40134 6.84992 9.09302C6.74442 8.8672 6.74488 8.55621 6.74529 8.22764C6.74529 7.8112 6.74529 7.34029 6.54129 6.88256C6.46246 6.70541 6.35689 6.56446 6.23509 6.45329ZM12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22Z"></path></svg>'),Jw=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),Qw=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),eb=Q('<!> <div class="radio-group svelte-1dxz5e"><label class="svelte-1dxz5e"><!>none</label> <label class="svelte-1dxz5e"><!>form-data</label> <label class="svelte-1dxz5e"><!>x-www-form-urlencoded</label> <label class="svelte-1dxz5e"><!>json</label> <label class="svelte-1dxz5e"><!>raw</label></div>',1),tb=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),nb=Q('<div class="heading svelte-1dxz5e" style="padding-top: 10px"><!> <!></div> <!>',1),rb=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),ob=Q('<div class="heading svelte-1dxz5e" style="padding-top: 10px"><!> <!></div> <!>',1),ib=Q('<div style="width: 100%"><!></div>'),sb=Q('<div style="width: 100%"><!></div>'),ab=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),lb=Q('<div class="heading svelte-1dxz5e"><!> <!></div> <!> <!> <div style="display: flex;gap: 2px;width: 100%;padding: 10px 0"><div><!></div> <div style="width: 100%"><!></div></div> <div class="heading svelte-1dxz5e"><!> <!></div> <!> <!> <!> <!> <!> <!> <div class="heading svelte-1dxz5e"><!> <!></div> <!>',1);const ub={hash:"svelte-1dxz5e",code:".heading.svelte-1dxz5e {display:flex;margin-bottom:10px;}.radio-group.svelte-1dxz5e {display:flex;margin:10px 0;flex-wrap:wrap;}.radio-group.svelte-1dxz5e label:where(.svelte-1dxz5e) {display:flex;font-size:14px;box-sizing:border-box;}"};function hf(e,t){de(t,!0),qe(e,ub);const n=y(t,"data",7),r=Ke(t,["$$slots","$$events","$$legacy","$$host","data"]);Gn(()=>{n().method||a(i,()=>({method:"get"})),n().outputDefs||s(i,"outputDefs",[{name:"headers",nameDisabled:!0,dataType:"Object",dataTypeDisabled:!0,deleteDisabled:!0},{name:"body",nameDisabled:!0,dataType:"String",deleteDisabled:!0},{name:"statusCode",nameDisabled:!0,dataType:"Number",dataTypeDisabled:!0,deleteDisabled:!0}])});const o=[{value:"get",label:"GET"},{value:"post",label:"POST"},{value:"put",label:"PUT"},{value:"delete",label:"DELETE"},{value:"head",label:"HEAD"},{value:"patch",label:"PATCH"}],i=nt(),{addParameter:s}=dn(),{updateNodeData:a}=yt();var l={get data(){return n()},set data(u){n(u),m()}};return Wt(e,Fe({get data(){return n()}},()=>r,{icon:u=>{var d=Uw();L(u,d)},children:(u,d)=>{var p=lb(),f=ae(p),g=I(f);ze(g,{level:3,children:(j,G)=>{we();var F=Ee("输入参数");L(j,F)},$$slots:{default:!0}});var h=V(g,2);Me(h,{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{s(i)},children:(j,G)=>{var F=Jw();L(j,F)},$$slots:{default:!0}}),R(f);var v=V(f,2);Vt(v,{});var w=V(v,2);ze(w,{level:3,mt:"10px",children:(j,G)=>{we();var F=Ee("URL 地址");L(j,F)},$$slots:{default:!0}});var b=V(w,2),x=I(b),$=I(x);{let j=T(()=>n().method?[n().method]:["get"]);wt($,{get items(){return o},style:"width: 100%",placeholder:"请选择请求方式",onSelect:G=>{const F=G.value;a(i,()=>({method:F}))},get value(){return c(j)}})}R(x);var S=V(x,2),E=I(S);{let j=T(()=>n().url||"");ot(E,{placeholder:"请输入url",style:"width: 100%",onchange:G=>{a(i,()=>({url:G.target.value}))},get value(){return c(j)}})}R(S),R(b);var D=V(b,2),O=I(D);ze(O,{level:3,children:(j,G)=>{we();var F=Ee("Http 头信息");L(j,F)},$$slots:{default:!0}});var q=V(O,2);Me(q,{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{s(i,"headers")},children:(j,G)=>{var F=Qw();L(j,F)},$$slots:{default:!0}}),R(D);var K=V(D,2);Vt(K,{dataKeyName:"headers"});var J=V(K,2);{var A=j=>{var G=eb(),F=ae(G);ze(F,{level:3,mt:"10px",children:(ke,B)=>{we();var ct=Ee("Body");L(ke,ct)},$$slots:{default:!0}});var se=V(F,2),W=I(se),ye=I(W);{let ke=T(()=>!n().bodyType||n().bodyType==="");ot(ye,{type:"radio",name:"bodyType",value:"",get checked(){return c(ke)},onchange:B=>{B.target?.checked&&a(i,{bodyType:""})}})}we(),R(W);var xe=V(W,2),ie=I(xe);{let ke=T(()=>n().bodyType==="form-data");ot(ie,{type:"radio",name:"bodyType",value:"form-data",get checked(){return c(ke)},onchange:B=>{B.target?.checked&&a(i,{bodyType:"form-data"})}})}we(),R(xe);var ee=V(xe,2),re=I(ee);{let ke=T(()=>n().bodyType==="x-www-form-urlencoded");ot(re,{type:"radio",name:"bodyType",value:"x-www-form-urlencoded",get checked(){return c(ke)},onchange:B=>{B.target?.checked&&a(i,{bodyType:"x-www-form-urlencoded"})}})}we(),R(ee);var ge=V(ee,2),he=I(ge);{let ke=T(()=>n().bodyType==="json");ot(he,{type:"radio",name:"bodyType",value:"json",get checked(){return c(ke)},onchange:B=>{B.target?.checked&&a(i,{bodyType:"json"})}})}we(),R(ge);var le=V(ge,2),Te=I(le);{let ke=T(()=>n().bodyType==="raw");ot(Te,{type:"radio",name:"bodyType",value:"raw",get checked(){return c(ke)},onchange:B=>{B.target?.checked&&a(i,{bodyType:"raw"})}})}we(),R(le),R(se),L(j,G)};ce(J,j=>{(n().method==="post"||n().method==="put"||n().method==="delete"||n().method==="patch")&&j(A)})}var _=V(J,2);{var k=j=>{var G=nb(),F=ae(G),se=I(F);ze(se,{level:3,children:(xe,ie)=>{we();var ee=Ee("参数");L(xe,ee)},$$slots:{default:!0}});var W=V(se,2);Me(W,{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{s(i,"formData")},children:(xe,ie)=>{var ee=tb();L(xe,ee)},$$slots:{default:!0}}),R(F);var ye=V(F,2);Vt(ye,{dataKeyName:"formData"}),L(j,G)};ce(_,j=>{n().bodyType==="form-data"&&j(k)})}var C=V(_,2);{var N=j=>{var G=ob(),F=ae(G),se=I(F);ze(se,{level:3,children:(xe,ie)=>{we();var ee=Ee("Body 参数");L(xe,ee)},$$slots:{default:!0}});var W=V(se,2);Me(W,{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{s(i,"formUrlencoded")},children:(xe,ie)=>{var ee=rb();L(xe,ee)},$$slots:{default:!0}}),R(F);var ye=V(F,2);Vt(ye,{dataKeyName:"formUrlencoded"}),L(j,G)};ce(C,j=>{n().bodyType==="x-www-form-urlencoded"&&j(N)})}var P=V(C,2);{var H=j=>{var G=ib(),F=I(G);We(F,{rows:5,style:"width: 100%",placeholder:"请输入 json 信息",get value(){return n().bodyJson},oninput:se=>{a(i,{bodyJson:se.target.value})}}),R(G),L(j,G)};ce(P,j=>{n().bodyType==="json"&&j(H)})}var Z=V(P,2);{var Y=j=>{var G=sb(),F=I(G);We(F,{rows:5,style:"width: 100%",placeholder:"请输入请求信息",get value(){return n().bodyRaw},oninput:se=>{a(i,{bodyRaw:se.target.value})}}),R(G),L(j,G)};ce(Z,j=>{n().bodyType==="raw"&&j(Y)})}var M=V(Z,2),X=I(M);ze(X,{level:3,mt:"10px",children:(j,G)=>{we();var F=Ee("输出参数");L(j,F)},$$slots:{default:!0}});var te=V(X,2);Me(te,{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{s(i,"outputDefs")},children:(j,G)=>{var F=ab();L(j,F)},$$slots:{default:!0}}),R(M);var oe=V(M,2);Kn(oe,{}),L(u,p)},$$slots:{icon:!0,default:!0}})),fe(l)}ue(hf,{data:{}},[],[],!0);var cb=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M15.5 5C13.567 5 12 6.567 12 8.5C12 10.433 13.567 12 15.5 12C17.433 12 19 10.433 19 8.5C19 6.567 17.433 5 15.5 5ZM10 8.5C10 5.46243 12.4624 3 15.5 3C18.5376 3 21 5.46243 21 8.5C21 9.6575 20.6424 10.7315 20.0317 11.6175L22.7071 14.2929L21.2929 15.7071L18.6175 13.0317C17.7315 13.6424 16.6575 14 15.5 14C12.4624 14 10 11.5376 10 8.5ZM3 4H8V6H3V4ZM3 11H8V13H3V11ZM21 18V20H3V18H21Z"></path></svg>'),db=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),fb=Q('<div class="heading svelte-15t2v24"><!> <!></div> <!> <!> <div class="setting-title svelte-15t2v24">知识库</div> <div class="setting-item svelte-15t2v24"><!></div> <div class="setting-title svelte-15t2v24">关键字</div> <div class="setting-item svelte-15t2v24"><!></div> <div class="setting-title svelte-15t2v24">获取数据量</div> <div class="setting-item svelte-15t2v24"><!></div> <div class="heading svelte-15t2v24"><!></div> <!>',1);const pb={hash:"svelte-15t2v24",code:".heading.svelte-15t2v24 {display:flex;margin-bottom:10px;}.setting-title.svelte-15t2v24 {font-size:12px;color:#999;margin-bottom:4px;margin-top:10px;}.setting-item.svelte-15t2v24 {display:flex;align-items:center;justify-content:space-between;margin-bottom:10px;gap:10px;}"};function vf(e,t){de(t,!0),qe(e,pb);const n=y(t,"data",7),r=Ke(t,["$$slots","$$events","$$legacy","$$host","data"]),o=nt(),{addParameter:i}=dn(),s=rr();let a=De(Yt([]));Gn(async()=>{const d=await s.provider?.knowledge?.();c(a).push(...d||[])});const{updateNodeData:l}=yt();et(()=>{(!n().outputDefs||n().outputDefs.length===0)&&i(o,"outputDefs",{name:"documents",dataType:"Array",nameDisabled:!0,dataTypeDisabled:!0,addChildDisabled:!0,deleteDisabled:!0,children:[{name:"title",dataType:"String",nameDisabled:!0,dataTypeDisabled:!0,deleteDisabled:!0},{name:"content",dataType:"String",nameDisabled:!0,dataTypeDisabled:!0,deleteDisabled:!0},{name:"documentId",dataType:"Number",nameDisabled:!0,dataTypeDisabled:!0,deleteDisabled:!0},{name:"knowledgeId",dataType:"Number",nameDisabled:!0,dataTypeDisabled:!0,deleteDisabled:!0}]})});var u={get data(){return n()},set data(d){n(d),m()}};return Wt(e,Fe({get data(){return n()}},()=>r,{icon:d=>{var p=cb();L(d,p)},children:(d,p)=>{var f=fb(),g=ae(f),h=I(g);ze(h,{level:3,children:(A,_)=>{we();var k=Ee("输入参数");L(A,k)},$$slots:{default:!0}});var v=V(h,2);Me(v,{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{i(o)},children:(A,_)=>{var k=db();L(A,k)},$$slots:{default:!0}}),R(g);var w=V(g,2);Vt(w,{});var b=V(w,2);ze(b,{level:3,mt:"10px",children:(A,_)=>{we();var k=Ee("知识库设置");L(A,k)},$$slots:{default:!0}});var x=V(b,4),$=I(x);{let A=T(()=>n().knowledgeId?[n().knowledgeId]:[]);wt($,{get items(){return c(a)},style:"width: 100%",placeholder:"请选择知识库",onSelect:_=>{const k=_.value;l(o,()=>({knowledgeId:k}))},get value(){return c(A)}})}R(x);var S=V(x,4),E=I(S);ot(E,{placeholder:"请输入关键字",style:"width: 100%",get value(){return n().keyword},onchange:A=>{const _=A.target.value;l(o,()=>({keyword:_}))}}),R(S);var D=V(S,4),O=I(D);{let A=T(()=>n().limit||"");ot(O,{placeholder:"搜索的数据条数",style:"width: 100%",onchange:_=>{const k=_.target.value;l(o,()=>({limit:k}))},get value(){return c(A)}})}R(D);var q=V(D,2),K=I(q);ze(K,{level:3,mt:"10px",children:(A,_)=>{we();var k=Ee("输出参数");L(A,k)},$$slots:{default:!0}}),R(q);var J=V(q,2);Kn(J,{}),L(d,f)},$$slots:{icon:!0,default:!0}})),fe(u)}ue(vf,{data:{}},[],[],!0);var gb=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M18.031 16.6168L22.3137 20.8995L20.8995 22.3137L16.6168 18.031C15.0769 19.263 13.124 20 11 20C6.032 20 2 15.968 2 11C2 6.032 6.032 2 11 2C15.968 2 20 6.032 20 11C20 13.124 19.263 15.0769 18.031 16.6168ZM16.0247 15.8748C17.2475 14.6146 18 12.8956 18 11C18 7.1325 14.8675 4 11 4C7.1325 4 4 7.1325 4 11C4 14.8675 7.1325 18 11 18C12.8956 18 14.6146 17.2475 15.8748 16.0247L16.0247 15.8748Z"></path></svg>'),hb=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),vb=Q('<div class="heading svelte-15t2v24"><!> <!></div> <!> <!> <div class="setting-title svelte-15t2v24">搜索引擎</div> <div class="setting-item svelte-15t2v24"><!></div> <div class="setting-title svelte-15t2v24">关键字</div> <div class="setting-item svelte-15t2v24"><!></div> <div class="setting-title svelte-15t2v24">搜索数据量</div> <div class="setting-item svelte-15t2v24"><!></div> <div class="heading svelte-15t2v24"><!></div> <!>',1);const mb={hash:"svelte-15t2v24",code:".heading.svelte-15t2v24 {display:flex;margin-bottom:10px;}.setting-title.svelte-15t2v24 {font-size:12px;color:#999;margin-bottom:4px;margin-top:10px;}.setting-item.svelte-15t2v24 {display:flex;align-items:center;justify-content:space-between;margin-bottom:10px;gap:10px;}"};function mf(e,t){de(t,!0),qe(e,mb);const n=y(t,"data",7),r=Ke(t,["$$slots","$$events","$$legacy","$$host","data"]),o=nt(),{addParameter:i}=dn(),s=rr();let a=De(Yt([]));Gn(async()=>{const d=await s.provider?.searchEngine?.();c(a).push(...d||[])});const{updateNodeData:l}=yt();et(()=>{(!n().outputDefs||n().outputDefs.length===0)&&i(o,"outputDefs",{name:"documents",dataType:"Array",nameDisabled:!0,dataTypeDisabled:!0,addChildDisabled:!0,deleteDisabled:!0,children:[{name:"title",dataType:"String",nameDisabled:!0,dataTypeDisabled:!0,deleteDisabled:!0},{name:"content",dataType:"String",nameDisabled:!0,dataTypeDisabled:!0,deleteDisabled:!0}]})});var u={get data(){return n()},set data(d){n(d),m()}};return Wt(e,Fe({get data(){return n()}},()=>r,{icon:d=>{var p=gb();L(d,p)},children:(d,p)=>{var f=vb(),g=ae(f),h=I(g);ze(h,{level:3,children:(A,_)=>{we();var k=Ee("输入参数");L(A,k)},$$slots:{default:!0}});var v=V(h,2);Me(v,{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{i(o)},children:(A,_)=>{var k=hb();L(A,k)},$$slots:{default:!0}}),R(g);var w=V(g,2);Vt(w,{});var b=V(w,2);ze(b,{level:3,mt:"10px",children:(A,_)=>{we();var k=Ee("搜索引擎设置");L(A,k)},$$slots:{default:!0}});var x=V(b,4),$=I(x);{let A=T(()=>n().engine?[n().engine]:[]);wt($,{get items(){return c(a)},style:"width: 100%",placeholder:"请选择搜索引擎",onSelect:_=>{const k=_.value;l(o,()=>({engine:k}))},get value(){return c(A)}})}R(x);var S=V(x,4),E=I(S);ot(E,{placeholder:"请输入关键字",style:"width: 100%",get value(){return n().keyword},onchange:A=>{const _=A.target.value;l(o,()=>({keyword:_}))}}),R(S);var D=V(S,4),O=I(D);ot(O,{placeholder:"搜索的数据条数",style:"width: 100%",get value(){return n().limit},onchange:A=>{const _=A.target.value;l(o,()=>({limit:_}))}}),R(D);var q=V(D,2),K=I(q);ze(K,{level:3,mt:"10px",children:(A,_)=>{we();var k=Ee("输出参数");L(A,k)},$$slots:{default:!0}}),R(q);var J=V(q,2);Kn(J,{}),L(d,f)},$$slots:{icon:!0,default:!0}})),fe(u)}ue(mf,{data:{}},[],[],!0);var yb=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M5.46257 4.43262C7.21556 2.91688 9.5007 2 12 2C17.5228 2 22 6.47715 22 12C22 14.1361 21.3302 16.1158 20.1892 17.7406L17 12H20C20 7.58172 16.4183 4 12 4C9.84982 4 7.89777 4.84827 6.46023 6.22842L5.46257 4.43262ZM18.5374 19.5674C16.7844 21.0831 14.4993 22 12 22C6.47715 22 2 17.5228 2 12C2 9.86386 2.66979 7.88416 3.8108 6.25944L7 12H4C4 16.4183 7.58172 20 12 20C14.1502 20 16.1022 19.1517 17.5398 17.7716L18.5374 19.5674Z"></path></svg>'),wb=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),bb=Q('<div class="heading svelte-1eqcy61"><!></div> <!> <div class="heading svelte-1eqcy61"><!> <!></div> <!>',1);const xb={hash:"svelte-1eqcy61",code:".heading.svelte-1eqcy61 {display:flex;margin:10px 0;align-items:center;}.loop_handle_wrapper ::after {content:'循环体';width:100px;height:20px;background:#000;color:#fff;display:flex;justify-content:center;align-items:center;}"};function yf(e,t){de(t,!0),qe(e,xb);const n=y(t,"data",7),r=Ke(t,["$$slots","$$events","$$legacy","$$host","data"]),o=nt(),{addParameter:i}=dn();et(()=>{(!n().loopVars||n().loopVars.length===0)&&i(o,"loopVars",{name:"loopVar",nameDisabled:!0,deleteDisabled:!0})});var s={get data(){return n()},set data(a){n(a),m()}};return Wt(e,Fe({get data(){return n()}},()=>r,{icon:a=>{var l=yb();L(a,l)},handle:a=>{Qn(a,{type:"source",get position(){return be.Bottom},id:"loop_handle",style:"bottom: -12px;width: 100px",class:"loop_handle_wrapper"})},children:(a,l)=>{var u=bb(),d=ae(u),p=I(d);ze(p,{level:3,children:(b,x)=>{we();var $=Ee("循环变量");L(b,$)},$$slots:{default:!0}}),R(d);var f=V(d,2);Vt(f,{dataKeyName:"loopVars"});var g=V(f,2),h=I(g);ze(h,{level:3,children:(b,x)=>{we();var $=Ee("输出参数");L(b,$)},$$slots:{default:!0}});var v=V(h,2);Me(v,{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{i(o,"outputDefs")},children:(b,x)=>{var $=wb();L(b,$)},$$slots:{default:!0}}),R(g);var w=V(g,2);Vt(w,{noneParameterText:"无输出参数",dataKeyName:"outputDefs",useChildrenOnly:!0}),L(a,u)},$$slots:{icon:!0,handle:!0,default:!0}})),fe(s)}ue(yf,{data:{}},[],[],!0);const Cb=(e,t)=>{const n=e.target.checked;t("required",n)};var $b=Q('<div class="input-more-setting svelte-2f9bnc"><div class="input-more-item svelte-2f9bnc">数据内容： <!></div> <div class="input-more-item svelte-2f9bnc">确认方式： <!></div> <div class="input-more-item svelte-2f9bnc">数据标题： <!></div> <div class="input-more-item svelte-2f9bnc">数据描述： <!></div> <label class="input-item-inline svelte-2f9bnc"><span>是否必填：</span> <input type="checkbox"/></label> <div class="input-more-item svelte-2f9bnc"><!></div></div>'),kb=Q('<div class="input-item svelte-2f9bnc"><!></div> <div class="input-item svelte-2f9bnc"><!></div> <div class="input-item svelte-2f9bnc"><!></div>',1);const _b={hash:"svelte-2f9bnc",code:".input-item.svelte-2f9bnc {display:flex;align-items:center;}.input-item-inline.svelte-2f9bnc {display:flex;align-items:center;font-size:12px;color:#666;}.input-more-setting.svelte-2f9bnc {display:flex;flex-direction:column;gap:10px;padding:10px;background:#fff;border:1px solid #ddd;border-radius:5px;width:200px;box-shadow:0 0 10px 2px rgba(0, 0, 0, 0.1);}.input-more-setting.svelte-2f9bnc .input-more-item:where(.svelte-2f9bnc) {display:flex;flex-direction:column;gap:3px;font-size:12px;color:#666;}"};function wf(e,t){de(t,!0),qe(e,_b);const n=y(t,"parameter",7),r=y(t,"index",7),o=y(t,"dataKeyName",7),i=y(t,"useChildrenOnly",7);let s=nt(),a=Zn(s),l=T(()=>({...n(),...a?.current?.data?.[o()][r()]}));const{updateNodeData:u}=yt(),d=(_,k)=>{u(s,C=>{let N=C.data?.[o()];return N[r()]={...N[r()],[_]:k},{[o()]:N}})},p=(_,k)=>{const C=k.target.value;d(_,C)},f=_=>{const k=_.value;d("ref",k)},g=_=>{const k=_.value;d("formType",k)},h=_=>{const k=_.value;d("contentType",k)};let v;const w=()=>{u(s,_=>{let k=_.data?.[o()];return k.splice(r(),1),{[o()]:[...k]}}),v?.hide()};let b=lf(i());var x={get parameter(){return n()},set parameter(_){n(_),m()},get index(){return r()},set index(_){r(_),m()},get dataKeyName(){return o()},set dataKeyName(_){o(_),m()},get useChildrenOnly(){return i()},set useChildrenOnly(_){i(_),m()}},$=kb(),S=ae($),E=I(S);{let _=T(()=>c(l).nameDisabled===!0);ot(E,{style:"width: 100%;",get value(){return c(l).name},placeholder:"请输入参数名称",get disabled(){return c(_)},oninput:k=>p("name",k)})}R(S);var D=V(S,2),O=I(D);{var q=_=>{ot(_,{get value(){return c(l).value},placeholder:"请输入参数值",oninput:k=>p("value",k)})},K=_=>{var k=Ne(),C=ae(k);{var N=P=>{{let H=T(()=>[c(l).ref]);wt(P,{get items(){return b.current},style:"width: 100%",defaultValue:["ref"],get value(){return c(H)},expandAll:!0,onSelect:f})}};ce(C,P=>{c(l).refType!=="input"&&P(N)},!0)}L(_,k)};ce(O,_=>{c(l).refType==="fixed"?_(q):_(K,!1)})}R(D);var J=V(D,2),A=I(J);return qt(kr(A,{placement:"bottom",floating:_=>{var k=$b(),C=I(k),N=V(I(C));{let F=T(()=>c(l).contentType?[c(l).contentType]:[]);wt(N,{get items(){return xa},style:"width: 100%",defaultValue:["text"],get value(){return c(F)},onSelect:h})}R(C);var P=V(C,2),H=V(I(P));{let F=T(()=>c(l).formType?[c(l).formType]:[]);wt(H,{get items(){return h2},style:"width: 100%",defaultValue:["single"],get value(){return c(F)},onSelect:g})}R(P);var Z=V(P,2),Y=V(I(Z));We(Y,{rows:1,style:"width: 100%;",onchange:F=>{p("formLabel",F)},get value(){return c(l).formLabel}}),R(Z);var M=V(Z,2),X=V(I(M));We(X,{rows:2,style:"width: 100%;",onchange:F=>{p("formDescription",F)},get value(){return c(l).formDescription}}),R(M);var te=V(M,2),oe=V(I(te),2);wn(oe),Hs(oe,!1),oe.__change=[Cb,d],R(te);var j=V(te,2),G=I(j);Me(G,{onclick:w,children:(F,se)=>{we();var W=Ee("删除");L(F,W)},$$slots:{default:!0}}),R(j),R(k),L(_,k)},children:(_,k)=>{Yo(_,{})},$$slots:{floating:!0,default:!0}}),_=>v=_,()=>v),R(J),L(e,$),fe(x)}Mn(["change"]),ue(wf,{parameter:{},index:{},dataKeyName:{},useChildrenOnly:{}},[],[],!0);var Sb=Q('<div class="input-header svelte-1sm1mgi">参数名称</div> <div class="input-header svelte-1sm1mgi">参数值</div> <div class="input-header svelte-1sm1mgi"></div>',1),Eb=Q('<div class="none-params svelte-1sm1mgi"> </div>'),Pb=Q('<div class="input-container svelte-1sm1mgi"><!> <!></div>');const Nb={hash:"svelte-1sm1mgi",code:`.input-container.svelte-1sm1mgi {display:grid;grid-template-columns:40% 50% 10%;row-gap:5px;column-gap:3px;}.input-container.svelte-1sm1mgi .none-params:where(.svelte-1sm1mgi) {font-size:12px;background:#f8f8f8;height:40px;display:flex;justify-content:center;align-items:center;border-radius:5px;width:calc(100% - 5px);grid-column:1 / -1;
  /* 从第一列开始到最后一列结束 */}.input-container.svelte-1sm1mgi .input-header:where(.svelte-1sm1mgi) {font-size:12px;color:#666;}`};function bf(e,t){de(t,!0),qe(e,Nb);const n=y(t,"noneParameterText",7,"无确认数据"),r=y(t,"dataKeyName",7,"parameters"),o=y(t,"useChildrenOnly",7);let i=nt(),s=Zn(i),a=T(()=>[...s?.current?.data?.[r()]||[]]);var l={get noneParameterText(){return n()},set noneParameterText(g="无确认数据"){n(g),m()},get dataKeyName(){return r()},set dataKeyName(g="parameters"){r(g),m()},get useChildrenOnly(){return o()},set useChildrenOnly(g){o(g),m()}},u=Pb(),d=I(u);{var p=g=>{var h=Sb();we(4),L(g,h)};ce(d,g=>{c(a).length!==0&&g(p)})}var f=V(d,2);return Ct(f,19,()=>c(a),g=>g.id,(g,h,v)=>{wf(g,{get parameter(){return c(h)},get index(){return c(v)},get dataKeyName(){return r()},get useChildrenOnly(){return o()}})},g=>{var h=Eb(),v=I(h,!0);R(h),Se(()=>Xe(v,n())),L(g,h)}),R(u),L(e,u),fe(l)}ue(bf,{noneParameterText:{},dataKeyName:{},useChildrenOnly:{}},[],[],!0);const Da=(e,t)=>{if(e===t)return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;const n=Array.isArray(e),r=Array.isArray(t);if(n!==r)return!1;if(n&&r){if(e.length!==t.length)return!1;for(let o=0;o<e.length;o++)if(!Da(e[o],t[o]))return!1;return!0}else{const o=Object.keys(e),i=Object.keys(t);if(o.length!==i.length)return!1;for(const s of o)if(!(s in t)||!Da(e[s],t[s]))return!1;return!0}};var Tb=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M23 12L15.9289 19.0711L14.5147 17.6569L20.1716 12L14.5147 6.34317L15.9289 4.92896L23 12ZM3.82843 12L9.48528 17.6569L8.07107 19.0711L1 12L8.07107 4.92896L9.48528 6.34317L3.82843 12Z"></path></svg>'),Db=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),Ob=Q('<div class="heading svelte-15t2v24"><!> <!></div> <!> <!> <div class="setting-title svelte-15t2v24">消息内容</div> <div class="setting-item svelte-15t2v24"><!></div> <div class="heading svelte-15t2v24"><!></div> <!>',1);const Lb={hash:"svelte-15t2v24",code:".heading.svelte-15t2v24 {display:flex;margin-bottom:10px;}.setting-title.svelte-15t2v24 {font-size:12px;color:#999;margin-bottom:4px;margin-top:10px;}.setting-item.svelte-15t2v24 {display:flex;align-items:center;justify-content:space-between;margin-bottom:10px;gap:10px;}"};function xf(e,t){de(t,!0),qe(e,Lb);const n=y(t,"data",7),r=Ke(t,["$$slots","$$events","$$legacy","$$host","data"]),o=nt(),{addParameter:i}=dn(),{updateNodeData:s}=yt();et(()=>{if(n().confirms){const l=n().confirms.map(u=>({...u,nameDisabled:!0,dataTypeDisabled:!0,dataType:u.formType==="checkbox"||u.formType==="select"?"Array":"String",addChildDisabled:!0}));Da(l,n().outputDefs)||s(o,()=>({outputDefs:l}))}});var a={get data(){return n()},set data(l){n(l),m()}};return Wt(e,Fe({get data(){return n()}},()=>r,{icon:l=>{var u=Tb();L(l,u)},children:(l,u)=>{var d=Ob(),p=ae(d),f=I(p);ze(f,{level:3,children:(E,D)=>{we();var O=Ee("确认数据");L(E,O)},$$slots:{default:!0}});var g=V(f,2);Me(g,{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{i(o,"confirms")},children:(E,D)=>{var O=Db();L(E,O)},$$slots:{default:!0}}),R(p);var h=V(p,2);bf(h,{dataKeyName:"confirms",noneParameterText:"无确认数据"});var v=V(h,2);ze(v,{level:3,mt:"10px",children:(E,D)=>{we();var O=Ee("确认消息");L(E,O)},$$slots:{default:!0}});var w=V(v,4),b=I(w);{let E=T(()=>n().message||"");We(b,{rows:5,placeholder:"请输入用户需要确认的消息内容",style:"width: 100%",onchange:D=>{s(o,()=>({message:D.target.value}))},get value(){return c(E)}})}R(w);var x=V(w,2),$=I(x);ze($,{level:3,mt:"10px",children:(E,D)=>{we();var O=Ee("输出参数");L(E,O)},$$slots:{default:!0}}),R(x);var S=V(x,2);Kn(S,{placeholder:""}),L(l,d)},$$slots:{icon:!0,default:!0}})),fe(a)}ue(xf,{data:{}},[],[],!0);const Mb={startNode:rf,codeNode:pf,confirmNode:xf,llmNode:ff,templateNode:gf,httpNode:hf,knowledgeNode:vf,searchEngineNode:mf,loopNode:yf,endNode:cf};var Hb=Q("<!> ",1);function Oa(e,t){de(t,!0);const n=y(t,"icon",7),r=y(t,"title",7),o=y(t,"type",7),i=y(t,"description",7),s=y(t,"extra",7),a=u=>{if(!u.dataTransfer)return null;const d={type:o(),data:{title:r(),description:i(),...s()}};u.dataTransfer.setData("application/tinyflow",JSON.stringify(d)),u.dataTransfer.effectAllowed="move"};var l={get icon(){return n()},set icon(u){n(u),m()},get title(){return r()},set title(u){r(u),m()},get type(){return o()},set type(u){o(u),m()},get description(){return i()},set description(u){i(u),m()},get extra(){return s()},set extra(u){s(u),m()}};return Me(e,{draggable:!0,ondragstart:a,get"data-node-type"(){return o()},children:(u,d)=>{var p=Hb(),f=ae(p);Ts(f,n);var g=V(f);Se(()=>Xe(g,` ${r()??""}`)),L(u,p)},$$slots:{default:!0}}),fe(l)}ue(Oa,{icon:{},title:{},type:{},description:{},extra:{}},[],[],!0);var Vb=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M4.83582 12L11.0429 18.2071L12.4571 16.7929L7.66424 12L12.4571 7.20712L11.0429 5.79291L4.83582 12ZM10.4857 12L16.6928 18.2071L18.107 16.7929L13.3141 12L18.107 7.20712L16.6928 5.79291L10.4857 12Z"></path></svg>'),zb=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M19.1642 12L12.9571 5.79291L11.5429 7.20712L16.3358 12L11.5429 16.7929L12.9571 18.2071L19.1642 12ZM13.5143 12L7.30722 5.79291L5.89301 7.20712L10.6859 12L5.89301 16.7929L7.30722 18.2071L13.5143 12Z"></path></svg>'),Ab=Q('<div><div class="tf-toolbar-container "><div class="tf-toolbar-container-header"><!></div> <div class="tf-toolbar-container-body"><div class="tf-toolbar-container-base"></div> <div class="tf-toolbar-container-tools"></div></div></div> <!></div>');function Cf(e,t){de(t,!0);let n=De("base"),r=De("show");const o=[{icon:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22ZM12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4C7.58172 4 4 7.58172 4 12C4 16.4183 7.58172 20 12 20ZM12 15C10.3431 15 9 13.6569 9 12C9 10.3431 10.3431 9 12 9C13.6569 9 15 10.3431 15 12C15 13.6569 13.6569 15 12 15Z"></path></svg>',title:"开始节点",type:"startNode",sortNo:100,description:"开始定义输入参数"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M5.46257 4.43262C7.21556 2.91688 9.5007 2 12 2C17.5228 2 22 6.47715 22 12C22 14.1361 21.3302 16.1158 20.1892 17.7406L17 12H20C20 7.58172 16.4183 4 12 4C9.84982 4 7.89777 4.84827 6.46023 6.22842L5.46257 4.43262ZM18.5374 19.5674C16.7844 21.0831 14.4993 22 12 22C6.47715 22 2 17.5228 2 12C2 9.86386 2.66979 7.88416 3.8108 6.25944L7 12H4C4 16.4183 7.58172 20 12 20C14.1502 20 16.1022 19.1517 17.5398 17.7716L18.5374 19.5674Z"></path></svg>',title:"循环",type:"loopNode",sortNo:200,description:"用于循环执行任务"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M20.7134 7.12811L20.4668 7.69379C20.2864 8.10792 19.7136 8.10792 19.5331 7.69379L19.2866 7.12811C18.8471 6.11947 18.0555 5.31641 17.0677 4.87708L16.308 4.53922C15.8973 4.35653 15.8973 3.75881 16.308 3.57612L17.0252 3.25714C18.0384 2.80651 18.8442 1.97373 19.2761 0.930828L19.5293 0.319534C19.7058 -0.106511 20.2942 -0.106511 20.4706 0.319534L20.7238 0.930828C21.1558 1.97373 21.9616 2.80651 22.9748 3.25714L23.6919 3.57612C24.1027 3.75881 24.1027 4.35653 23.6919 4.53922L22.9323 4.87708C21.9445 5.31641 21.1529 6.11947 20.7134 7.12811ZM9 2C13.0675 2 16.426 5.03562 16.9337 8.96494L19.1842 12.5037C19.3324 12.7367 19.3025 13.0847 18.9593 13.2317L17 14.071V17C17 18.1046 16.1046 19 15 19H13.001L13 22H4L4.00025 18.3061C4.00033 17.1252 3.56351 16.0087 2.7555 15.0011C1.65707 13.6313 1 11.8924 1 10C1 5.58172 4.58172 2 9 2ZM9 4C5.68629 4 3 6.68629 3 10C3 11.3849 3.46818 12.6929 4.31578 13.7499C5.40965 15.114 6.00036 16.6672 6.00025 18.3063L6.00013 20H11.0007L11.0017 17H15V12.7519L16.5497 12.0881L15.0072 9.66262L14.9501 9.22118C14.5665 6.25141 12.0243 4 9 4ZM19.4893 16.9929L21.1535 18.1024C22.32 16.3562 23 14.2576 23 12.0001C23 11.317 22.9378 10.6486 22.8186 10L20.8756 10.5C20.9574 10.9878 21 11.489 21 12.0001C21 13.8471 20.4436 15.5642 19.4893 16.9929Z"></path></svg>',title:"大模型",type:"llmNode",sortNo:300,description:"使用大模型处理问题"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M15.5 5C13.567 5 12 6.567 12 8.5C12 10.433 13.567 12 15.5 12C17.433 12 19 10.433 19 8.5C19 6.567 17.433 5 15.5 5ZM10 8.5C10 5.46243 12.4624 3 15.5 3C18.5376 3 21 5.46243 21 8.5C21 9.6575 20.6424 10.7315 20.0317 11.6175L22.7071 14.2929L21.2929 15.7071L18.6175 13.0317C17.7315 13.6424 16.6575 14 15.5 14C12.4624 14 10 11.5376 10 8.5ZM3 4H8V6H3V4ZM3 11H8V13H3V11ZM21 18V20H3V18H21Z"></path></svg>',title:"知识库",type:"knowledgeNode",sortNo:400,description:"通过知识库获取内容"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M18.031 16.6168L22.3137 20.8995L20.8995 22.3137L16.6168 18.031C15.0769 19.263 13.124 20 11 20C6.032 20 2 15.968 2 11C2 6.032 6.032 2 11 2C15.968 2 20 6.032 20 11C20 13.124 19.263 15.0769 18.031 16.6168ZM16.0247 15.8748C17.2475 14.6146 18 12.8956 18 11C18 7.1325 14.8675 4 11 4C7.1325 4 4 7.1325 4 11C4 14.8675 7.1325 18 11 18C12.8956 18 14.6146 17.2475 15.8748 16.0247L16.0247 15.8748Z"></path></svg>',title:"搜索引擎",type:"searchEngineNode",sortNo:500,description:"通过搜索引擎搜索内容"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M6.23509 6.45329C4.85101 7.89148 4 9.84636 4 12C4 16.4183 7.58172 20 12 20C13.0808 20 14.1116 19.7857 15.0521 19.3972C15.1671 18.6467 14.9148 17.9266 14.8116 17.6746C14.582 17.115 13.8241 16.1582 12.5589 14.8308C12.2212 14.4758 12.2429 14.2035 12.3636 13.3943L12.3775 13.3029C12.4595 12.7486 12.5971 12.4209 14.4622 12.1248C15.4097 11.9746 15.6589 12.3533 16.0043 12.8777C16.0425 12.9358 16.0807 12.9928 16.1198 13.0499C16.4479 13.5297 16.691 13.6394 17.0582 13.8064C17.2227 13.881 17.428 13.9751 17.7031 14.1314C18.3551 14.504 18.3551 14.9247 18.3551 15.8472V15.9518C18.3551 16.3434 18.3168 16.6872 18.2566 16.9859C19.3478 15.6185 20 13.8854 20 12C20 8.70089 18.003 5.8682 15.1519 4.64482C14.5987 5.01813 13.8398 5.54726 13.575 5.91C13.4396 6.09538 13.2482 7.04166 12.6257 7.11976C12.4626 7.14023 12.2438 7.12589 12.012 7.11097C11.3905 7.07058 10.5402 7.01606 10.268 7.75495C10.0952 8.2232 10.0648 9.49445 10.6239 10.1543C10.7134 10.2597 10.7307 10.4547 10.6699 10.6735C10.59 10.9608 10.4286 11.1356 10.3783 11.1717C10.2819 11.1163 10.0896 10.8931 9.95938 10.7412C9.64554 10.3765 9.25405 9.92233 8.74797 9.78176C8.56395 9.73083 8.36166 9.68867 8.16548 9.64736C7.6164 9.53227 6.99443 9.40134 6.84992 9.09302C6.74442 8.8672 6.74488 8.55621 6.74529 8.22764C6.74529 7.8112 6.74529 7.34029 6.54129 6.88256C6.46246 6.70541 6.35689 6.56446 6.23509 6.45329ZM12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22Z"></path></svg>',title:"Http 请求",type:"httpNode",sortNo:600,description:"通过 HTTP 请求获取数据"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M23 12L15.9289 19.0711L14.5147 17.6569L20.1716 12L14.5147 6.34317L15.9289 4.92896L23 12ZM3.82843 12L9.48528 17.6569L8.07107 19.0711L1 12L8.07107 4.92896L9.48528 6.34317L3.82843 12Z"></path></svg>',title:"动态代码",type:"codeNode",sortNo:700,description:"动态执行代码"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M2 4C2 3.44772 2.44772 3 3 3H21C21.5523 3 22 3.44772 22 4V20C22 20.5523 21.5523 21 21 21H3C2.44772 21 2 20.5523 2 20V4ZM4 5V19H20V5H4ZM7 8H17V11H15V10H13V14H14.5V16H9.5V14H11V10H9V11H7V8Z"></path></svg>',title:"内容模板",type:"templateNode",sortNo:800,description:"通过模板引擎生成内容"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M15.3873 13.4975L17.9403 20.5117L13.2418 22.2218L10.6889 15.2076L6.79004 17.6529L8.4086 1.63318L19.9457 12.8646L15.3873 13.4975ZM15.3768 19.3163L12.6618 11.8568L15.6212 11.4459L9.98201 5.9561L9.19088 13.7863L11.7221 12.1988L14.4371 19.6583L15.3768 19.3163Z"></path></svg>',title:"用户确认",type:"confirmNode",sortNo:900,description:"确认继续或选择内容"},{icon:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M6 5.1438V16.0002H18.3391L6 5.1438ZM4 2.932C4 2.07155 5.01456 1.61285 5.66056 2.18123L21.6501 16.2494C22.3423 16.8584 21.9116 18.0002 20.9896 18.0002H6V22H4V2.932Z"></path></svg>',title:"结束节点",type:"endNode",sortNo:1e3,description:"结束定义输出参数"}],i=[{label:"基础节点",value:"base"},{label:"业务工具",value:"tools"}],s=[],a=rr(),l=a.customNodes;if(l){const b=Object.keys(l).sort((x,$)=>(l[x].sortNo||0)-(l[$].sortNo||0));for(let x of b)l[x].group==="base"?o.push({type:x,...l[x]}):s.push({icon:l[x].icon,title:l[x].title,type:x});o.sort((x,$)=>(x.sortNo||0)-($.sortNo||0))}if(a.hiddenNodes){const b=typeof a.hiddenNodes=="function"?a.hiddenNodes():a.hiddenNodes;if(Array.isArray(b)){for(let x of b)for(let $=0;$<o.length;$++)if(o[$].type===x){o.splice($,1);break}}}var u=Ab(),d=I(u),p=I(d),f=I(p);Md(f,{style:"width: 100%",get items(){return i},onChange:b=>{U(n,b.value.toString(),!0)}}),R(p);var g=V(p,2),h=I(g);Ct(h,21,()=>o,Rr,(b,x)=>{Oa(b,Fe(()=>c(x)))}),R(h);var v=V(h,2);Ct(v,21,()=>s,Rr,(b,x)=>{Oa(b,Fe(()=>c(x)))}),R(v),R(g),R(d);var w=V(d,2);Me(w,{onclick:()=>{U(r,c(r)?"":"show",!0)},children:(b,x)=>{var $=Ne(),S=ae($);{var E=O=>{var q=Vb();L(O,q)},D=O=>{var q=zb();L(O,q)};ce(S,O=>{c(r)==="show"?O(E):O(D,!1)})}L(b,$)},$$slots:{default:!0}}),R(u),Se(()=>{Mt(u,1,`tf-toolbar ${c(r)??""}`),mt(h,`display: ${c(n)==="base"?"flex":"none"}`),mt(v,`display: ${c(n)!=="base"?"flex":"none"}`)}),L(e,u),fe()}ue(Cf,{},[],[],!0);const Rb=()=>({getNode:e=>Ue.getNode(e)}),Ib=()=>({ensureParentInNodesBefore:(e,t)=>{Ue.updateNodes(n=>{let r=-1;for(let s=0;s<n.length;s++)if(n[s].id===e){r=s;break}if(r<=0)return n;let o=-1;for(let s=0;s<r;s++)if(n[s].parentId===e||n[s].id===t){o=s;break}if(o==-1)return n;const i=n[r];for(let s=r;s>o;s--)n[s]=n[s-1];return n[o]=i,n})}}),qb=()=>({getEdgesByTarget:e=>Ue.getEdges().filter(t=>t.target===e)});var Zb=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),Bb=Q('<div class="heading svelte-q0cqsa"><!> <!></div> <!>',1),Kb=Q('<div class="setting-title svelte-q0cqsa"> </div> <div class="setting-item svelte-q0cqsa"><!></div>',1),jb=Q('<div class="setting-title svelte-q0cqsa"> </div> <div class="setting-item svelte-q0cqsa"><!></div>',1),Yb=Q('<div class="setting-title svelte-q0cqsa"> </div> <div class="setting-item svelte-q0cqsa"><div class="slider-container svelte-q0cqsa"><span class="svelte-q0cqsa"> </span> <input/></div></div>',1),Xb=Q('<div class="setting-title svelte-q0cqsa"> </div> <div class="setting-item svelte-q0cqsa"><!></div>',1),Fb=Q('<div class="setting-title svelte-q0cqsa"> </div> <div class="setting-item svelte-q0cqsa"><!></div>',1),Wb=me('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path></svg>'),Gb=Q('<div class="heading svelte-q0cqsa"><!> <!></div> <!>',1),Ub=Q("<!> <!> <div></div> <!>",1);const Jb={hash:"svelte-q0cqsa",code:`.heading.svelte-q0cqsa {display:flex;align-items:center;margin-bottom:10px;}.setting-title.svelte-q0cqsa {font-size:12px;color:#999;margin-bottom:4px;margin-top:10px;}.setting-item.svelte-q0cqsa {display:flex;align-items:center;justify-content:space-between;margin-bottom:10px;gap:10px;}

    /* 新增样式 */.slider-container.svelte-q0cqsa {width:100%;display:flex;flex-direction:column;gap:4px;}.slider-container.svelte-q0cqsa span:where(.svelte-q0cqsa) {font-size:12px;color:#666;display:flex;justify-content:space-between;align-items:center;}input[type="range"].svelte-q0cqsa {width:100%;height:4px;background:#ddd;border-radius:2px;outline:none;-webkit-appearance:none;}input[type="range"].svelte-q0cqsa::-webkit-slider-thumb {-webkit-appearance:none;width:14px;height:14px;background:#007bff;border-radius:50%;cursor:pointer;}`};function $f(e,t){de(t,!0),qe(e,Jb);const n=y(t,"data",7),r=Ke(t,["$$slots","$$events","$$legacy","$$host","data"]),o=nt(),{addParameter:i}=dn(),s=yt(),{updateNodeData:a}=s,l=w=>{a(o,w)},u=(w,b)=>{l({[w]:b.target?.value})},d={...r,id:o,data:n()},p=document.createElement("div"),f=rr().customNodes[t.type];f.render?.(p,d,s);const g=f.forms;let h;et(()=>{n().expand&&h&&h.append(p)}),et(()=>{n()&&f.onUpdate?.(p,{...d,data:n()})}),et(()=>{!n().parameters&&f.parameters&&l({parameters:Xo(JSON.parse(JSON.stringify(f.parameters)))})}),et(()=>{!n().outputDefs&&f.outputDefs&&l({outputDefs:Xo(JSON.parse(JSON.stringify(f.outputDefs)))})});var v={get data(){return n()},set data(w){n(w),m()}};{const w=x=>{var $=Ne(),S=ae($);Ts(S,()=>f.icon),L(x,$)};let b=T(()=>({...n(),description:f.description}));Wt(e,Fe({get data(){return c(b)}},()=>r,{icon:w,children:(x,$)=>{var S=Ub(),E=ae(S);{var D=_=>{var k=Bb(),C=ae(k),N=I(C);ze(N,{level:3,children:(Y,M)=>{we();var X=Ee("输入参数");L(Y,X)},$$slots:{default:!0}});var P=V(N,2);{var H=Y=>{Me(Y,{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{i(o)},children:(M,X)=>{var te=Zb();L(M,te)},$$slots:{default:!0}})};ce(P,Y=>{f.parametersAddEnable!==!1&&Y(H)})}R(C);var Z=V(C,2);Vt(Z,{}),L(_,k)};ce(E,_=>{f.parametersEnable!==!1&&_(D)})}var O=V(E,2);{var q=_=>{var k=Ne(),C=ae(k);Ct(C,17,()=>g,Rr,(N,P)=>{var H=Ne(),Z=ae(H);{var Y=X=>{var te=Kb(),oe=ae(te),j=I(oe,!0);R(oe);var G=V(oe,2),F=I(G);{let se=T(()=>n()[c(P).name]||c(P).defaultValue);ot(F,Fe({get placeholder(){return c(P).placeholder},style:"width: 100%",get value(){return c(se)}},()=>c(P).attrs,{onchange:W=>{u(c(P).name,W)}}))}R(G),Se(()=>Xe(j,c(P).label)),L(X,te)},M=X=>{var te=Ne(),oe=ae(te);{var j=F=>{var se=jb(),W=ae(se),ye=I(W,!0);R(W);var xe=V(W,2),ie=I(xe);{let ee=T(()=>n()[c(P).name]||c(P).defaultValue);We(ie,Fe({rows:3,get placeholder(){return c(P).placeholder},style:"width: 100%",get value(){return c(ee)}},()=>c(P).attrs,{onchange:re=>{u(c(P).name,re)}}))}R(xe),Se(()=>Xe(ye,c(P).label)),L(F,se)},G=F=>{var se=Ne(),W=ae(se);{var ye=ie=>{var ee=Yb(),re=ae(ee),ge=I(re,!0);R(re);var he=V(re,2),le=I(he),Te=I(le),ke=I(Te);R(Te);var B=V(Te,2);wn(B);var ct=Ae=>l({[c(P).name]:parseFloat(Ae.target.value)});ut(B,()=>({class:"nodrag",type:"range",...c(P).attrs,value:n()[c(P).name]??c(P).defaultValue,oninput:ct}),void 0,void 0,"svelte-q0cqsa"),R(le),R(he),Se(()=>{Xe(ge,c(P).label),Xe(ke,`${c(P).description??""}: ${n()[c(P).name]??c(P).defaultValue??""}`)}),L(ie,ee)},xe=ie=>{var ee=Ne(),re=ae(ee);{var ge=le=>{var Te=Xb(),ke=ae(Te),B=I(ke,!0);R(ke);var ct=V(ke,2),Ae=I(ct);{let Ze=T(()=>c(P).options||[]),Re=T(()=>n()[c(P).name]?[n()[c(P).name]]:[c(P).defaultValue]);wt(Ae,{get items(){return c(Ze)},style:"width: 100%",get placeholder(){return c(P).placeholder},onSelect:dt=>{const it=dt.value;l({[c(P).name]:it})},get value(){return c(Re)}})}R(ct),Se(()=>Xe(B,c(P).label)),L(le,Te)},he=le=>{var Te=Ne(),ke=ae(Te);{var B=Ae=>{var Ze=Fb(),Re=ae(Ze),dt=I(Re,!0);R(Re);var it=V(Re,2),Pt=I(it);{let Be=T(()=>c(P).chosen?.buttonText);Ld(Pt,{style:"width: 100%",get placeholder(){return c(P).placeholder},get buttonText(){return c(Be)},onChosen:(Qe,ve,Ye)=>{c(P).chosen?.onChosen?.(l,Qe,ve,Ye)},get value(){return n()[c(P).chosen?.valueDataKey||""]},get label(){return n()[c(P).chosen?.labelDataKey||""]}})}R(it),Se(()=>Xe(dt,c(P).label)),L(Ae,Ze)},ct=Ae=>{var Ze=Ne(),Re=ae(Ze);{var dt=it=>{ze(it,Fe({level:3,mt:"10px"},()=>c(P).attrs,{children:(Pt,Be)=>{we();var Qe=Ee();Se(()=>Xe(Qe,c(P).label)),L(Pt,Qe)},$$slots:{default:!0}}))};ce(Re,it=>{c(P).type==="heading"&&it(dt)},!0)}L(Ae,Ze)};ce(ke,Ae=>{c(P).type==="chosen"?Ae(B):Ae(ct,!1)},!0)}L(le,Te)};ce(re,le=>{c(P).type==="select"?le(ge):le(he,!1)},!0)}L(ie,ee)};ce(W,ie=>{c(P).type==="slider"?ie(ye):ie(xe,!1)},!0)}L(F,se)};ce(oe,F=>{c(P).type==="textarea"?F(j):F(G,!1)},!0)}L(X,te)};ce(Z,X=>{c(P).type==="input"?X(Y):X(M,!1)})}L(N,H)}),L(_,k)};ce(O,_=>{g&&_(q)})}var K=V(O,2);qt(K,_=>h=_,()=>h);var J=V(K,2);{var A=_=>{var k=Gb(),C=ae(k),N=I(C);ze(N,{level:3,mt:"10px",children:(Y,M)=>{we();var X=Ee("输出参数");L(Y,X)},$$slots:{default:!0}});var P=V(N,2);{var H=Y=>{Me(Y,{class:"input-btn-more",style:"margin-left: auto",onclick:()=>{i(o,"outputDefs")},children:(M,X)=>{var te=Wb();L(M,te)},$$slots:{default:!0}})};ce(P,Y=>{f.outputDefsAddEnable!==!1&&Y(H)})}R(C);var Z=V(C,2);Kn(Z,{}),L(_,k)};ce(J,_=>{f.outputDefsEnable!==!1&&_(A)})}Se(()=>{mt(K,f.rootStyle||""),Mt(K,1,Hn(f.rootClass),"svelte-q0cqsa")}),L(x,S)},$$slots:{icon:!0,default:!0}}))}return fe(v)}ue($f,{data:{}},[],[],!0);const Qb=()=>({updateEdgeData:(e,t,n)=>{const r=Ue.getEdge(e);if(!r)return;const o=typeof t=="function"?t(r):t;r.data=n?.replace?o:{...r.data,...o},Ue.updateEdges(i=>i.map(s=>s.id===e?r:s))}}),ex=()=>({deleteEdge:e=>{Ue.removeEdge(e)}});var tx=Q('<div class="panel-content svelte-80qc4q"><div>边属性设置</div> <div class="setting-title svelte-80qc4q">边条件设置</div> <div class="setting-item svelte-80qc4q"><!></div> <div class="setting-item svelte-80qc4q" style="padding: 8px 0"><!> <!></div></div>'),nx=Q("<!> <!> <!> <!>",1),rx=Q('<div style="position: relative; height: 100%; width: 100%;overflow: hidden"><!> <!></div>');const ox={hash:"svelte-80qc4q",code:".panel-content.svelte-80qc4q {padding:10px;background-color:#fff;border-radius:5px;box-shadow:0 2px 4px rgba(0, 0, 0, 0.1);width:200px;border:1px solid #efefef;}.setting-title.svelte-80qc4q {margin:10px 0;font-size:12px;color:#999;}.setting-item.svelte-80qc4q {display:flex;gap:5px;align-items:center;justify-content:end;}"};function kf(e,t){de(t,!0),qe(e,ox);const n=y(t,"onInit",7),r=yt();n()(r);let o=De(!1),i=De(null);const{updateEdgeData:s}=Qb(),a=N=>{N.preventDefault(),N.dataTransfer&&(N.dataTransfer.dropEffect="move")},l=N=>{N.preventDefault();const P=r.screenToFlowPosition({x:N.clientX-250,y:N.clientY-100}),H=N.dataTransfer?.getData("application/tinyflow");if(!H)return;const Z=JSON.parse(H),Y={id:`node_${_r()}`,position:P,data:{},...Z};Ue.addNode(Y),Ue.selectNodeOnly(Y.id)},{getNode:u}=Rb(),d=N=>{const P=u(N.source),H=u(N.target);if(N.sourceHandle==="loop_handle"||P.parentId){const Z=r.getEdges();for(let Y of Z)if(Y.target===N.target){const M=u(Y.source);if(N.sourceHandle==="loop_handle"&&M.parentId!==P.id||P.parentId&&M.parentId!==P.parentId)return!1}}return!(!P.parentId&&H.parentId&&H.parentId!==P.id)},{ensureParentInNodesBefore:p}=Ib(),f=(N,P)=>{if(!P.isValid)return;const H=P.toNode;if(H.parentId)return;const Z=P.fromNode,Y=P.fromHandle,M={position:{...H.position}};if(Y.id==="loop_handle"?M.parentId=Z.id:Z.parentId&&(M.parentId=Z.parentId),M.parentId){const X=u(M.parentId);M.position={x:H.position.x-X.position.x,y:H.position.y-X.position.y},p(M.parentId,H.id),r.updateNode(H.id,M)}setTimeout(()=>{Ue.getEdges().forEach(X=>{X.target===H.id&&X.source==Z.id&&(U(o,!0),U(i,X,!0))})})},{getEdgesByTarget:g}=qb(),h=N=>{N.edges.forEach(P=>{P.id===c(i)?.id&&(U(i,null),U(o,!1));const H=u(P.target);if(H&&H.parentId){const Z=g(P.target),Y=u(H.parentId);if(Z.length===0)r.updateNode(H.id,{parentId:void 0,position:{x:H.position.x+Y.position.x,y:H.position.y+Y.position.y}});else{let M=!1;for(let X=0;X<Z.length;X++){const te=Z[X],oe=u(te.source);if(oe.parentId||oe.type==="loopNode"){M=!0;break}}M||r.updateNode(H.id,{parentId:void 0,position:{x:H.position.x+Y.position.x,y:H.position.y+Y.position.y}})}}})},{deleteEdge:v}=ex(),w=(N,P)=>{},b=N=>{},x={},$=rr().customNodes;if($)for(let N of Object.keys($))x[N]=$f;const S=rr().onDataChange;et(()=>{S?.({nodes:Ue.getNodes(),edges:Ue.getEdges(),viewport:Ue.getViewport()})});var E={get onInit(){return n()},set onInit(N){n(N),m()}},D=rx(),O=I(D),q=Ue.getNodes,K=Ue.setNodes,J=Ue.getEdges,A=Ue.setEdges,_=Ue.getViewport,k=Ue.setViewport;{let N=T(()=>({...Mb,...x})),P=T(()=>({markerEnd:{type:Do.ArrowClosed,width:20,height:20}}));md(O,{get nodeTypes(){return c(N)},get nodes(){return q()},set nodes(H){K(H)},get edges(){return J()},set edges(H){A(H)},get viewport(){return _()},set viewport(H){k(H)},class:"tinyflow-logo",ondrop:l,ondragover:a,isValidConnection:d,onconnectend:f,onconnectstart:w,onconnect:b,connectionRadius:50,onedgeclick:H=>{U(o,!0),U(i,H.edge,!0)},onbeforeconnect:H=>({...H,id:_r()}),ondelete:h,onclick:H=>{const Z=H.target;Z.classList.contains("svelte-flow__edge-interaction")||Z.classList.contains("panel-content")||Z.closest(".panel-content")||(U(o,!1),U(i,null))},get defaultEdgeOptions(){return c(P)},children:(H,Z)=>{var Y=nx(),M=ae(Y);Ed(M,{});var X=V(M,2);kd(X,{});var te=V(X,2);Nd(te,{});var oe=V(te,2);{var j=G=>{Ro(G,{children:(F,se)=>{var W=tx(),ye=V(I(W),4),xe=I(ye);{let ge=T(()=>c(i)?.data?.condition);We(xe,{rows:3,placeholder:"请输入边条件",style:"width: 100%",get value(){return c(ge)},onchange:he=>{c(i)&&s(c(i).id,{condition:he.target?.value})}})}R(ye);var ie=V(ye,2),ee=I(ie);Me(ee,{onclick:()=>{v(c(i)?.id),U(o,!1)},children:(ge,he)=>{we();var le=Ee("删除");L(ge,le)},$$slots:{default:!0}});var re=V(ee,2);Me(re,{primary:!0,onclick:()=>{U(o,!1)},children:(ge,he)=>{we();var le=Ee("保存");L(ge,le)},$$slots:{default:!0}}),R(ie),R(W),L(F,W)},$$slots:{default:!0}})};ce(oe,G=>{c(o)&&G(j)})}L(H,Y)},$$slots:{default:!0}})}var C=V(O,2);return Cf(C,{}),R(D),L(e,D),fe(E)}ue(kf,{onInit:{}},[],[],!0);function ix(e,t){de(t,!0);const n=y(t,"options",7),r=y(t,"onInit",7);let{data:o}=n();if(typeof o=="string")try{o=JSON.parse(o.trim())}catch{console.error("Invalid JSON data:",o)}Ue.init(o?.nodes||[],o?.edges||[]),Lr("tinyflow_options",n());var i={get options(){return n()},set options(s){n(s),m()},get onInit(){return r()},set onInit(s){r(s),m()}};return yd(e,{children:(s,a)=>{kf(s,{get onInit(){return r()}})},$$slots:{default:!0}}),fe(i)}customElements.define("tinyflow-component",ue(ix,{options:{},onInit:{}},[],[],!1));const sx=Jt.forwardRef((e,t)=>{const n=Jt.useRef(null),r=Jt.useRef(null);Jt.useImperativeHandle(t,()=>({getData:()=>r.current?r.current.getData():(console.warn("Tinyflow instance is not initialized"),null)}));const{data:o,style:i,className:s}=e;return Jt.useEffect(()=>{if(n.current){const a=new v2({...e,element:n.current});return r.current=a,()=>{a.destroy(),r.current=null}}},[o]),Mf.jsx("div",{ref:n,style:{height:"600px",...i},className:s})});sr.Tinyflow=sx,Object.defineProperty(sr,Symbol.toStringTag,{value:"Module"})});
//# sourceMappingURL=index.umd.js.map
