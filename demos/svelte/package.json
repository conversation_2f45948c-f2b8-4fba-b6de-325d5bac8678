{"name": "@tinyflow-ai/demo-svelte", "private": true, "version": "0.0.2", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "check": "svelte-check --tsconfig ./tsconfig.json && tsc -p tsconfig.node.json"}, "devDependencies": {"@sveltejs/vite-plugin-svelte": "^6.0.0", "@tsconfig/svelte": "^5.0.4", "autoprefixer": "^10.4.21", "svelte": "^5.35.6", "svelte-check": "^4.2.2", "tslib": "^2.7.0", "typescript": "^5.6.2", "vite": "^7.0.4"}, "dependencies": {"@tinyflow-ai/svelte": "workspace:*"}}